===========
Cookies bar
===========

**Cookies** are small text files sent to your device when you visit a website. They are processed
and stored by your browser and track user information like login details, preferences, and browsing
history. **Essential cookies** are necessary for the website to function, while **optional cookies**
are used to analyze behavior or display ads.

Data protection laws require notifying users about data collection methods and purposes.
**Cookies bar** fulfill this obligation by informing users on their first visit and allowing them to
decide whether to store all or only essential cookies on their device.

Configuration
=============

To enable the cookies bar on your website, go to :menuselection:`Website --> Configuration -->
Settings` and enable :guilabel:`Cookies Bar` in the :guilabel:`Privacy` section.

.. note::
   The :ref:`Cookies Policy <cookies-bar/policy>` page (/cookie-policy`) is automatically created
   when you enable the cookies bar.

.. _cookies-bar/customization:

Customization
=============

To adapt the display of the cookies bar, click :guilabel:`Edit`, go to the
:guilabel:`Invisible Elements` section at the bottom of the panel, and click
:guilabel:`Cookies Bar`. You can modify the :guilabel:`Layout` and :guilabel:`Size` of the
cookies bar, and enable :guilabel:`Backdrop` to gray out the page in the background when the cookies
bar is displayed on the screen.

Click anywhere in the building block to further customize the appearance of the cookies bar using
:guilabel:`Block`, :guilabel:`Column` and/or :guilabel:`Inline Text` customization options.

To edit the contents of the cookies bar (i.e., the consent message), click directly in the building
block.

.. _cookies-bar/policy:

Cookies policy
==============

When you enable the cookies bar for your website, Odoo creates the **Cookie Policy** page
(`/cookie-policy`) containing a non-exhaustive list of cookies, with their purpose and examples. To
access it, click the :guilabel:`Cookie Policy` hyperlink in the cookies bar or open the page from
:menuselection:`Website --> Site --> Pages`.

To adapt the content of the page according to your needs, click the :guilabel:`Edit` button.

.. tip::
   You could add a link to this page in your website's footer, for example.

.. seealso::
   :doc:`Pages <../pages>`
