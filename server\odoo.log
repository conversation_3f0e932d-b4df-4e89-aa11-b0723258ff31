2025-06-25 16:53:39,501 8024 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 16:53:39,509 8024 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 16:53:39,545 8024 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-25 16:53:46,799 13732 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 16:53:46,799 13732 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 16:53:46,799 13732 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\addons', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 16:53:46,799 13732 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 16:53:47,161 13732 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 16:53:47,195 13732 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 16:53:47,491 23848 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 16:53:47,502 23848 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 16:53:47,510 23848 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 16:53:47,511 23848 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 16:53:47,524 23848 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 16:53:47] "GET / HTTP/1.1" 500 - 11 0.007 0.036
2025-06-25 16:54:00,212 13732 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 16:55:06,320 23848 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 16:55:06,341 23848 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 16:55:06,353 23848 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 16:55:06,354 23848 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 16:55:06,377 23848 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 16:55:06] "GET / HTTP/1.1" 500 - 11 0.014 0.061
2025-06-25 16:57:02,055 13732 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 16:57:02,055 13732 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 16:57:02,441 13732 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-25 16:57:05,378 32456 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 16:57:05,379 32456 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 16:57:05,379 32456 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 16:57:05,379 32456 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 16:57:05,660 32456 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 16:57:05,679 32456 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 16:57:07,448 32456 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 16:57:08,475 23848 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 16:57:08,491 23848 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 16:57:08,503 23848 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 16:57:08,504 23848 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 16:57:08,524 23848 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 16:57:08] "GET / HTTP/1.1" 500 - 11 0.012 0.050
2025-06-25 17:00:02,407 32456 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:00:02,425 32456 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 17:00:02,473 32456 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 17:00:02,474 32456 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 17:00:02,514 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:00:02] "GET / HTTP/1.1" 500 - 11 0.080 0.278
2025-06-25 17:00:11,734 23772 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:00:11,735 23772 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:00:11,735 23772 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:00:11,735 23772 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:00:12,204 23772 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:00:12,225 23772 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:00:16,029 23772 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:01:43,312 32456 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:01:43,329 32456 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 17:01:43,340 32456 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 17:01:43,342 32456 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 17:01:43,362 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:01:43] "GET / HTTP/1.1" 500 - 11 0.011 0.055
2025-06-25 17:01:59,300 15040 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-06-25 17:01:59,301 15040 DEBUG ? odoo.netsvc: logger level set: "odoo.modules:DEBUG" 
2025-06-25 17:01:59,302 15040 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:01:59,302 15040 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:01:59,302 15040 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:01:59,302 15040 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:01:59,568 15040 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:01:59,589 15040 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:02:00,105 15040 DEBUG ? odoo.service.server: Setting signal handlers 
2025-06-25 17:02:00,115 15040 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:02:00,119 15040 DEBUG ? odoo.service.server: cron0 started! 
2025-06-25 17:02:00,120 15040 DEBUG ? odoo.service.server: cron1 started! 
2025-06-25 17:02:38,612 39772 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-06-25 17:02:38,612 39772 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-06-25 17:02:38,612 39772 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-06-25 17:02:38,612 39772 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-06-25 17:02:38,612 39772 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-06-25 17:02:38,613 39772 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-06-25 17:02:38,613 39772 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:02:38,613 39772 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:02:38,614 39772 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:02:38,614 39772 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:02:40,839 39772 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:02:40,857 39772 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:02:41,253 39772 DEBUG ? odoo.service.server: Setting signal handlers 
2025-06-25 17:02:41,259 39772 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:02:41,343 39772 DEBUG demo odoo.modules.registry: Multiprocess load registry signaling: [Registry: 1] [Cache default: 1] [Cache assets: 1] [Cache templates: 1] [Cache routing: 1] [Cache groups: 1] 
2025-06-25 17:02:41,348 39772 INFO demo odoo.modules.loading: init db 
2025-06-25 17:02:42,102 39772 INFO demo odoo.modules.loading: skipping reset_modules_state, ir_module_module table does not exists 
2025-06-25 17:02:42,103 39772 ERROR demo odoo.modules.registry: Failed to load registry 
2025-06-25 17:02:42,175 39772 DEBUG demo odoo.service.server: cron0 started! 
2025-06-25 17:02:42,176 39772 DEBUG demo odoo.service.server: cron1 started! 
2025-06-25 17:03:42,248 39772 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-06-25 17:03:43,326 39772 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-06-25 17:04:59,003 18044 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:04:59,004 18044 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:04:59,005 18044 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:04:59,006 18044 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:04:59,327 18044 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:04:59,341 18044 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:04:59,905 18044 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:07:02,934 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:07:02] "GET / HTTP/1.1" 303 - 1 0.007 0.101
2025-06-25 17:07:03,094 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:07:03] "GET /odoo HTTP/1.1" 303 - 2 0.011 0.129
2025-06-25 17:07:03,315 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:07:03] "GET /web/database/selector HTTP/1.1" 500 - 1 0.006 0.205
2025-06-25 17:07:34,669 32456 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:07:34] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.011 0.226
2025-06-25 17:22:34,274 32456 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 17:22:34,275 32456 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 17:22:34,560 32456 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 3 connections  
2025-06-25 17:22:37,692 14680 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:22:37,692 14680 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:22:37,692 14680 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:22:37,692 14680 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:22:37,991 14680 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:22:38,014 14680 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:22:38,670 14680 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:22:40,542 18044 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:22:40,558 18044 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 17:22:40,665 18044 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:22:41,164 18044 INFO ? odoo.modules.loading: loading 174 modules... 
