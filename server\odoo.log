2025-06-25 07:53:16,859 26900 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 07:53:16,863 26900 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 07:53:17,227 26900 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-25 07:53:19,243 25596 INFO ? odoo: Odoo version 18.0-20250519 
2025-06-25 07:53:19,243 25596 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.20250519\server\odoo.conf 
2025-06-25 07:53:19,243 25596 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.20250519\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.20250519\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.20250519\\server\\odoo\\addons'] 
2025-06-25 07:53:19,243 25596 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 07:53:19,434 25596 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.20250519\thirdparty\wkhtmltopdf.exe 
2025-06-25 07:53:19,455 25596 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 07:53:21,044 25596 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 07:53:42,034 25596 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 07:53:42,042 25596 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 07:53:42,068 25596 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 07:53:42,199 25596 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'pos_default_payment_customer', defaulting to LGPL-3 
2025-06-25 07:53:42,355 25596 INFO ? odoo.modules.loading: loading 172 modules... 
2025-06-25 07:53:46,080 25596 INFO ? odoo.modules.loading: 172 modules loaded in 3.72s, 0 queries (+0 extra) 
2025-06-25 07:53:46,470 25596 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 07:53:46,472 25596 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-25 07:53:46,482 25596 INFO ? odoo.modules.registry: Registry loaded in 4.527s 
2025-06-25 07:53:46,490 25596 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 07:53:46,599 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 07:53:47,189 25596 WARNING ecomplus odoo.modules.module: Missing `license` key in manifest for 'pos_default_payment_customer', defaulting to LGPL-3 
2025-06-25 07:53:48,877 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:48] "GET / HTTP/1.1" 200 - 263 0.376 6.550
2025-06-25 07:53:49,363 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 500 - 0 0.000 0.340
2025-06-25 07:53:49,369 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/assets/1/fb2fc64/web.assets_frontend.min.css HTTP/1.1" 500 - 0 0.000 0.458
2025-06-25 07:53:49,723 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 500 - 0 0.000 0.038
2025-06-25 07:53:49,731 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 500 - 0 0.000 0.045
2025-06-25 07:53:49,738 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 500 - 0 0.000 0.037
2025-06-25 07:53:49,968 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:49] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 500 - 0 0.000 0.020
2025-06-25 07:53:50,087 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 500 - 0 0.000 0.043
2025-06-25 07:53:50,093 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 500 - 0 0.000 0.047
2025-06-25 07:53:50,098 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 500 - 0 0.000 0.053
2025-06-25 07:53:50,289 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 500 - 0 0.000 0.013
2025-06-25 07:53:50,434 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 500 - 0 0.000 0.020
2025-06-25 07:53:50,466 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:53:50] "GET /web/image/website/1/favicon?unique=d6bf3d7 HTTP/1.1" 500 - 0 0.000 0.022
2025-06-25 07:54:21,240 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 07:54:21,248 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.008s 
2025-06-25 07:54:21,252 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 07:54:21,256 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 07:54:21,263 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 07:54:21,376 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.113s 
2025-06-25 07:54:21,378 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 07:54:21,381 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 07:54:21,388 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 07:54:21,396 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.008s 
2025-06-25 07:54:21,399 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 07:54:21,403 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 07:54:30,232 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:30] "GET /odoo HTTP/1.1" 200 - 95 0.060 3.036
2025-06-25 07:54:30,471 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:30] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 11 0.009 0.009
2025-06-25 07:54:30,503 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:30] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.008 0.009
2025-06-25 07:54:31,059 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:31] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 500 - 0 0.000 0.023
2025-06-25 07:54:31,060 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:31] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.000 0.008
2025-06-25 07:54:31,392 25596 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-25 07:54:31,597 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:31] "POST /mail/data HTTP/1.1" 200 - 44 0.044 0.023
2025-06-25 07:54:32,747 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:32] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 07:54:48,315 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:48] "POST /web/action/load HTTP/1.1" 500 - 0 0.000 0.027
2025-06-25 07:54:48,615 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:48] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.004 0.011
2025-06-25 07:54:50,869 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:50] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.016
2025-06-25 07:54:54,551 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:54:54] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.022
2025-06-25 07:55:00,847 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:55:00] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.016
2025-06-25 07:55:09,534 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:55:09] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.013
2025-06-25 07:55:22,856 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:55:22] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.016
2025-06-25 07:55:41,539 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:55:41] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.014
2025-06-25 07:56:09,845 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:56:09] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.014
2025-06-25 07:56:52,586 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:56:52] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.017
2025-06-25 07:57:56,853 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 07:57:56] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.015
2025-06-25 07:59:34,303 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:59:34] "POST /web/webclient/version_info HTTP/1.1" 200 - 2 0.002 0.463
2025-06-25 07:59:59,348 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:59:59] "GET /odoo HTTP/1.1" 200 - 24 0.011 0.024
2025-06-25 07:59:59,676 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:59:59] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.004 0.007
2025-06-25 07:59:59,999 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 07:59:59] "POST /web/action/load HTTP/1.1" 200 - 9 0.009 0.009
2025-06-25 08:00:00,138 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.016 0.050
2025-06-25 08:00:00,138 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "POST /mail/data HTTP/1.1" 200 - 26 0.028 0.040
2025-06-25 08:00:00,160 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.012 0.055
2025-06-25 08:00:00,197 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "POST /mail/data HTTP/1.1" 200 - 53 0.079 0.039
2025-06-25 08:00:00,385 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.006 0.003
2025-06-25 08:00:00,587 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.003 0.004
2025-06-25 08:00:00,907 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:00] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.000 0.005
2025-06-25 08:00:02,210 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:02] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 08:00:11,955 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:11] "POST /web/action/load HTTP/1.1" 200 - 12 0.041 0.006
2025-06-25 08:00:12,388 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:12] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 58 0.046 0.053
2025-06-25 08:00:12,539 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:12] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 08:00:12,762 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:12] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.010 0.036
2025-06-25 08:00:12,792 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:12] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.064 0.014
2025-06-25 08:00:25,317 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:25] "POST /web/action/load HTTP/1.1" 200 - 12 0.054 0.008
2025-06-25 08:00:25,788 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:25] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 65 0.093 0.059
2025-06-25 08:00:25,935 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:25] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.017 0.019
2025-06-25 08:00:26,135 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:26] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 12 0.013 0.010
2025-06-25 08:00:26,226 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:26] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.009 0.016
2025-06-25 08:00:38,233 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:38] "POST /web/dataset/call_button/pos.config/open_ui HTTP/1.1" 200 - 21 0.016 0.016
2025-06-25 08:00:38,773 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:38] "GET /pos/ui?config_id=4&from_backend=True&debug=1 HTTP/1.1" 200 - 61 0.068 0.241
2025-06-25 08:00:38,926 25596 ERROR ecomplus odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 2386, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 1913, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 1976, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\service\model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 1943, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 2191, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\addons\base\models\ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\http.py", line 740, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\addons\web\controllers\dataset.py", line 36, in call_kw
    return call_kw(request.env[model], method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\addons\point_of_sale\models\pos_session.py", line 173, in load_data
    response['pos.session'] = self._load_pos_data(response)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\addons\pos_self_order\models\pos_session.py", line 43, in _load_pos_data
    sessions = super()._load_pos_data(data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.20250519\server\odoo\addons\pos_default_payment_customer\models\pos_session.py", line 18, in _load_pos_data
    result[0]['auto_apply_default_payment'] = config.auto_apply_default_payment
    ~~~~~~^^^
KeyError: 0
2025-06-25 08:00:38,927 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:38] "POST /web/dataset/call_kw/pos.session/load_data HTTP/1.1" 200 - 19 0.013 0.017
2025-06-25 08:00:39,226 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:39] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.007 0.004
2025-06-25 08:00:39,228 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:39] "GET /web/image/website/1/favicon?unique=d6bf3d7 HTTP/1.1" 200 - 6 0.004 0.009
2025-06-25 08:00:43,925 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:43] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.006
2025-06-25 08:00:51,207 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.004 0.008
2025-06-25 08:00:51,263 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.002 0.005
2025-06-25 08:00:51,672 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.009 0.011
2025-06-25 08:00:51,681 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "POST /web/action/load HTTP/1.1" 200 - 13 0.017 0.014
2025-06-25 08:00:51,692 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.021
2025-06-25 08:00:51,700 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.011 0.022
2025-06-25 08:00:51,720 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "POST /mail/data HTTP/1.1" 200 - 26 0.049 0.019
2025-06-25 08:00:51,916 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:51] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 5 0.002 0.022
2025-06-25 08:00:52,042 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:52] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.022 0.020
2025-06-25 08:00:52,238 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:52] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 7 0.004 0.005
2025-06-25 08:00:52,313 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:52] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.008 0.012
2025-06-25 08:00:53,846 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:53] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.002 0.007
2025-06-25 08:00:55,665 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:55] "POST /web/action/load HTTP/1.1" 200 - 12 0.003 0.009
2025-06-25 08:00:56,001 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:56] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 4 0.001 0.010
2025-06-25 08:00:56,255 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:00:56,362 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.029 0.022
2025-06-25 08:00:56,368 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:00:56] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.005 0.051
2025-06-25 08:01:15,369 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:15] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.020 0.014
2025-06-25 08:01:15,742 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:15] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.009 0.049
2025-06-25 08:01:18,533 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:18] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.037 0.033
2025-06-25 08:01:18,891 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:18] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.006 0.035
2025-06-25 08:01:34,579 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['POS Default Payment & Customer'] to user admin #2 via 127.0.0.1 
2025-06-25 08:01:35,118 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:35] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 5 0.003 0.541
2025-06-25 08:01:35,480 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:35] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 17 0.017 0.020
2025-06-25 08:01:35,771 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:35] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 11 0.053 0.012
2025-06-25 08:01:38,249 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:38] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 13 0.017 0.014
2025-06-25 08:01:38,571 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['POS Default Payment & Customer'] to user admin #2 via 127.0.0.1 
2025-06-25 08:01:38,571 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-06-25 08:01:38,572 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['POS Default Payment & Customer'] to user admin #2 via 127.0.0.1 
2025-06-25 08:01:38,646 25596 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 08:01:38,651 25596 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-25 08:01:38,668 25596 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 08:01:38,669 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:01:39,532 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:01:39,544 25596 INFO ecomplus odoo.modules.loading: loading 172 modules... 
2025-06-25 08:01:39,628 25596 INFO ecomplus odoo.modules.loading: 172 modules loaded in 0.08s, 0 queries (+0 extra) 
2025-06-25 08:01:40,036 25596 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 08:01:40,169 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['POS Default Payment & Customer'] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:01:40,174 25596 INFO ecomplus odoo.addons.base.models.ir_model: Deleting ir.ui.view(3746,) 
2025-06-25 08:01:40,257 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [78157] 
2025-06-25 08:01:40,257 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [3746] 
2025-06-25 08:01:40,263 25596 INFO ecomplus odoo.addons.base.models.ir_model: Deleting ir.model.fields(14674, 14673, 14672, 14671) 
2025-06-25 08:01:40,370 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [78120, 78119, 78118, 78117] 
2025-06-25 08:01:40,371 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [14674, 14673, 14672, 14671] 
2025-06-25 08:01:40,375 25596 INFO ecomplus odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-06-25 08:01:40,378 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [78157, 78120, 78119, 78118, 78117, 78116, 78115] 
2025-06-25 08:01:40,390 25596 INFO ecomplus odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-06-25 08:01:40,407 25596 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 08:01:40,413 25596 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 08:01:40,437 25596 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 08:01:40,438 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:01:41,634 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:01:41,647 25596 INFO ecomplus odoo.modules.loading: loading 171 modules... 
2025-06-25 08:01:41,732 25596 INFO ecomplus odoo.modules.loading: 171 modules loaded in 0.09s, 0 queries (+0 extra) 
2025-06-25 08:01:42,291 25596 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 08:01:43,499 25596 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-25 08:01:43,514 25596 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-25 08:01:43,516 25596 INFO ecomplus odoo.modules.registry: Registry loaded in 3.126s 
2025-06-25 08:01:43,526 25596 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-25 08:01:43,528 25596 INFO ecomplus odoo.modules.registry: Registry loaded in 4.916s 
2025-06-25 08:01:43,529 25596 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-25 08:01:43,540 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:43] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4762 2.598 2.378
2025-06-25 08:01:43,565 25596 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 08:01:44,914 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:44] "GET /odoo HTTP/1.1" 200 - 132 0.091 1.262
2025-06-25 08:01:45,120 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.011 0.008
2025-06-25 08:01:45,433 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.006
2025-06-25 08:01:45,557 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.012 0.012
2025-06-25 08:01:45,583 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.026 0.024
2025-06-25 08:01:45,611 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "POST /mail/data HTTP/1.1" 200 - 44 0.057 0.022
2025-06-25 08:01:45,631 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "POST /mail/data HTTP/1.1" 200 - 57 0.058 0.039
2025-06-25 08:01:45,797 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:45] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.005 0.004
2025-06-25 08:01:46,014 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:46] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.004 0.008
2025-06-25 08:01:46,346 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:46] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.006
2025-06-25 08:01:47,578 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:01:47] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 08:02:01,613 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:01] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.007
2025-06-25 08:02:02,018 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:02] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 70 0.037 0.050
2025-06-25 08:02:02,226 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:02] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.016 0.016
2025-06-25 08:02:02,350 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:02] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 13 0.007 0.007
2025-06-25 08:02:02,430 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:02] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.007 0.013
2025-06-25 08:02:15,131 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:15] "POST /web/dataset/call_button/pos.config/open_ui HTTP/1.1" 200 - 21 0.009 0.018
2025-06-25 08:02:15,602 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:15] "GET /pos/ui?config_id=4&from_backend=True&debug=1 HTTP/1.1" 200 - 61 0.028 0.208
2025-06-25 08:02:16,071 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.011 0.009
2025-06-25 08:02:16,254 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "POST /web/dataset/call_kw/pos.session/load_data HTTP/1.1" 200 - 266 0.242 0.278
2025-06-25 08:02:16,393 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.004
2025-06-25 08:02:16,657 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "POST /web/dataset/call_kw/barcode.nomenclature/read HTTP/1.1" 200 - 7 0.005 0.003
2025-06-25 08:02:16,916 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "POST /web/dataset/call_kw/barcode.rule/search_read HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 08:02:16,982 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:16] "POST /web/dataset/call_kw/product.product/search_read HTTP/1.1" 200 - 5 0.003 0.006
2025-06-25 08:02:17,240 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:17] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.004 0.005
2025-06-25 08:02:21,334 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:21] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.007 0.012
2025-06-25 08:02:24,156 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.002 0.005
2025-06-25 08:02:24,349 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=5 HTTP/1.1" 304 - 6 0.001 0.151
2025-06-25 08:02:24,358 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=4 HTTP/1.1" 304 - 6 0.010 0.150
2025-06-25 08:02:24,358 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=1 HTTP/1.1" 304 - 6 0.009 0.151
2025-06-25 08:02:24,360 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=3 HTTP/1.1" 304 - 6 0.006 0.157
2025-06-25 08:02:24,361 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=7 HTTP/1.1" 304 - 6 0.006 0.158
2025-06-25 08:02:24,479 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:24] "GET /web/image?model=pos.category&field=image_128&id=2 HTTP/1.1" 304 - 6 0.003 0.003
2025-06-25 08:02:28,706 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:28] "POST /web/dataset/call_kw/product.product/get_product_info_pos HTTP/1.1" 200 - 52 0.059 0.040
2025-06-25 08:02:57,012 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #16349280 finished 
2025-06-25 08:02:57,079 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:57] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 185 0.166 0.320
2025-06-25 08:02:57,447 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:02:57] "GET /web/image?model=res.company&id=1&field=logo HTTP/1.1" 304 - 11 0.006 0.009
2025-06-25 08:03:13,251 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:03:13] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.001 0.006
2025-06-25 08:03:35,668 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #2940088 finished 
2025-06-25 08:03:35,743 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:03:35] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 113 0.095 0.165
2025-06-25 08:03:35,785 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:03:35] "GET /web/image?model=res.company&id=1&field=logo HTTP/1.1" 304 - 6 0.003 0.012
2025-06-25 08:03:50,777 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:03:50] "POST /web/dataset/call_kw/pos.order/write HTTP/1.1" 200 - 6 0.003 0.043
2025-06-25 08:03:51,037 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:03:51] "POST /web/dataset/call_kw/pos.config/notify_synchronisation HTTP/1.1" 200 - 8 0.006 0.040
2025-06-25 08:04:01,556 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:01] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.004 0.003
2025-06-25 08:04:06,712 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:06] "POST /web/dataset/call_kw/product.product/get_product_info_pos HTTP/1.1" 200 - 34 0.017 0.030
2025-06-25 08:04:15,734 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #86517682 finished 
2025-06-25 08:04:15,799 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:15] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 122 0.141 0.147
2025-06-25 08:04:22,617 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:22] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.002 0.007
2025-06-25 08:04:26,240 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:26] "POST /web/dataset/call_kw/product.product/get_product_info_pos HTTP/1.1" 200 - 34 0.014 0.026
2025-06-25 08:04:29,403 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #36018332 finished 
2025-06-25 08:04:29,471 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:29] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 58 0.076 0.090
2025-06-25 08:04:30,319 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:30] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 6 0.003 0.004
2025-06-25 08:04:32,391 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 08:04:32,401 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.009s 
2025-06-25 08:04:32,403 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 08:04:32,407 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 08:04:36,649 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #87831174 started for PoS orders references: [{'name': '/', 'uuid': '6db2d1cd-40c6-40b9-8013-8a13f2aeeafd'}] 
2025-06-25 08:04:37,281 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #87831174 order {'name': '/', 'uuid': '6db2d1cd-40c6-40b9-8013-8a13f2aeeafd'} updated pos.order #35 
2025-06-25 08:04:37,284 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #87831174 finished 
2025-06-25 08:04:37,350 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:04:37] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 131 0.149 0.559
2025-06-25 08:16:16,849 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:16:16] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 513 0.211 0.788
2025-06-25 08:16:18,343 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:16:18] "GET /pos_restaurant/static/img/plan.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:16:22,716 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:16:22] "GET /pos_restaurant/static/img/table.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:16:24,218 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 08:16:24,225 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:16:24] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 6 0.004 0.009
2025-06-25 08:16:42,747 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:16:42] "POST /web/dataset/call_kw/product.product/get_product_info_pos HTTP/1.1" 200 - 34 0.024 0.026
2025-06-25 08:19:15,575 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=3 HTTP/1.1" 304 - 6 0.008 0.426
2025-06-25 08:19:15,576 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=4 HTTP/1.1" 304 - 6 0.010 0.424
2025-06-25 08:19:15,576 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.026 0.409
2025-06-25 08:19:15,596 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=1 HTTP/1.1" 304 - 6 0.013 0.439
2025-06-25 08:19:15,597 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=5 HTTP/1.1" 304 - 6 0.016 0.438
2025-06-25 08:19:15,597 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=2 HTTP/1.1" 304 - 6 0.014 0.437
2025-06-25 08:19:15,898 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:15] "GET /web/image?model=pos.category&field=image_128&id=7 HTTP/1.1" 304 - 6 0.004 0.003
2025-06-25 08:19:24,780 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:24] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.002 0.007
2025-06-25 08:19:24,783 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:24] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.006 0.007
2025-06-25 08:19:25,127 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /web/action/load HTTP/1.1" 200 - 13 0.007 0.012
2025-06-25 08:19:25,149 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.002 0.017
2025-06-25 08:19:25,155 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.012 0.016
2025-06-25 08:19:25,171 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.013 0.033
2025-06-25 08:19:25,174 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /mail/data HTTP/1.1" 200 - 26 0.046 0.019
2025-06-25 08:19:25,385 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 5 0.003 0.017
2025-06-25 08:19:25,474 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.011 0.019
2025-06-25 08:19:25,712 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 7 0.002 0.008
2025-06-25 08:19:25,793 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:25] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.007 0.014
2025-06-25 08:19:27,317 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:27] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.004
2025-06-25 08:19:30,519 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:30] "POST /web/action/load HTTP/1.1" 200 - 14 0.040 0.014
2025-06-25 08:19:30,947 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:30] "POST /web/action/run HTTP/1.1" 200 - 17 0.032 0.083
2025-06-25 08:19:31,178 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:31] "POST /web/dataset/call_kw/project.task/get_views HTTP/1.1" 200 - 63 0.035 0.054
2025-06-25 08:19:31,287 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:31] "POST /web/dataset/call_kw/project.task/read_progress_bar HTTP/1.1" 200 - 5 0.008 0.007
2025-06-25 08:19:31,528 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:31] "POST /web/dataset/call_kw/project.task/web_read_group HTTP/1.1" 200 - 6 0.033 0.008
2025-06-25 08:19:31,563 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:31] "POST /web/dataset/call_kw/project.task/web_search_read HTTP/1.1" 200 - 14 0.015 0.017
2025-06-25 08:19:31,889 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:19:31] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:20:21,663 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 08:20:21,668 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.005s 
2025-06-25 08:20:21,671 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 08:20:21,675 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 08:20:21,682 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 08:20:21,690 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.008s 
2025-06-25 08:20:21,693 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 08:20:21,697 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 08:20:48,743 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 08:20:48,748 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.005s 
2025-06-25 08:20:48,750 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 08:20:48,753 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 08:20:48,759 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 08:20:48,764 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.005s 
2025-06-25 08:20:48,766 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 08:20:48,769 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 08:20:48,775 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 08:20:48,778 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-25 08:20:48,780 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 08:20:48,783 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 08:21:17,038 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:17] "POST /web/action/load HTTP/1.1" 200 - 12 0.008 0.011
2025-06-25 08:21:17,334 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:17] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 51 0.031 0.039
2025-06-25 08:21:17,361 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:17] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 08:21:17,697 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:17] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.027 0.025
2025-06-25 08:21:17,732 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:17] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.006 0.081
2025-06-25 08:21:20,785 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:20] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 56 0.024 0.021
2025-06-25 08:21:21,144 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.006 0.048
2025-06-25 08:21:39,705 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:39] "POST /web/action/load HTTP/1.1" 200 - 11 0.009 0.008
2025-06-25 08:21:40,053 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:40] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 14 0.010 0.008
2025-06-25 08:21:40,298 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:40] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 4 0.001 0.005
2025-06-25 08:21:41,691 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:41] "POST /web/dataset/call_kw/base.module.update/web_save HTTP/1.1" 200 - 7 0.005 0.006
2025-06-25 08:21:42,018 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-06-25 08:21:43,460 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:43] "POST /web/dataset/call_button/base.module.update/update_module HTTP/1.1" 200 - 1673 0.855 0.593
2025-06-25 08:21:48,707 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:48] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.041 0.045
2025-06-25 08:21:49,036 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.006 0.007
2025-06-25 08:21:49,356 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "GET /muk_web_appsbar/static/description/icon.png HTTP/1.1" 404 - 37 0.018 0.053
2025-06-25 08:21:49,529 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "GET /muk_web_dialog/static/description/icon.png HTTP/1.1" 404 - 37 0.107 0.062
2025-06-25 08:21:49,560 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "GET /muk_web_enterprise_theme/static/description/icon.png HTTP/1.1" 404 - 37 0.142 0.058
2025-06-25 08:21:49,571 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "GET /muk_web_chatter/static/description/icon.png HTTP/1.1" 404 - 37 0.143 0.069
2025-06-25 08:21:49,580 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:49] "GET /muk_web_colors/static/description/icon.png HTTP/1.1" 404 - 37 0.159 0.060
2025-06-25 08:21:58,057 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['MuK Backend Theme'] to user admin #2 via 127.0.0.1 
2025-06-25 08:21:58,057 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 08:21:58,057 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['MuK Backend Theme'] to user admin #2 via 127.0.0.1 
2025-06-25 08:21:58,081 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:21:58] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 13 0.006 0.025
2025-06-25 08:22:06,345 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['MuK AppsBar'] to user admin #2 via 127.0.0.1 
2025-06-25 08:22:06,345 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 08:22:06,347 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['MuK AppsBar'] to user admin #2 via 127.0.0.1 
2025-06-25 08:22:06,413 25596 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 08:22:06,420 25596 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 08:22:06,440 25596 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 08:22:06,441 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:22:07,450 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:22:07,468 25596 INFO ecomplus odoo.modules.loading: loading 171 modules... 
2025-06-25 08:22:07,614 25596 INFO ecomplus odoo.modules.loading: 171 modules loaded in 0.15s, 0 queries (+0 extra) 
2025-06-25 08:22:07,618 25596 INFO ecomplus odoo.modules.loading: loading 172 modules... 
2025-06-25 08:22:07,618 25596 INFO ecomplus odoo.modules.loading: Loading module muk_web_appsbar (22/172) 
2025-06-25 08:22:08,005 25596 INFO ecomplus odoo.modules.registry: module muk_web_appsbar: creating or updating database tables 
2025-06-25 08:22:08,433 25596 INFO ecomplus odoo.modules.loading: loading muk_web_appsbar/templates/webclient.xml 
2025-06-25 08:22:08,477 25596 INFO ecomplus odoo.modules.loading: loading muk_web_appsbar/views/res_users.xml 
2025-06-25 08:22:08,513 25596 INFO ecomplus odoo.modules.loading: loading muk_web_appsbar/views/res_config_settings.xml 
2025-06-25 08:22:08,618 25596 INFO ecomplus odoo.addons.base.models.ir_module: module muk_web_appsbar: no translation for language vi_VN 
2025-06-25 08:22:08,728 25596 INFO ecomplus odoo.modules.loading: Module muk_web_appsbar loaded in 1.11s, 165 queries (+165 other) 
2025-06-25 08:22:08,728 25596 INFO ecomplus odoo.modules.loading: 172 modules loaded in 1.11s, 165 queries (+165 extra) 
2025-06-25 08:22:08,729 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:22:08,732 25596 INFO ecomplus odoo.modules.loading: loading 172 modules... 
2025-06-25 08:22:08,732 25596 INFO ecomplus odoo.modules.loading: 172 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-25 08:22:09,457 25596 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 08:22:11,516 25596 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-25 08:22:11,528 25596 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-25 08:22:11,530 25596 INFO ecomplus odoo.modules.registry: Registry loaded in 5.135s 
2025-06-25 08:22:11,530 25596 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-25 08:22:11,541 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:11] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 3769 2.828 2.374
2025-06-25 08:22:11,569 25596 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 08:22:13,163 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:13] "GET /odoo HTTP/1.1" 200 - 135 0.084 1.515
2025-06-25 08:22:13,678 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:13] "GET /web/webclient/translations/2aa11579688a13fc27f3eac75c8f97d27c3acce2?lang=vi_VN HTTP/1.1" 200 - 2 0.014 0.100
2025-06-25 08:22:22,576 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/264c71e/web.assets_web.min.css (id:1519) 
2025-06-25 08:22:22,578 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1514] (matching /web/assets/1/_______/web.assets_web.min.css) because it was replaced with /web/assets/1/264c71e/web.assets_web.min.css 
2025-06-25 08:22:22,692 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:22] "GET /web/assets/1/264c71e/web.assets_web.min.css HTTP/1.1" 200 - 25 0.091 9.407
2025-06-25 08:22:31,418 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/5d7e3a0/web.assets_web_print.min.css (id:1520) 
2025-06-25 08:22:31,420 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1515] (matching /web/assets/1/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/1/5d7e3a0/web.assets_web_print.min.css 
2025-06-25 08:22:31,449 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:31] "GET /web/assets/1/5d7e3a0/web.assets_web_print.min.css HTTP/1.1" 200 - 19 0.064 8.364
2025-06-25 08:22:35,912 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/e06b08e/web.assets_web.min.js (id:1521) 
2025-06-25 08:22:35,913 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1516] (matching /web/assets/1/_______/web.assets_web.min.js) because it was replaced with /web/assets/1/e06b08e/web.assets_web.min.js 
2025-06-25 08:22:36,870 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:36] "GET /web/assets/1/e06b08e/web.assets_web.min.js HTTP/1.1" 200 - 16 0.117 23.215
2025-06-25 08:22:37,532 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.009
2025-06-25 08:22:37,610 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "POST /mail/data HTTP/1.1" 200 - 44 0.031 0.027
2025-06-25 08:22:37,725 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.008 0.011
2025-06-25 08:22:37,748 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.028 0.015
2025-06-25 08:22:37,753 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.022 0.022
2025-06-25 08:22:37,794 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "POST /mail/data HTTP/1.1" 200 - 53 0.051 0.038
2025-06-25 08:22:37,843 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "GET /web/static/img/default_icon_app.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 08:22:37,926 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:37] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.004 0.007
2025-06-25 08:22:38,057 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:38] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 200 - 11 0.006 0.016
2025-06-25 08:22:38,204 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:38] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.005 0.009
2025-06-25 08:22:38,531 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:38] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.005
2025-06-25 08:22:39,436 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:22:39] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.002 0.004
2025-06-25 08:23:22,425 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-25 08:23:22,479 25596 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 8 checked, 4 removed 
2025-06-25 08:23:22,487 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646] 
2025-06-25 08:23:22,553 25596 INFO ecomplus odoo.models.unlink: User #1 deleted res.config.settings records with IDs: [25] 
2025-06-25 08:23:22,563 25596 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-25 08:23:22,579 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-25 08:23:22,585 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 2 entries 
2025-06-25 08:23:22,586 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 08:23:22,593 25596 INFO ecomplus odoo.models.unlink: User #1 deleted base.module.update records with IDs: [20] 
2025-06-25 08:23:22,611 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-25 08:23:22,622 25596 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915] 
2025-06-25 08:23:22,750 25596 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-25 08:23:23,125 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.700s 
2025-06-25 08:23:23,129 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-25 08:23:23,132 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-25 08:26:00,399 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:00] "POST /web/action/load HTTP/1.1" 200 - 12 0.002 0.011
2025-06-25 08:26:00,716 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:00] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 58 0.032 0.038
2025-06-25 08:26:00,735 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:00] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:26:01,088 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:01] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 63 0.030 0.029
2025-06-25 08:26:01,095 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.008 0.058
2025-06-25 08:26:03,145 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.025 0.021
2025-06-25 08:26:03,489 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:03] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.005 0.032
2025-06-25 08:26:04,810 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:04] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.056 0.036
2025-06-25 08:26:05,153 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:05] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.007 0.009
2025-06-25 08:26:08,299 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['MuK Backend Theme'] to user admin #2 via 127.0.0.1 
2025-06-25 08:26:08,301 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 08:26:08,354 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['MuK Backend Theme'] to user admin #2 via 127.0.0.1 
2025-06-25 08:26:08,779 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:08] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 18 0.176 0.465
2025-06-25 08:26:09,253 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:09] "GET /muk_web_dialog/static/description/icon.png HTTP/1.1" 404 - 484 1.379 2.322
2025-06-25 08:26:09,254 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:09] "GET /muk_web_appsbar/static/description/icon.png HTTP/1.1" 404 - 480 1.256 2.598
2025-06-25 08:26:09,287 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:09] "GET /muk_web_chatter/static/description/icon.png HTTP/1.1" 404 - 404 1.333 2.408
2025-06-25 08:26:09,290 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:09] "GET /muk_web_enterprise_theme/static/description/icon.png HTTP/1.1" 404 - 445 1.277 2.457
2025-06-25 08:26:09,304 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:09] "GET /muk_web_colors/static/description/icon.png HTTP/1.1" 404 - 458 1.310 2.444
2025-06-25 08:26:26,067 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['MuK Chatter'] to user admin #2 via 127.0.0.1 
2025-06-25 08:26:26,067 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 08:26:26,068 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['MuK Chatter'] to user admin #2 via 127.0.0.1 
2025-06-25 08:26:26,149 25596 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 08:26:26,154 25596 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 08:26:26,175 25596 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 08:26:26,176 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:26:27,426 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:26:27,441 25596 INFO ecomplus odoo.modules.loading: loading 172 modules... 
2025-06-25 08:26:27,562 25596 INFO ecomplus odoo.modules.loading: 172 modules loaded in 0.12s, 0 queries (+0 extra) 
2025-06-25 08:26:27,566 25596 INFO ecomplus odoo.modules.loading: loading 173 modules... 
2025-06-25 08:26:27,566 25596 INFO ecomplus odoo.modules.loading: Loading module muk_web_chatter (36/173) 
2025-06-25 08:26:27,898 25596 INFO ecomplus odoo.modules.registry: module muk_web_chatter: creating or updating database tables 
2025-06-25 08:26:28,040 25596 INFO ecomplus odoo.modules.loading: loading muk_web_chatter/views/res_users.xml 
2025-06-25 08:26:28,074 25596 INFO ecomplus odoo.addons.base.models.ir_module: module muk_web_chatter: no translation for language vi_VN 
2025-06-25 08:26:28,115 25596 INFO ecomplus odoo.modules.loading: Module muk_web_chatter loaded in 0.55s, 96 queries (+96 other) 
2025-06-25 08:26:28,115 25596 INFO ecomplus odoo.modules.loading: 173 modules loaded in 0.55s, 96 queries (+96 extra) 
2025-06-25 08:26:28,116 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:26:28,118 25596 INFO ecomplus odoo.modules.loading: loading 173 modules... 
2025-06-25 08:26:28,119 25596 INFO ecomplus odoo.modules.loading: 173 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-25 08:26:28,776 25596 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 08:26:30,460 25596 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-25 08:26:30,471 25596 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-25 08:26:30,472 25596 INFO ecomplus odoo.modules.registry: Registry loaded in 4.334s 
2025-06-25 08:26:30,472 25596 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-25 08:26:30,483 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:30] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 3702 2.400 2.022
2025-06-25 08:26:30,502 25596 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 08:26:31,755 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:31] "GET /odoo HTTP/1.1" 200 - 135 0.068 1.188
2025-06-25 08:26:32,233 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:32] "GET /web/webclient/translations/8e40090e0f22b7cfbdf4b88d6ad84f83a2a2c0c6?lang=vi_VN HTTP/1.1" 200 - 2 0.012 0.062
2025-06-25 08:26:39,604 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/3df0fa8/web.assets_web.min.css (id:1522) 
2025-06-25 08:26:39,606 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1519] (matching /web/assets/1/_______/web.assets_web.min.css) because it was replaced with /web/assets/1/3df0fa8/web.assets_web.min.css 
2025-06-25 08:26:39,671 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:39] "GET /web/assets/1/3df0fa8/web.assets_web.min.css HTTP/1.1" 200 - 25 0.052 7.846
2025-06-25 08:26:49,294 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/8298630/web.assets_web_print.min.css (id:1523) 
2025-06-25 08:26:49,298 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1520] (matching /web/assets/1/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/1/8298630/web.assets_web_print.min.css 
2025-06-25 08:26:49,324 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:49] "GET /web/assets/1/8298630/web.assets_web_print.min.css HTTP/1.1" 200 - 19 0.080 9.252
2025-06-25 08:26:51,350 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/cbbca6a/web.assets_web.min.js (id:1524) 
2025-06-25 08:26:51,352 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1521] (matching /web/assets/1/_______/web.assets_web.min.js) because it was replaced with /web/assets/1/cbbca6a/web.assets_web.min.js 
2025-06-25 08:26:52,088 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:52] "GET /web/assets/1/cbbca6a/web.assets_web.min.js HTTP/1.1" 200 - 16 0.053 19.897
2025-06-25 08:26:52,667 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:52] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 11 0.005 0.008
2025-06-25 08:26:52,742 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:52] "POST /mail/data HTTP/1.1" 200 - 44 0.038 0.035
2025-06-25 08:26:52,820 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:52] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.008 0.008
2025-06-25 08:26:52,838 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:52] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.004 0.012
2025-06-25 08:26:53,166 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:53] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.002 0.006
2025-06-25 08:26:54,516 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:26:54] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 08:27:03,988 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:03] "POST /web/action/load HTTP/1.1" 200 - 8 0.016 0.021
2025-06-25 08:27:04,003 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:04] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.022 0.029
2025-06-25 08:27:04,003 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:04] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.032 0.019
2025-06-25 08:27:04,267 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:04] "POST /mail/data HTTP/1.1" 200 - 53 0.025 0.047
2025-06-25 08:27:04,406 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:04] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.002 0.009
2025-06-25 08:27:10,579 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:10] "POST /web/action/load HTTP/1.1" 200 - 12 0.005 0.006
2025-06-25 08:27:10,971 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:10] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 58 0.031 0.040
2025-06-25 08:27:11,151 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:27:11,338 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 63 0.033 0.022
2025-06-25 08:27:11,347 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:11] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.006 0.057
2025-06-25 08:27:14,772 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:14] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.029 0.021
2025-06-25 08:27:15,084 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:15] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.003 0.003
2025-06-25 08:27:16,790 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:16] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.024 0.021
2025-06-25 08:27:17,119 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:17] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.004 0.007
2025-06-25 08:27:20,918 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:20] "GET /muk_web_dialog/static/description/icon.png HTTP/1.1" 404 - 451 1.087 2.325
2025-06-25 08:27:20,922 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:20] "GET /muk_web_chatter/static/description/icon.png HTTP/1.1" 404 - 445 1.093 2.327
2025-06-25 08:27:20,943 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:20] "GET /muk_web_colors/static/description/icon.png HTTP/1.1" 404 - 397 1.316 2.124
2025-06-25 08:27:20,944 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:20] "GET /muk_web_enterprise_theme/static/description/icon.png HTTP/1.1" 404 - 442 1.138 2.295
2025-06-25 08:27:20,958 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:20] "GET /muk_web_appsbar/static/description/icon.png HTTP/1.1" 404 - 508 1.100 2.505
2025-06-25 08:27:22,083 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['MuK Dialog'] to user admin #2 via 127.0.0.1 
2025-06-25 08:27:22,083 25596 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 08:27:22,084 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['MuK Dialog'] to user admin #2 via 127.0.0.1 
2025-06-25 08:27:22,178 25596 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 08:27:22,184 25596 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 08:27:22,202 25596 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 08:27:22,203 25596 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 08:27:23,005 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:27:23,017 25596 INFO ecomplus odoo.modules.loading: loading 173 modules... 
2025-06-25 08:27:23,240 25596 INFO ecomplus odoo.modules.loading: 173 modules loaded in 0.22s, 0 queries (+0 extra) 
2025-06-25 08:27:23,243 25596 INFO ecomplus odoo.modules.loading: loading 174 modules... 
2025-06-25 08:27:23,244 25596 INFO ecomplus odoo.modules.loading: Loading module muk_web_dialog (12/174) 
2025-06-25 08:27:23,512 25596 INFO ecomplus odoo.modules.registry: module muk_web_dialog: creating or updating database tables 
2025-06-25 08:27:23,648 25596 INFO ecomplus odoo.modules.loading: loading muk_web_dialog/views/res_users.xml 
2025-06-25 08:27:23,680 25596 INFO ecomplus odoo.addons.base.models.ir_module: module muk_web_dialog: no translation for language vi_VN 
2025-06-25 08:27:23,718 25596 INFO ecomplus odoo.modules.loading: Module muk_web_dialog loaded in 0.47s, 96 queries (+96 other) 
2025-06-25 08:27:23,718 25596 INFO ecomplus odoo.modules.loading: 174 modules loaded in 0.47s, 96 queries (+96 extra) 
2025-06-25 08:27:23,721 25596 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 08:27:23,722 25596 INFO ecomplus odoo.modules.loading: loading 174 modules... 
2025-06-25 08:27:23,723 25596 INFO ecomplus odoo.modules.loading: 174 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-25 08:27:24,153 25596 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 08:27:25,603 25596 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-25 08:27:25,647 25596 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-25 08:27:25,648 25596 INFO ecomplus odoo.modules.registry: Registry loaded in 3.478s 
2025-06-25 08:27:25,649 25596 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-25 08:27:25,655 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:25] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 3707 1.852 1.726
2025-06-25 08:27:25,980 25596 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 08:27:27,381 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:27] "GET /odoo HTTP/1.1" 200 - 135 0.062 1.342
2025-06-25 08:27:27,929 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:27] "GET /web/webclient/translations/2b7fe25a728be8ce37e4496c0fd51336747aedcc?lang=vi_VN HTTP/1.1" 200 - 2 0.135 0.070
2025-06-25 08:27:34,510 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/5f105f5/web.assets_web.min.css (id:1525) 
2025-06-25 08:27:34,511 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1522] (matching /web/assets/1/_______/web.assets_web.min.css) because it was replaced with /web/assets/1/5f105f5/web.assets_web.min.css 
2025-06-25 08:27:34,712 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:34] "GET /web/assets/1/5f105f5/web.assets_web.min.css HTTP/1.1" 200 - 25 0.064 7.243
2025-06-25 08:27:43,699 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/300294e/web.assets_web_print.min.css (id:1526) 
2025-06-25 08:27:43,701 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1523] (matching /web/assets/1/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/1/300294e/web.assets_web_print.min.css 
2025-06-25 08:27:43,761 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:43] "GET /web/assets/1/300294e/web.assets_web_print.min.css HTTP/1.1" 200 - 19 0.087 8.591
2025-06-25 08:27:44,911 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/3ec73ac/web.assets_web.min.js (id:1527) 
2025-06-25 08:27:44,912 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1524] (matching /web/assets/1/_______/web.assets_web.min.js) because it was replaced with /web/assets/1/3ec73ac/web.assets_web.min.js 
2025-06-25 08:27:45,570 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:45] "GET /web/assets/1/3ec73ac/web.assets_web.min.js HTTP/1.1" 200 - 16 0.012 18.149
2025-06-25 08:27:46,135 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.008
2025-06-25 08:27:46,186 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "POST /mail/data HTTP/1.1" 200 - 44 0.022 0.026
2025-06-25 08:27:46,243 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.003 0.009
2025-06-25 08:27:46,272 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.017 0.008
2025-06-25 08:27:46,279 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.015 0.016
2025-06-25 08:27:46,310 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "POST /mail/data HTTP/1.1" 200 - 53 0.046 0.033
2025-06-25 08:27:46,450 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.003 0.004
2025-06-25 08:27:46,503 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.006 0.007
2025-06-25 08:27:46,657 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.006 0.009
2025-06-25 08:27:46,983 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:46] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.002 0.004
2025-06-25 08:27:47,955 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:47] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 08:27:50,151 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:50] "POST /web/action/load HTTP/1.1" 200 - 14 0.005 0.008
2025-06-25 08:27:50,479 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:50] "POST /web/dataset/call_kw/calendar.event/get_views HTTP/1.1" 200 - 61 0.027 0.054
2025-06-25 08:27:50,498 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:50] "POST /web/dataset/call_kw/calendar.event/has_access HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:27:50,846 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:50] "POST /web/dataset/call_kw/calendar.event/get_unusual_days HTTP/1.1" 200 - 9 0.024 0.009
2025-06-25 08:27:51,087 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/calendar.filters/search_read HTTP/1.1" 200 - 5 0.008 0.004
2025-06-25 08:27:51,171 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/calendar.event/search_read HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 08:27:51,411 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/res.partner/get_attendee_detail HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 08:27:51,569 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/res.partner/get_working_hours_for_all_attendees HTTP/1.1" 200 - 26 0.056 0.030
2025-06-25 08:27:51,735 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /calendar/check_credentials HTTP/1.1" 200 - 3 0.001 0.002
2025-06-25 08:27:51,891 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/res.users/check_synchronization_status HTTP/1.1" 200 - 3 0.002 0.006
2025-06-25 08:27:51,894 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "POST /web/dataset/call_kw/calendar.event/get_default_duration HTTP/1.1" 200 - 7 0.006 0.004
2025-06-25 08:27:51,983 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:51] "GET /web/bundle/web.fullcalendar_lib?lang=vi_VN&debug=1 HTTP/1.1" 200 - 7 0.005 0.008
2025-06-25 08:27:52,298 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Found a similar attachment for /web/assets/1/b17a1c8/web.fullcalendar_lib.min.js, copying from /web/assets/b17a1c8/web.fullcalendar_lib.min.js 
2025-06-25 08:27:52,409 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:52] "GET /web/assets/1/b17a1c8/web.fullcalendar_lib.min.js HTTP/1.1" 200 - 12 0.007 0.111
2025-06-25 08:27:52,583 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:52] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 9 0.007 0.019
2025-06-25 08:27:54,162 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:54] "POST /web/action/load HTTP/1.1" 200 - 11 0.006 0.008
2025-06-25 08:27:54,567 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:54] "POST /web/dataset/call_kw/ket_noi_tmdt.shop/get_views HTTP/1.1" 200 - 61 0.036 0.038
2025-06-25 08:27:54,744 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:54] "POST /web/dataset/call_kw/ket_noi_tmdt.shop/web_search_read HTTP/1.1" 200 - 5 0.006 0.002
2025-06-25 08:27:54,896 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:54] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.003
2025-06-25 08:27:55,939 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-25 08:27:55,992 25596 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 9 checked, 6 removed 
2025-06-25 08:27:56,051 25596 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-25 08:27:56,060 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-25 08:27:56,067 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 08:27:56,069 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 08:27:56,084 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-25 08:27:56,094 25596 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [916, 917, 918, 919] 
2025-06-25 08:27:56,252 25596 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-25 08:27:56,576 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.637s 
2025-06-25 08:27:56,579 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-25 08:27:56,582 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-25 08:27:57,143 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:57] "POST /web/action/load HTTP/1.1" 200 - 13 0.004 0.010
2025-06-25 08:27:57,627 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:57] "POST /web/dataset/call_kw/res.partner/get_views HTTP/1.1" 200 - 80 0.048 0.113
2025-06-25 08:27:57,790 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:57] "POST /web/dataset/call_kw/res.partner/web_search_read HTTP/1.1" 200 - 37 0.036 0.029
2025-06-25 08:27:57,960 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:57] "GET /web/image/res.partner/7/avatar_128?unique=1749815317000 HTTP/1.1" 200 - 7 0.003 0.017
2025-06-25 08:27:58,193 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:58] "GET /web/image/res.partner/1/avatar_128?unique=1750739322000 HTTP/1.1" 200 - 7 0.011 0.014
2025-06-25 08:27:58,196 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:58] "GET /web/image/res.partner/8/avatar_128?unique=1749837389000 HTTP/1.1" 200 - 8 0.012 0.015
2025-06-25 08:27:58,201 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:58] "GET /web/image/res.partner/9/avatar_128?unique=1749873682000 HTTP/1.1" 200 - 7 0.015 0.017
2025-06-25 08:27:59,878 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:27:59] "POST /web/action/load HTTP/1.1" 200 - 14 0.008 0.079
2025-06-25 08:28:00,426 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:28:00] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 138 0.068 0.155
2025-06-25 08:28:00,532 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:28:00] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 19 0.030 0.040
2025-06-25 08:28:00,993 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:28:00] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.006 0.009
2025-06-25 08:28:57,611 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-25 08:28:57,662 25596 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-06-25 08:28:57,722 25596 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-25 08:28:57,729 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-25 08:28:57,735 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 08:28:57,737 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 08:28:57,750 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-25 08:28:57,757 25596 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [920, 921, 922, 923] 
2025-06-25 08:28:57,904 25596 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-25 08:28:58,226 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.615s 
2025-06-25 08:28:58,229 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-25 08:28:58,235 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-25 08:29:23,286 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 08:29:23,290 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.003s 
2025-06-25 08:29:23,293 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 08:29:23,297 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 08:29:23,305 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 08:29:23,312 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.008s 
2025-06-25 08:29:23,317 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 08:29:23,324 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 08:50:10,748 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:50:10] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.005 0.138
2025-06-25 08:50:12,048 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:50:12] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.005 0.155
2025-06-25 08:50:12,276 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 08:50:12,294 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.016s 
2025-06-25 08:50:12,300 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 08:50:12,308 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 08:53:18,694 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:18] "POST /web/action/load HTTP/1.1" 200 - 13 0.053 0.010
2025-06-25 08:53:18,988 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:18] "POST /web/dataset/call_kw/project.project/get_views HTTP/1.1" 200 - 59 0.056 0.058
2025-06-25 08:53:19,062 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "POST /web/dataset/call_kw/project.project/web_search_read HTTP/1.1" 200 - 10 0.014 0.006
2025-06-25 08:53:19,308 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /web/static/img/smiling_face.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:53:19,481 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /web/image/res.users/1/avatar_128 HTTP/1.1" 200 - 10 0.008 0.016
2025-06-25 08:53:19,510 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /web/image/res.users/4/avatar_128 HTTP/1.1" 200 - 9 0.013 0.023
2025-06-25 08:53:19,526 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /web/image/res.users/5/avatar_128 HTTP/1.1" 200 - 9 0.019 0.030
2025-06-25 08:53:19,527 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /web/image/res.users/3/avatar_128 HTTP/1.1" 200 - 9 0.030 0.023
2025-06-25 08:53:20,774 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:20] "POST /web/action/load HTTP/1.1" 200 - 8 0.007 0.006
2025-06-25 08:53:21,185 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.005 0.076
2025-06-25 08:53:21,185 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 5 0.006 0.074
2025-06-25 08:53:21,190 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.081
2025-06-25 08:53:21,191 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.005 0.078
2025-06-25 08:53:21,191 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.009 0.076
2025-06-25 08:53:21,366 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:21] "GET /website/force/1?path=/ HTTP/1.1" 303 - 10 0.009 0.004
2025-06-25 08:53:22,335 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:22] "GET /website/iframefallback HTTP/1.1" 200 - 50 0.038 0.789
2025-06-25 08:53:23,317 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/e1a2422/website.assets_wysiwyg.min.css (id:1529) 
2025-06-25 08:53:23,319 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1484] (matching /web/assets/1/_______/website.assets_wysiwyg.min.css) because it was replaced with /web/assets/1/e1a2422/website.assets_wysiwyg.min.css 
2025-06-25 08:53:23,393 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:23] "GET /web/assets/1/e1a2422/website.assets_wysiwyg.min.css HTTP/1.1" 200 - 19 0.070 0.668
2025-06-25 08:53:30,387 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:30] "GET /website/static/src/img/phone.png HTTP/1.1" 200 - 0 0.000 0.052
2025-06-25 08:53:30,440 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:30] "GET / HTTP/1.1" 200 - 507 7.505 1.426
2025-06-25 08:53:30,442 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/74b47e3/web.assets_frontend.min.css (id:1530) 
2025-06-25 08:53:30,443 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1517] (matching /web/assets/1/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/1/74b47e3/web.assets_frontend.min.css 
2025-06-25 08:53:30,475 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:30] "GET /web/assets/1/74b47e3/web.assets_frontend.min.css HTTP/1.1" 200 - 19 0.047 8.085
2025-06-25 08:53:30,789 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:30] "GET /web/assets/1/bbc4610/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 5 0.003 0.029
2025-06-25 08:53:30,873 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:30] "GET /web/image/website/1/logo/My%20Website?unique=d6bf3d7 HTTP/1.1" 200 - 5 0.004 0.004
2025-06-25 08:53:31,416 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:31] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 23 0.007 0.131
2025-06-25 08:53:32,585 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/cbaa45e/website.assets_all_wysiwyg.min.css (id:1531) 
2025-06-25 08:53:32,586 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1485] (matching /web/assets/1/_______/website.assets_all_wysiwyg.min.css) because it was replaced with /web/assets/1/cbaa45e/website.assets_all_wysiwyg.min.css 
2025-06-25 08:53:32,608 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:32] "GET /web/assets/1/cbaa45e/website.assets_all_wysiwyg.min.css HTTP/1.1" 200 - 19 0.017 1.050
2025-06-25 08:53:32,769 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:32] "GET /web/assets/1/9065123/website.assets_all_wysiwyg.min.js HTTP/1.1" 200 - 5 0.003 0.231
2025-06-25 08:53:37,300 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:37] "GET /contactus HTTP/1.1" 200 - 42 0.042 0.071
2025-06-25 08:53:37,665 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:37] "GET /web/image/website.s_parallax_default_image HTTP/1.1" 200 - 9 0.006 0.008
2025-06-25 08:53:37,925 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:37] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 6 0.004 0.009
2025-06-25 08:53:38,070 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:38] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 08:53:43,222 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:43] "GET /web/bundle/web._assets_jquery?lang=vi_VN&debug=1 HTTP/1.1" 200 - 7 0.004 0.007
2025-06-25 08:53:43,615 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/533613d/web._assets_jquery.min.js (id:1532) 
2025-06-25 08:53:43,672 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:43] "GET /web/assets/1/533613d/web._assets_jquery.min.js HTTP/1.1" 200 - 11 0.005 0.138
2025-06-25 08:53:43,939 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:43] "GET /web/bundle/website.backend_assets_all_wysiwyg?lang=vi_VN&debug=1 HTTP/1.1" 200 - 24 0.012 0.134
2025-06-25 08:53:44,911 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/549af65/website.backend_assets_all_wysiwyg.min.css (id:1533) 
2025-06-25 08:53:44,941 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:44] "GET /web/assets/1/549af65/website.backend_assets_all_wysiwyg.min.css HTTP/1.1" 200 - 17 0.010 0.935
2025-06-25 08:53:47,365 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/f9b45d2/website.backend_assets_all_wysiwyg.min.js (id:1534) 
2025-06-25 08:53:47,557 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:47] "GET /web/assets/1/f9b45d2/website.backend_assets_all_wysiwyg.min.js HTTP/1.1" 200 - 11 0.008 2.668
2025-06-25 08:53:47,631 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 08:53:47,844 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:47] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 243 0.102 0.117
2025-06-25 08:53:47,957 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:47] "POST /web/dataset/call_kw/res.lang/get_installed HTTP/1.1" 200 - 3 0.002 0.013
2025-06-25 08:53:47,973 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:47] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 9 0.009 0.023
2025-06-25 08:53:50,274 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 553 0.364 1.480
2025-06-25 08:53:50,646 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /web/static/img/openhand.cur HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 08:53:50,952 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_blog_posts.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:53:50,953 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_image.svg HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 08:53:50,960 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_event_upcoming_snippet.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:53:50,966 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_alert.svg HTTP/1.1" 200 - 0 0.000 0.021
2025-06-25 08:53:50,967 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_share.svg HTTP/1.1" 200 - 0 0.000 0.024
2025-06-25 08:53:50,973 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:50] "GET /website/static/src/img/snippets_thumbs/s_rating.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:53:51,272 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_accordion.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:53:51,274 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_card.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:53:51,284 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_button.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:53:51,284 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_text_block.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:53:51,295 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_video.svg HTTP/1.1" 200 - 0 0.000 0.021
2025-06-25 08:53:51,295 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_website_form.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:53:51,597 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_debug_group.png HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 08:53:51,597 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_dynamic_products.svg HTTP/1.1" 200 - 0 0.000 0.008
2025-06-25 08:53:51,607 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_hr.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:53:51,607 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_instagram_page.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:53:51,619 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_cover.svg HTTP/1.1" 200 - 0 0.000 0.019
2025-06-25 08:53:51,619 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_three_columns.svg HTTP/1.1" 200 - 0 0.000 0.019
2025-06-25 08:53:51,918 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_social_media.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:53:51,918 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_text_image.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:53:51,930 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_facebook_page.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:53:51,934 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_picture.svg HTTP/1.1" 200 - 0 0.000 0.022
2025-06-25 08:53:51,935 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_company_team.svg HTTP/1.1" 200 - 0 0.000 0.022
2025-06-25 08:53:51,959 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:51] "GET /website/static/src/img/snippets_thumbs/s_searchbar_inline.svg HTTP/1.1" 200 - 0 0.000 0.033
2025-06-25 08:53:52,264 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_badge.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:53:52,266 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_cta_badge.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:53:52,282 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_text_highlight.svg HTTP/1.1" 200 - 0 0.000 0.050
2025-06-25 08:53:52,283 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_chart.svg HTTP/1.1" 200 - 0 0.000 0.036
2025-06-25 08:53:52,284 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_progress_bar.svg HTTP/1.1" 200 - 0 0.000 0.037
2025-06-25 08:53:52,284 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_newsletter_subscribe_form.svg HTTP/1.1" 200 - 0 0.000 0.053
2025-06-25 08:53:52,610 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_countdown.svg HTTP/1.1" 200 - 0 0.000 0.041
2025-06-25 08:53:52,610 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_blockquote.svg HTTP/1.1" 200 - 0 0.000 0.042
2025-06-25 08:53:52,615 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_map.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:53:52,617 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_embed_code.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:53:52,617 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_google_map.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:53:52,622 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_donation_button.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:53:52,925 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_add_to_cart.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:53:52,925 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:52] "GET /website/static/src/img/snippets_thumbs/s_group.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:53:56,706 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:56] "GET /website/static/src/img/snippets_options/header_extra_element_text.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:53:57,001 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_extra_element_cta.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:53:57,003 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/shadow_in.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:53:57,003 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_extra_element_logo.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:53:57,011 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/shadow_out.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:53:57,012 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_extra_element_social.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:53:57,021 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_default.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:53:57,359 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_boxed.svg HTTP/1.1" 200 - 0 0.000 0.038
2025-06-25 08:53:57,360 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_hamburger.svg HTTP/1.1" 200 - 0 0.000 0.040
2025-06-25 08:53:57,360 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_stretch.svg HTTP/1.1" 200 - 0 0.000 0.039
2025-06-25 08:53:57,370 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_search.svg HTTP/1.1" 200 - 0 0.000 0.045
2025-06-25 08:53:57,370 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_vertical.svg HTTP/1.1" 200 - 0 0.000 0.048
2025-06-25 08:53:57,370 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_sales_one.svg HTTP/1.1" 200 - 0 0.000 0.035
2025-06-25 08:53:57,713 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_effect_scroll.png HTTP/1.1" 200 - 0 0.000 0.030
2025-06-25 08:53:57,713 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_effect_standard.png HTTP/1.1" 200 - 0 0.000 0.032
2025-06-25 08:53:57,715 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_sidebar.svg HTTP/1.1" 200 - 0 0.000 0.036
2025-06-25 08:53:57,715 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_sales_three.svg HTTP/1.1" 200 - 0 0.000 0.037
2025-06-25 08:53:57,725 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_sales_two.svg HTTP/1.1" 200 - 0 0.000 0.049
2025-06-25 08:53:57,725 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:57] "GET /website/static/src/img/snippets_options/header_template_sales_four.svg HTTP/1.1" 200 - 0 0.000 0.048
2025-06-25 08:53:58,078 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 12 0.035 0.012
2025-06-25 08:53:58,096 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 12 0.052 0.015
2025-06-25 08:53:58,112 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.024 0.018
2025-06-25 08:53:58,121 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.026 0.015
2025-06-25 08:53:58,124 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.031
2025-06-25 08:53:58,132 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "GET / HTTP/1.1" 200 - 34 0.048 0.056
2025-06-25 08:53:58,400 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "GET /@/ HTTP/1.1" 303 - 4 0.001 0.004
2025-06-25 08:53:58,410 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 08:53:58,446 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.009
2025-06-25 08:53:58,447 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.011
2025-06-25 08:53:58,465 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.011
2025-06-25 08:53:58,484 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "GET /odoo/action-website.website_preview?path=/ HTTP/1.1" 200 - 26 0.024 0.032
2025-06-25 08:53:58,713 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "GET /web/assets/1/5f105f5/web.assets_web.min.css HTTP/1.1" 200 - 5 0.002 0.005
2025-06-25 08:53:58,724 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.006
2025-06-25 08:53:58,763 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.010
2025-06-25 08:53:58,763 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.005
2025-06-25 08:53:58,788 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.002 0.006
2025-06-25 08:53:58,799 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:58] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.003
2025-06-25 08:53:59,036 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.006
2025-06-25 08:53:59,041 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /website/static/src/img/snippets_options/header_effect_fixed.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:53:59,074 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /website/static/src/img/snippets_options/header_effect_disappears.png HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:53:59,074 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /website/static/src/img/snippets_options/header_effect_fade_out.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:53:59,104 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/action/load HTTP/1.1" 200 - 10 0.006 0.005
2025-06-25 08:53:59,392 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /website/get_languages HTTP/1.1" 200 - 7 0.005 0.007
2025-06-25 08:53:59,402 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.009 0.011
2025-06-25 08:53:59,409 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.002 0.010
2025-06-25 08:53:59,413 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 4 0.004 0.009
2025-06-25 08:53:59,418 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.001 0.010
2025-06-25 08:53:59,445 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /mail/data HTTP/1.1" 200 - 26 0.037 0.027
2025-06-25 08:53:59,709 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:53:59,715 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 08:53:59,737 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 6 0.004 0.013
2025-06-25 08:53:59,744 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.013 0.012
2025-06-25 08:53:59,748 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /web/assets/1/300294e/web.assets_web_print.min.css HTTP/1.1" 200 - 5 0.003 0.012
2025-06-25 08:54:00,048 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:00] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.003 0.004
2025-06-25 08:54:00,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:00] "GET /website/iframefallback HTTP/1.1" 200 - 13 0.004 0.010
2025-06-25 08:54:00,436 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:00] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.010 0.016
2025-06-25 08:54:00,470 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:00] "GET / HTTP/1.1" 200 - 34 0.023 0.087
2025-06-25 08:54:00,951 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:00] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:54:01,352 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:01] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 08:54:03,831 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:03] "GET /web/bundle/web._assets_jquery?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.001 0.003
2025-06-25 08:54:04,092 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:04] "GET /web/bundle/website.backend_assets_all_wysiwyg?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 08:54:04,351 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:04] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.093 0.108
2025-06-25 08:54:04,448 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:04] "POST /web/dataset/call_kw/res.lang/get_installed HTTP/1.1" 200 - 3 0.001 0.006
2025-06-25 08:54:04,452 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:04] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 6 0.004 0.006
2025-06-25 08:54:04,639 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:04] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 16 0.010 0.064
2025-06-25 08:54:09,516 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /website/static/src/img/snippets_options/content_width_full.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:09,517 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /website/static/src/img/snippets_options/desktop_invisible.svg HTTP/1.1" 200 - 0 0.000 0.033
2025-06-25 08:54:09,518 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /website/static/src/img/snippets_options/content_width_small.svg HTTP/1.1" 200 - 0 0.000 0.020
2025-06-25 08:54:09,525 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /website/static/src/img/snippets_options/mobile_invisible.svg HTTP/1.1" 200 - 0 0.000 0.041
2025-06-25 08:54:09,526 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /web_editor/static/src/img/snippets_options/bg_shape.svg HTTP/1.1" 200 - 0 0.000 0.044
2025-06-25 08:54:09,527 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /website/static/src/img/snippets_options/content_width_normal.svg HTTP/1.1" 200 - 0 0.000 0.028
2025-06-25 08:54:09,831 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "POST /web/dataset/call_kw/utm.source/fields_get HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 08:54:09,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "POST /web/dataset/call_kw/utm.medium/fields_get HTTP/1.1" 200 - 3 0.001 0.008
2025-06-25 08:54:09,834 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:09] "POST /web/dataset/call_kw/utm.campaign/fields_get HTTP/1.1" 200 - 3 0.002 0.007
2025-06-25 08:54:10,157 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 5 0.002 0.009
2025-06-25 08:54:10,161 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 5 0.009 0.005
2025-06-25 08:54:10,162 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 5 0.007 0.007
2025-06-25 08:54:10,472 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 08:54:10,506 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.034 0.004
2025-06-25 08:54:10,890 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:10] "POST /web_editor/get_image_info HTTP/1.1" 200 - 7 0.003 0.003
2025-06-25 08:54:11,148 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web/static/img/transparent.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:11,217 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_square.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:54:11,219 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_cornered_triangle.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:54:11,228 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_triangle.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:11,228 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_shuriken.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:11,228 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_diamond.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:11,462 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_pentagon.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:11,554 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_hexagon.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:11,554 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_heptagon.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:11,555 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_star.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:11,557 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_star_16pin.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:11,560 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_star_8pin.svg HTTP/1.1" 200 - 0 0.000 0.023
2025-06-25 08:54:11,794 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_slanted.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:11,900 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_gem.svg HTTP/1.1" 200 - 0 0.000 0.020
2025-06-25 08:54:11,900 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_tear.svg HTTP/1.1" 200 - 0 0.000 0.021
2025-06-25 08:54:11,900 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_tetris.svg HTTP/1.1" 200 - 0 0.000 0.023
2025-06-25 08:54:11,901 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_emerald.svg HTTP/1.1" 200 - 0 0.000 0.024
2025-06-25 08:54:11,903 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:11] "GET /web_editor/static/image_shapes/geometric/geo_kayak.svg HTTP/1.1" 200 - 0 0.000 0.025
2025-06-25 08:54:12,114 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_sonar.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:12,213 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_door.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:12,262 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_2.svg HTTP/1.1" 200 - 0 0.000 0.046
2025-06-25 08:54:12,262 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_1.svg HTTP/1.1" 200 - 0 0.000 0.046
2025-06-25 08:54:12,271 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_3.svg HTTP/1.1" 200 - 0 0.000 0.053
2025-06-25 08:54:12,272 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_4.svg HTTP/1.1" 200 - 0 0.000 0.051
2025-06-25 08:54:12,435 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_5.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:12,545 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric/geo_square_6.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:12,584 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_shuriken.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:12,592 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_circle.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:54:12,593 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_square.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:12,594 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_diamond.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:12,752 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_triangle.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:12,855 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_pentagon.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:12,903 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_heptagon.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:12,904 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_hexagon.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:54:12,908 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_star_7pin.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:54:12,908 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:12] "GET /web_editor/static/image_shapes/geometric_round/geo_round_star.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:54:13,072 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_star_8pin.svg HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 08:54:13,165 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_star_16pin.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:13,210 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_emerald.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:13,218 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_pill.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:13,224 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_tear.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:13,225 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_lemon.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:13,387 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_gem.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:13,486 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_cornered.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:13,538 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_sonar.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:13,538 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_door.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:13,547 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_clover.svg HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 08:54:13,556 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_bread.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:13,705 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_square_1.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:13,807 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_square_2.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:13,856 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_blob_medium.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:13,863 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/panel/panel_duo.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:13,867 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_blob_soft.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:13,868 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:13] "GET /web_editor/static/image_shapes/geometric_round/geo_round_blob_hard.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:54:14,029 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_duo_r.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:14,128 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_duo_step.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:14,175 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_duo_step_pill.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:14,181 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_window.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:14,186 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_trio_in_r.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:14,187 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/panel/panel_trio_out_r.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:14,353 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composite/composite_double_pill.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:14,444 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composite/composite_triple_pill.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:14,490 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composite/composite_half_circle.svg HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 08:54:14,500 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composite/composite_cut_circle.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:54:14,507 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composite/composite_sonar.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:14,513 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/brushed/brush_1.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:14,679 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/brushed/brush_2.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:14,770 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/brushed/brush_3.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:14,817 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/brushed/brush_4.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:14,818 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composition/composition_organic_line.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:14,833 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composition/composition_oval_line.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:14,833 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composition/composition_triangle_line.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:14,999 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:14] "GET /web_editor/static/image_shapes/composition/composition_line_1.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:15,092 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_line_3.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,130 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_line_2.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:15,131 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_mixed_1.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:15,150 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_mixed_2.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:15,155 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_planet_1.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,323 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_planet_2.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:15,410 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_square_1.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:15,448 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_square_2.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:15,448 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_square_3.svg HTTP/1.1" 200 - 0 0.000 0.010
2025-06-25 08:54:15,456 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_square_4.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:15,471 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/composition/composition_square_line.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:15,648 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_organic_cross.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,738 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_organic_caps.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,769 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_wave_1.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:15,769 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_oval_zebra.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,783 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_line_star.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:15,798 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_line_sun.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:15,962 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:15] "GET /web_editor/static/image_shapes/pattern/pattern_wave_2.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:16,067 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_wave_3.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:16,085 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_point.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:16,099 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_organic_dot.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:16,104 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_labyrinth.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:16,116 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_circuit.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:16,278 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/pattern/pattern_wave_4.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:16,387 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_1.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:16,395 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_2.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:16,411 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_4.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:16,420 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_3.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:16,425 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_5.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:16,597 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_shadow_1.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:16,707 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_blob_shadow_2.svg HTTP/1.1" 200 - 0 0.000 0.006
2025-06-25 08:54:16,718 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_square_1.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:54:16,730 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_square_2.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:16,747 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/special/special_speed.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:16,747 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/solid/solid_square_3.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:54:16,916 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:16] "GET /web_editor/static/image_shapes/special/special_rain.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:17,027 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/special/special_snow.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:17,040 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/special/special_layered.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:17,054 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/special/special_filter.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:17,061 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/special/special_flag.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:17,070 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/special/special_organic.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:17,233 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_front_portrait.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:17,354 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_3d_portrait_01.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:17,384 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_front_landscape.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:17,384 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_3d_landscape_01.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:17,392 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_3d_portrait_02.svg HTTP/1.1" 200 - 0 0.000 0.038
2025-06-25 08:54:17,401 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/iphone_3d_landscape_02.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:17,555 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_front_portrait.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 08:54:17,678 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_3d_portrait_01.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 08:54:17,711 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_3d_portrait_02.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:17,711 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_3d_landscape_01.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:17,711 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_front_landscape.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:17,726 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/galaxy_3d_landscape_02.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:17,879 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:17] "GET /web_editor/static/image_shapes/devices/ipad_front_portrait.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:18,001 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/ipad_3d_portrait_01.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:18,025 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/ipad_front_landscape.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:18,037 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/ipad_3d_landscape_01.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:18,037 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/ipad_3d_portrait_02.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:18,052 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/ipad_3d_landscape_02.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:18,194 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /website/static/src/img/snippets_options/align_top.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:18,323 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /website/static/src/img/snippets_options/align_middle.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:18,347 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /website/static/src/img/snippets_options/align_bottom.svg HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 08:54:18,348 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/imac_front.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 08:54:18,355 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /website/static/src/img/snippets_options/align_stretch.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:18,371 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/imac_3d_01.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 08:54:18,517 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/imac_3d_02.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:18,658 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/macbook_front.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 08:54:18,673 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/browser_01.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 08:54:18,673 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/macbook_3d_02.svg HTTP/1.1" 200 - 0 0.000 0.014
2025-06-25 08:54:18,674 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/macbook_3d_01.svg HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 08:54:18,686 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "POST /web_editor/get_image_info HTTP/1.1" 200 - 6 0.004 0.006
2025-06-25 08:54:18,846 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "GET /web_editor/static/image_shapes/devices/browser_02.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 08:54:18,991 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:18] "POST /web/dataset/call_kw/ir.attachment/search_read HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 08:54:19,289 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:19] "GET /web_editor/static/image_shapes/devices/browser_03.svg HTTP/1.1" 200 - 0 0.000 0.041
2025-06-25 08:54:19,313 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:19] "GET /web_editor/static/src/img/curved_arrow.svg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 08:54:23,007 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:23] "POST /web/dataset/call_kw/ir.attachment/search_read HTTP/1.1" 200 - 4 0.001 0.006
2025-06-25 08:54:24,554 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:24] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 0 0.000 0.046
2025-06-25 08:54:24,827 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:24] "GET /web/static/fonts/tiktok_only.woff HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 08:54:42,350 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:42] "GET / HTTP/1.1" 200 - 34 0.017 0.060
2025-06-25 08:54:42,752 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:42] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.002
2025-06-25 08:54:45,437 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:45] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.128 0.122
2025-06-25 08:54:57,472 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 200 - 10 0.003 0.009
2025-06-25 08:54:57,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 200 - 10 0.014 0.050
2025-06-25 08:54:57,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 200 - 10 0.015 0.051
2025-06-25 08:54:57,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 200 - 10 0.012 0.054
2025-06-25 08:54:57,843 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 200 - 11 0.012 0.017
2025-06-25 08:54:57,847 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 200 - 11 0.014 0.016
2025-06-25 08:54:57,849 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:57] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 200 - 11 0.019 0.013
2025-06-25 08:54:58,207 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:58] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 200 - 11 0.009 0.043
2025-06-25 08:54:58,207 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:58] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 200 - 11 0.014 0.041
2025-06-25 08:54:58,208 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:54:58] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 200 - 11 0.012 0.043
2025-06-25 08:55:05,798 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:55:05] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.007
2025-06-25 08:55:05,799 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:55:05] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.007
2025-06-25 08:55:05,801 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:55:05] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.005 0.005
2025-06-25 08:55:06,038 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:55:06] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.001 0.005
2025-06-25 08:55:06,113 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 08:55:06] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 09:00:53,065 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET / HTTP/1.1" 200 - 34 0.023 0.117
2025-06-25 09:00:53,231 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.009 0.013
2025-06-25 09:00:53,566 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.021 0.025
2025-06-25 09:00:53,570 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.028 0.022
2025-06-25 09:00:53,574 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.031 0.023
2025-06-25 09:00:53,584 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.025 0.034
2025-06-25 09:00:53,585 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.028 0.034
2025-06-25 09:00:53,590 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.012 0.019
2025-06-25 09:00:53,876 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.003
2025-06-25 09:00:53,897 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.009 0.009
2025-06-25 09:00:53,897 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:53] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.005 0.013
2025-06-25 09:00:54,083 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:54] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.003 0.009
2025-06-25 09:00:56,006 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:00:56] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.113 0.116
2025-06-25 09:01:11,055 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:11] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 09:01:11,371 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:11] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:01:11,372 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:11] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:01:11,693 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:11] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:01:11,694 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:11] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.001 0.006
2025-06-25 09:01:42,155 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:42] "GET /web/bundle/web.ace_lib?lang=vi_VN&debug=1 HTTP/1.1" 200 - 7 0.005 0.039
2025-06-25 09:01:42,468 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Found a similar attachment for /web/assets/1/d8f0860/web.ace_lib.min.js, copying from /web/assets/d8f0860/web.ace_lib.min.js 
2025-06-25 09:01:43,300 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:01:43] "GET /web/assets/1/d8f0860/web.ace_lib.min.js HTTP/1.1" 200 - 12 0.007 0.832
2025-06-25 09:02:29,546 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 09:02:29,548 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:29] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 19 0.018 0.078
2025-06-25 09:02:29,891 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:29] "POST /website/get_seo_data HTTP/1.1" 200 - 10 0.007 0.010
2025-06-25 09:02:30,372 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET / HTTP/1.1" 200 - 119 0.078 0.420
2025-06-25 09:02:30,508 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.007 0.016
2025-06-25 09:02:30,842 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.013 0.022
2025-06-25 09:02:30,870 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.022 0.033
2025-06-25 09:02:30,874 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.028 0.026
2025-06-25 09:02:30,880 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.016 0.036
2025-06-25 09:02:30,882 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.018 0.029
2025-06-25 09:02:30,884 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:30] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.020 0.041
2025-06-25 09:02:31,194 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:31] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.005
2025-06-25 09:02:31,207 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:31] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.012 0.008
2025-06-25 09:02:31,208 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:31] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-25 09:02:31,341 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:31] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.009 0.007
2025-06-25 09:02:37,767 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:37] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.107 0.152
2025-06-25 09:02:40,834 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:40] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 09:02:41,139 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:41] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.000 0.006
2025-06-25 09:02:41,140 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:41] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 09:02:41,461 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:41] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:02:41,462 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:41] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:02:43,066 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:43] "POST /web_editor/get_image_info HTTP/1.1" 200 - 6 0.002 0.007
2025-06-25 09:02:56,482 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:56] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.005 0.008
2025-06-25 09:02:56,740 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:56] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.002 0.008
2025-06-25 09:02:56,803 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:56] "POST /web_editor/get_image_info HTTP/1.1" 200 - 12 0.005 0.006
2025-06-25 09:02:57,055 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "GET /website/static/src/img/snippets_demo/s_references_6.png HTTP/1.1" 200 - 0 0.000 0.001
2025-06-25 09:02:57,317 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "GET /web_editor/static/src/img/snippets_options/bring-backward.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 09:02:57,618 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "GET /web_editor/static/src/img/snippets_options/bring-forward.svg HTTP/1.1" 200 - 0 0.000 0.009
2025-06-25 09:02:57,680 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "POST /web/dataset/call_kw/ir.attachment/search_read HTTP/1.1" 200 - 4 0.004 0.002
2025-06-25 09:02:57,713 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "POST /web/dataset/call_kw/ir.attachment/search_read HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 09:02:57,726 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:02:57] "POST /web/dataset/call_kw/ir.attachment/search_read HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 09:03:06,779 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:06] "GET / HTTP/1.1" 200 - 34 0.020 0.082
2025-06-25 09:03:16,023 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:16] "GET /contactus HTTP/1.1" 200 - 42 0.025 0.072
2025-06-25 09:03:16,074 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:16] "GET /web/image/website.s_parallax_default_image HTTP/1.1" 304 - 8 0.004 0.008
2025-06-25 09:03:16,565 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:16] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 6 0.005 0.009
2025-06-25 09:03:16,805 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:16] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 09:03:20,421 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:20] "GET / HTTP/1.1" 200 - 34 0.021 0.088
2025-06-25 09:03:20,914 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:20] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.003 0.003
2025-06-25 09:03:22,231 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.008 0.011
2025-06-25 09:03:22,555 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.013 0.015
2025-06-25 09:03:22,555 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.011 0.015
2025-06-25 09:03:22,559 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.013 0.016
2025-06-25 09:03:22,773 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.012 0.016
2025-06-25 09:03:22,774 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.014 0.014
2025-06-25 09:03:22,776 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:22] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.009 0.023
2025-06-25 09:03:23,383 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:23] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.011 0.010
2025-06-25 09:03:23,383 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:23] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.013 0.009
2025-06-25 09:03:23,386 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:23] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.009 0.014
2025-06-25 09:03:26,488 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:26] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.138 0.136
2025-06-25 09:03:28,721 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:28] "POST /web/dataset/call_kw/utm.campaign/fields_get HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 09:03:28,725 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:28] "POST /web/dataset/call_kw/utm.medium/fields_get HTTP/1.1" 200 - 3 0.002 0.009
2025-06-25 09:03:28,726 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:28] "POST /web/dataset/call_kw/utm.source/fields_get HTTP/1.1" 200 - 3 0.003 0.006
2025-06-25 09:03:28,980 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:28] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.003 0.002
2025-06-25 09:03:29,047 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:29] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.004 0.004
2025-06-25 09:03:29,048 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:29] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.001 0.007
2025-06-25 09:03:29,058 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:29] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:03:29,364 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:29] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.001 0.004
2025-06-25 09:03:31,775 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:31] "POST /web_editor/get_image_info HTTP/1.1" 200 - 6 0.003 0.005
2025-06-25 09:03:48,190 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET / HTTP/1.1" 200 - 34 0.016 0.104
2025-06-25 09:03:48,667 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.016 0.044
2025-06-25 09:03:48,668 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.013 0.048
2025-06-25 09:03:48,668 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.009 0.053
2025-06-25 09:03:48,697 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.020 0.072
2025-06-25 09:03:48,697 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.024 0.065
2025-06-25 09:03:48,698 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.017 0.070
2025-06-25 09:03:48,983 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.006
2025-06-25 09:03:48,997 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.010 0.011
2025-06-25 09:03:48,999 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:48] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.007 0.014
2025-06-25 09:03:49,016 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:49] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.004 0.009
2025-06-25 09:03:49,201 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:49] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.004 0.010
2025-06-25 09:03:52,728 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:52] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.130 0.144
2025-06-25 09:03:57,278 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:57] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:03:57,589 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:57] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.007
2025-06-25 09:03:57,590 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:57] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.007
2025-06-25 09:03:57,912 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:57] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:03:57,913 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:03:57] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.001 0.005
2025-06-25 09:04:13,886 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:13] "GET /web/bundle/web.ace_lib?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.003 0.004
2025-06-25 09:04:26,584 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Calendar: Event Reminder' (30) starting 
2025-06-25 09:04:26,594 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Calendar: Event Reminder' (30) done in 0.009s 
2025-06-25 09:04:26,597 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Calendar: Event Reminder' (30) processed 0 records, 0 records remaining 
2025-06-25 09:04:26,602 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Calendar: Event Reminder' (30) completed 
2025-06-25 09:04:26,609 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 09:04:26,626 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.017s 
2025-06-25 09:04:26,629 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 09:04:26,632 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 09:04:37,488 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:37] "GET / HTTP/1.1" 200 - 34 0.012 0.105
2025-06-25 09:04:43,732 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:43] "GET /my/home HTTP/1.1" 200 - 74 0.052 0.347
2025-06-25 09:04:43,813 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:43] "GET /sale/static/src/img/bag.svg HTTP/1.1" 200 - 0 0.000 0.022
2025-06-25 09:04:44,152 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:44] "GET /account/static/src/img/Bill.svg HTTP/1.1" 200 - 0 0.000 0.045
2025-06-25 09:04:44,167 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:44] "POST /my/counters HTTP/1.1" 200 - 31 0.039 0.023
2025-06-25 09:04:44,210 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:44] "GET /portal/static/src/img/portal-connection.svg HTTP/1.1" 200 - 0 0.000 0.011
2025-06-25 09:04:46,103 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:46] "GET /odoo HTTP/1.1" 200 - 37 0.026 0.045
2025-06-25 09:04:46,345 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:46] "GET /web/assets/1/3ec73ac/web.assets_web.min.js HTTP/1.1" 200 - 5 0.003 0.004
2025-06-25 09:04:46,945 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:46] "POST /web/action/load HTTP/1.1" 200 - 8 0.004 0.006
2025-06-25 09:04:46,991 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:46] "POST /mail/data HTTP/1.1" 200 - 26 0.028 0.016
2025-06-25 09:04:47,082 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.008 0.015
2025-06-25 09:04:47,082 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.010 0.012
2025-06-25 09:04:47,103 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.014 0.016
2025-06-25 09:04:47,144 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "POST /mail/data HTTP/1.1" 200 - 40 0.049 0.037
2025-06-25 09:04:47,265 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.001 0.007
2025-06-25 09:04:47,332 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 6 0.004 0.008
2025-06-25 09:04:47,465 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.003 0.004
2025-06-25 09:04:47,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:47] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.005
2025-06-25 09:04:48,853 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:48] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.002 0.003
2025-06-25 09:04:52,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/action/load HTTP/1.1" 200 - 8 0.006 0.003
2025-06-25 09:04:52,845 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.003 0.001
2025-06-25 09:04:52,930 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 4 0.004 0.009
2025-06-25 09:04:52,932 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.010
2025-06-25 09:04:52,933 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.012
2025-06-25 09:04:52,935 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.012
2025-06-25 09:04:52,956 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:52] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.003 0.003
2025-06-25 09:04:53,282 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:53] "GET /website/iframefallback HTTP/1.1" 200 - 18 0.007 0.017
2025-06-25 09:04:53,366 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:53] "GET / HTTP/1.1" 200 - 34 0.018 0.076
2025-06-25 09:04:53,815 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:53] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.002
2025-06-25 09:04:55,309 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.001 0.010
2025-06-25 09:04:55,638 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.013 0.011
2025-06-25 09:04:55,638 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.012 0.011
2025-06-25 09:04:55,640 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.010 0.013
2025-06-25 09:04:55,745 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.009 0.015
2025-06-25 09:04:55,748 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.010 0.016
2025-06-25 09:04:55,748 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.009 0.016
2025-06-25 09:04:55,976 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.015 0.007
2025-06-25 09:04:55,980 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.007 0.018
2025-06-25 09:04:55,982 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:55] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.011 0.015
2025-06-25 09:04:59,808 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:04:59] "GET /web/bundle/web._assets_jquery?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 09:05:00,070 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:00] "GET /web/bundle/website.backend_assets_all_wysiwyg?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 09:05:00,352 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:00] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.105 0.105
2025-06-25 09:05:00,470 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:00] "POST /web/dataset/call_kw/res.lang/get_installed HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 09:05:00,487 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:00] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 9 0.004 0.018
2025-06-25 09:05:05,003 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:05] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 551 0.622 3.372
2025-06-25 09:05:08,496 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:08] "POST /web/dataset/call_kw/utm.campaign/fields_get HTTP/1.1" 200 - 3 0.002 0.005
2025-06-25 09:05:08,811 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:08] "POST /web/dataset/call_kw/utm.medium/fields_get HTTP/1.1" 200 - 3 0.002 0.004
2025-06-25 09:05:08,815 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:08] "POST /web/dataset/call_kw/utm.source/fields_get HTTP/1.1" 200 - 3 0.004 0.004
2025-06-25 09:05:08,842 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:08] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.001 0.004
2025-06-25 09:05:09,136 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:09] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.006
2025-06-25 09:05:09,136 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:09] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 09:05:09,455 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:09] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:05:09,456 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:09] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.002 0.006
2025-06-25 09:05:15,151 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:15] "GET /website/static/src/img/snippets_options/palette.svg HTTP/1.1" 200 - 0 0.000 0.036
2025-06-25 09:05:15,589 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:15] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.004
2025-06-25 09:05:15,589 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:15] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 9 0.002 0.009
2025-06-25 09:05:26,181 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:26] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 8 0.004 0.005
2025-06-25 09:05:26,487 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:26] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.003
2025-06-25 09:05:56,935 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:05:56] "POST /web_editor/get_image_info HTTP/1.1" 200 - 6 0.002 0.004
2025-06-25 09:10:58,143 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 09:10:58,145 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:58] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.015 0.040
2025-06-25 09:10:58,547 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:58] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.010 0.014
2025-06-25 09:10:58,803 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:58] "GET / HTTP/1.1" 200 - 119 0.076 0.376
2025-06-25 09:10:58,898 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:58] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.006 0.010
2025-06-25 09:10:59,259 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.026 0.041
2025-06-25 09:10:59,266 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.038 0.037
2025-06-25 09:10:59,270 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.040 0.038
2025-06-25 09:10:59,275 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.039 0.039
2025-06-25 09:10:59,281 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.059 0.028
2025-06-25 09:10:59,283 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.025 0.047
2025-06-25 09:10:59,588 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.004 0.006
2025-06-25 09:10:59,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-25 09:10:59,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.010 0.008
2025-06-25 09:10:59,761 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:10:59] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.006 0.008
2025-06-25 09:11:04,522 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:04] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.113 0.128
2025-06-25 09:11:18,327 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:18] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:11:18,638 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:18] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.003
2025-06-25 09:11:18,640 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:18] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.004 0.003
2025-06-25 09:11:18,964 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:18] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.007
2025-06-25 09:11:18,964 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:18] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:11:23,400 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:11:23] "GET /web/bundle/web.ace_lib?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 09:20:30,066 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 09:20:30,068 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:30] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.019 0.042
2025-06-25 09:20:30,476 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:30] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.010 0.014
2025-06-25 09:20:30,752 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:30] "GET / HTTP/1.1" 200 - 119 0.076 0.404
2025-06-25 09:20:30,893 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:30] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.006 0.018
2025-06-25 09:20:31,231 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.027 0.024
2025-06-25 09:20:31,234 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.030 0.022
2025-06-25 09:20:31,235 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.024 0.029
2025-06-25 09:20:31,242 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.018 0.039
2025-06-25 09:20:31,243 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.026 0.030
2025-06-25 09:20:31,257 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.010 0.028
2025-06-25 09:20:31,544 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.000 0.005
2025-06-25 09:20:31,589 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.005 0.014
2025-06-25 09:20:31,591 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.009 0.010
2025-06-25 09:20:31,736 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:31] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.002 0.009
2025-06-25 09:20:33,517 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:33] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.117 0.120
2025-06-25 09:20:34,525 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 09:20:34,530 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.005s 
2025-06-25 09:20:34,533 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 09:20:34,538 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 09:20:34,548 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 09:20:34,557 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.009s 
2025-06-25 09:20:34,560 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 09:20:34,563 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 09:20:34,571 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 09:20:34,576 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.005s 
2025-06-25 09:20:34,579 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 09:20:34,583 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 09:20:34,592 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 09:20:34,602 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.010s 
2025-06-25 09:20:34,607 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 09:20:34,612 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 09:20:34,621 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 09:20:34,625 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.004s 
2025-06-25 09:20:34,628 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 09:20:34,632 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 09:20:41,951 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:41] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.001 0.004
2025-06-25 09:20:42,265 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:42] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.001 0.005
2025-06-25 09:20:42,266 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:42] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.003 0.002
2025-06-25 09:20:42,591 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:42] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 09:20:42,591 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:20:42] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 09:29:34,748 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 09:29:34,752 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.004s 
2025-06-25 09:29:34,755 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 09:29:34,759 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 09:29:34,765 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 09:29:34,791 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.025s 
2025-06-25 09:29:34,793 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 09:29:34,797 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 09:33:51,500 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 09:33:51,501 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:51] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.028 0.099
2025-06-25 09:33:51,890 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:51] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.008 0.015
2025-06-25 09:33:52,168 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET / HTTP/1.1" 200 - 119 0.088 0.460
2025-06-25 09:33:52,321 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.008 0.013
2025-06-25 09:33:52,791 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.014 0.169
2025-06-25 09:33:52,792 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.018 0.166
2025-06-25 09:33:52,826 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.026 0.187
2025-06-25 09:33:52,826 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.035 0.176
2025-06-25 09:33:52,831 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.019 0.172
2025-06-25 09:33:52,847 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:52] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.045 0.182
2025-06-25 09:33:53,122 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:53] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.003 0.006
2025-06-25 09:33:53,137 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:53] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.011 0.011
2025-06-25 09:33:53,159 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:53] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.007 0.019
2025-06-25 09:33:53,373 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:33:53] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.008 0.008
2025-06-25 09:34:11,167 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:11] "POST /web/action/load HTTP/1.1" 200 - 12 0.043 0.009
2025-06-25 09:34:11,463 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:11] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 51 0.038 0.051
2025-06-25 09:34:11,502 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 09:34:11,844 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 63 0.036 0.030
2025-06-25 09:34:11,922 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:11] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.004 0.138
2025-06-25 09:34:13,331 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:13] "POST /web/action/load HTTP/1.1" 200 - 12 0.007 0.008
2025-06-25 09:34:13,784 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:13] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 59 0.056 0.074
2025-06-25 09:34:13,960 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:13] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.027 0.023
2025-06-25 09:34:14,121 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:14] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 8 0.004 0.007
2025-06-25 09:34:14,201 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:14] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.008 0.028
2025-06-25 09:34:16,465 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:16] "POST /web/dataset/call_button/pos.config/open_ui HTTP/1.1" 200 - 21 0.017 0.018
2025-06-25 09:34:17,045 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:17] "GET /pos/ui?config_id=4&from_backend=True&debug=1 HTTP/1.1" 200 - 66 0.029 0.326
2025-06-25 09:34:26,015 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/3a3edd0/point_of_sale.assets_prod.min.css (id:1536) 
2025-06-25 09:34:26,016 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1499] (matching /web/assets/1/_______/point_of_sale.assets_prod.min.css) because it was replaced with /web/assets/1/3a3edd0/point_of_sale.assets_prod.min.css 
2025-06-25 09:34:26,075 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:26] "GET /web/assets/1/3a3edd0/point_of_sale.assets_prod.min.css HTTP/1.1" 200 - 19 0.077 8.916
2025-06-25 09:34:38,649 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/dcf87cc/point_of_sale.assets_prod.min.js (id:1537) 
2025-06-25 09:34:38,649 25596 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1510] (matching /web/assets/1/_______/point_of_sale.assets_prod.min.js) because it was replaced with /web/assets/1/dcf87cc/point_of_sale.assets_prod.min.js 
2025-06-25 09:34:38,849 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:38] "GET /web/assets/1/dcf87cc/point_of_sale.assets_prod.min.js HTTP/1.1" 200 - 13 0.009 21.439
2025-06-25 09:34:39,387 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:39] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.005 0.024
2025-06-25 09:34:39,753 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:39] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.002 0.017
2025-06-25 09:34:40,034 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:40] "POST /web/dataset/call_kw/pos.session/load_data HTTP/1.1" 200 - 249 0.336 0.341
2025-06-25 09:34:40,139 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:40] "POST /web/dataset/call_kw/barcode.nomenclature/read HTTP/1.1" 200 - 7 0.007 0.006
2025-06-25 09:34:40,460 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:40] "POST /web/dataset/call_kw/barcode.rule/search_read HTTP/1.1" 200 - 4 0.000 0.006
2025-06-25 09:34:40,724 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:40] "POST /web/dataset/call_kw/product.product/search_read HTTP/1.1" 200 - 5 0.002 0.008
2025-06-25 09:34:40,782 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:34:40] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.001 0.005
2025-06-25 09:43:34,970 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 09:43:34,976 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.006s 
2025-06-25 09:43:34,977 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 09:43:34,982 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 09:43:41,040 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:41] "GET /point_of_sale/static/src/img/scroll-down.png HTTP/1.1" 200 - 0 0.000 0.005
2025-06-25 09:43:41,042 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:41] "GET /point_of_sale/static/src/img/scroll-up.png HTTP/1.1" 200 - 0 0.000 0.007
2025-06-25 09:43:41,058 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:41] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.009 0.012
2025-06-25 09:43:42,456 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:42] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 5 0.002 0.005
2025-06-25 09:43:43,035 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=4 HTTP/1.1" 304 - 6 0.009 0.157
2025-06-25 09:43:43,035 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=5 HTTP/1.1" 304 - 6 0.008 0.159
2025-06-25 09:43:43,035 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=3 HTTP/1.1" 304 - 6 0.008 0.158
2025-06-25 09:43:43,050 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=2 HTTP/1.1" 304 - 6 0.006 0.172
2025-06-25 09:43:43,051 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=1 HTTP/1.1" 304 - 6 0.011 0.169
2025-06-25 09:43:43,060 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:43:43] "GET /web/image?model=pos.category&field=image_128&id=7 HTTP/1.1" 304 - 6 0.016 0.170
2025-06-25 09:44:45,531 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #6934954 finished 
2025-06-25 09:44:45,610 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:44:45] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 81 0.067 0.159
2025-06-25 09:44:47,223 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #33588616 started for PoS orders references: [{'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'}] 
2025-06-25 09:44:47,235 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #33588616 order {'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'} updated pos.order #36 
2025-06-25 09:44:47,238 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #33588616 finished 
2025-06-25 09:44:47,359 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:44:47] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 50 0.025 0.116
2025-06-25 09:44:47,694 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:44:47] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 6 0.003 0.006
2025-06-25 09:44:47,701 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:44:47] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 6 0.008 0.006
2025-06-25 09:49:46,834 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:49:46] "GET /hr_attendance/static/img/background-light.svg HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 09:49:46,837 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 09:49:46] "GET /web/static/img/odoo_logo.svg HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 09:49:50,902 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #11125320 started for PoS orders references: [{'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'}] 
2025-06-25 09:49:50,921 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #11125320 order {'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'} updated pos.order #36 
2025-06-25 09:49:50,925 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #11125320 finished 
2025-06-25 09:49:51,014 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 09:49:51] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 50 0.037 0.085
2025-06-25 10:04:21,034 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 10:04:21,082 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:04:21] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 7 0.001 0.053
2025-06-25 10:04:27,027 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 10:04:27,062 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.034s 
2025-06-25 10:04:27,066 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 10:04:27,069 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 10:04:57,475 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:04:57] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 15 0.012 0.029
2025-06-25 10:04:57,805 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:04:57] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.004
2025-06-25 10:04:57,897 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:04:57] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 15 0.075 0.028
2025-06-25 10:05:04,659 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:04] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 15 0.011 0.028
2025-06-25 10:05:30,822 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:30] "POST /web/dataset/call_kw/pos.order/search_read HTTP/1.1" 200 - 4 0.030 0.005
2025-06-25 10:05:31,075 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:31] "POST /web/dataset/call_kw/pos.order/search_read HTTP/1.1" 200 - 32 0.018 0.024
2025-06-25 10:05:42,887 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #53941444 started for PoS orders references: [{'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'}] 
2025-06-25 10:05:42,901 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #53941444 order {'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'} updated pos.order #36 
2025-06-25 10:05:42,904 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #53941444 finished 
2025-06-25 10:05:43,026 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:43] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 50 0.031 0.112
2025-06-25 10:05:44,857 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:44] "POST /web/dataset/call_kw/pos.config/read_config_open_orders HTTP/1.1" 200 - 6 0.003 0.003
2025-06-25 10:05:56,032 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #95319680 started for PoS orders references: [{'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'}] 
2025-06-25 10:05:56,046 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #95319680 order {'name': '/', 'uuid': 'a32a37a9-7c67-498c-8456-c6f6459b1c2f'} updated pos.order #36 
2025-06-25 10:05:56,050 25596 INFO ecomplus odoo.addons.point_of_sale.models.pos_order: PoS synchronisation #95319680 finished 
2025-06-25 10:05:56,131 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:56] "POST /web/dataset/call_kw/pos.order/sync_from_ui HTTP/1.1" 200 - 50 0.028 0.077
2025-06-25 10:05:57,417 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.006 0.005
2025-06-25 10:05:57,427 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.013 0.009
2025-06-25 10:05:57,682 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "POST /web/action/load HTTP/1.1" 200 - 13 0.009 0.007
2025-06-25 10:05:57,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 5 0.015 0.087
2025-06-25 10:05:57,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.039 0.066
2025-06-25 10:05:57,834 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "POST /mail/data HTTP/1.1" 200 - 26 0.039 0.067
2025-06-25 10:05:57,850 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.010 0.082
2025-06-25 10:05:57,854 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:57] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.012 0.099
2025-06-25 10:05:58,034 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:58] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.012 0.019
2025-06-25 10:05:58,237 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:58] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 7 0.047 0.032
2025-06-25 10:05:58,321 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:58] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-25 10:05:59,830 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:05:59] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.038
2025-06-25 10:06:01,837 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:01] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.007
2025-06-25 10:06:02,167 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:02] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 7 0.006 0.003
2025-06-25 10:06:02,215 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:02] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.037 0.021
2025-06-25 10:06:09,577 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:09] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.010 0.017
2025-06-25 10:06:09,873 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:09] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 10:06:13,771 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:13] "POST /web/action/load HTTP/1.1" 200 - 8 0.002 0.009
2025-06-25 10:06:14,094 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:14] "POST /mail/data HTTP/1.1" 200 - 40 0.041 0.030
2025-06-25 10:06:14,166 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:14] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.002 0.006
2025-06-25 10:06:25,464 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:25] "GET / HTTP/1.1" 200 - 34 0.023 0.080
2025-06-25 10:06:25,698 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:25] "GET / HTTP/1.1" 200 - 34 0.022 0.058
2025-06-25 10:06:29,983 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:29] "GET /odoo HTTP/1.1" 200 - 34 0.013 0.047
2025-06-25 10:06:30,301 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.004 0.006
2025-06-25 10:06:30,632 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "POST /web/action/load HTTP/1.1" 200 - 8 0.005 0.006
2025-06-25 10:06:30,707 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.008 0.011
2025-06-25 10:06:30,731 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 6 0.013 0.016
2025-06-25 10:06:30,731 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.014 0.019
2025-06-25 10:06:30,795 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "POST /mail/data HTTP/1.1" 200 - 26 0.087 0.019
2025-06-25 10:06:30,835 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:30] "POST /mail/data HTTP/1.1" 200 - 40 0.108 0.037
2025-06-25 10:06:31,000 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:31] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.005 0.003
2025-06-25 10:06:31,149 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:31] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.002 0.004
2025-06-25 10:06:31,472 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:31] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.005
2025-06-25 10:06:32,469 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:32] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.003
2025-06-25 10:06:32,879 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:32] "POST /web/action/load HTTP/1.1" 200 - 11 0.006 0.006
2025-06-25 10:06:33,201 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:33] "POST /web/dataset/call_kw/ket_noi_tmdt.shop/get_views HTTP/1.1" 200 - 27 0.013 0.062
2025-06-25 10:06:33,221 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:33] "POST /web/dataset/call_kw/ket_noi_tmdt.shop/web_search_read HTTP/1.1" 200 - 4 0.004 0.004
2025-06-25 10:06:33,529 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:33] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 10:06:34,606 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:34] "POST /web/action/load HTTP/1.1" 200 - 11 0.027 0.009
2025-06-25 10:06:34,876 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:34] "POST /web/dataset/call_kw/connection.api/get_views HTTP/1.1" 200 - 18 0.029 0.016
2025-06-25 10:06:34,948 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:34] "POST /web/dataset/call_kw/connection.api/web_search_read HTTP/1.1" 200 - 6 0.007 0.003
2025-06-25 10:06:37,530 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/action/load HTTP/1.1" 200 - 8 0.007 0.003
2025-06-25 10:06:37,871 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.004 0.009
2025-06-25 10:06:37,873 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 4 0.003 0.011
2025-06-25 10:06:37,919 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.056
2025-06-25 10:06:37,923 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.058
2025-06-25 10:06:37,926 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:37] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.003 0.060
2025-06-25 10:06:38,112 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:38] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.002 0.003
2025-06-25 10:06:38,273 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:38] "GET /website/iframefallback HTTP/1.1" 200 - 18 0.014 0.016
2025-06-25 10:06:38,377 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:38] "GET / HTTP/1.1" 200 - 34 0.069 0.063
2025-06-25 10:06:38,539 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:38] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.005
2025-06-25 10:06:41,740 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:41] "GET /web/bundle/web._assets_jquery?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.002 0.002
2025-06-25 10:06:41,981 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:41] "GET /web/bundle/website.backend_assets_all_wysiwyg?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.000 0.003
2025-06-25 10:06:42,297 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:42] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.086 0.109
2025-06-25 10:06:42,337 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:42] "POST /web/dataset/call_kw/res.lang/get_installed HTTP/1.1" 200 - 3 0.003 0.003
2025-06-25 10:06:42,360 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:42] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 9 0.005 0.017
2025-06-25 10:06:44,190 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:44] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 551 0.297 1.365
2025-06-25 10:06:47,167 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.017 0.014
2025-06-25 10:06:47,167 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.017 0.012
2025-06-25 10:06:47,169 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.014 0.020
2025-06-25 10:06:47,177 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.021 0.018
2025-06-25 10:06:47,306 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.007 0.010
2025-06-25 10:06:47,306 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.008 0.010
2025-06-25 10:06:47,499 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.015 0.010
2025-06-25 10:06:47,500 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.014 0.012
2025-06-25 10:06:47,505 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.013 0.016
2025-06-25 10:06:47,513 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:47] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.010 0.012
2025-06-25 10:06:56,961 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:56] "POST /web/dataset/call_kw/utm.campaign/fields_get HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 10:06:57,266 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.medium/fields_get HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 10:06:57,267 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.source/fields_get HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 10:06:57,278 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.001 0.004
2025-06-25 10:06:57,589 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 10:06:57,643 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.053 0.005
2025-06-25 10:06:57,907 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 10:06:57,954 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:06:57] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.002
2025-06-25 10:07:10,468 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:07:10] "GET /web/bundle/web.ace_lib?lang=vi_VN&debug=1 HTTP/1.1" 200 - 3 0.003 0.002
2025-06-25 10:08:22,999 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:22] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.070
2025-06-25 10:08:22,999 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:22] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.023 0.050
2025-06-25 10:08:23,000 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:22] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.032 0.037
2025-06-25 10:08:23,000 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "GET /slides HTTP/1.1" 200 - 86 0.089 0.323
2025-06-25 10:08:23,012 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.027 0.048
2025-06-25 10:08:23,017 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.014 0.066
2025-06-25 10:08:23,334 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.013
2025-06-25 10:08:23,336 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.014
2025-06-25 10:08:23,342 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.017
2025-06-25 10:08:23,346 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.014 0.015
2025-06-25 10:08:23,347 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.014 0.019
2025-06-25 10:08:23,356 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.011
2025-06-25 10:08:23,671 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.013
2025-06-25 10:08:23,677 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.014
2025-06-25 10:08:23,679 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.017
2025-06-25 10:08:23,681 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:23] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.016
2025-06-25 10:08:24,006 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:24] "POST /website/get_languages HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 10:08:29,312 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:08:29,313 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:29] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.044 0.141
2025-06-25 10:08:29,674 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:29] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.008 0.010
2025-06-25 10:08:30,213 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:30] "GET / HTTP/1.1" 200 - 119 0.097 0.462
2025-06-25 10:08:30,619 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:30] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 10:08:33,695 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:33] "POST /web/action/load HTTP/1.1" 200 - 12 0.042 0.015
2025-06-25 10:08:34,015 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:34] "POST /web/dataset/call_kw/slide.channel/get_views HTTP/1.1" 200 - 56 0.040 0.075
2025-06-25 10:08:34,061 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:34] "POST /web/dataset/call_kw/slide.channel/web_search_read HTTP/1.1" 200 - 4 0.002 0.008
2025-06-25 10:08:36,878 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:36] "POST /web/dataset/call_kw/slide.channel/onchange HTTP/1.1" 200 - 14 0.015 0.013
2025-06-25 10:08:50,899 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:50] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.002 0.006
2025-06-25 10:08:50,900 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:50] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 10:08:51,144 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:51] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.003 0.004
2025-06-25 10:08:51,280 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:51] "GET /website/iframefallback HTTP/1.1" 200 - 18 0.017 0.048
2025-06-25 10:08:51,327 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:51] "GET / HTTP/1.1" 200 - 34 0.033 0.077
2025-06-25 10:08:51,551 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:51] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 10:08:57,257 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-06-25 10:08:57,258 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:57] "POST /web/dataset/call_kw/website.page/website_publish_button HTTP/1.1" 200 - 19 0.018 0.021
2025-06-25 10:08:59,121 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:08:59] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 253 0.164 0.158
2025-06-25 10:09:20,539 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 12 0.013 0.015
2025-06-25 10:09:20,555 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 12 0.031 0.010
2025-06-25 10:09:20,556 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.015 0.013
2025-06-25 10:09:20,559 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 12 0.031 0.015
2025-06-25 10:09:20,559 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.017 0.012
2025-06-25 10:09:20,567 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.015 0.019
2025-06-25 10:09:20,859 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.007
2025-06-25 10:09:20,880 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.009
2025-06-25 10:09:20,885 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.010
2025-06-25 10:09:20,889 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.016
2025-06-25 10:09:20,892 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.013
2025-06-25 10:09:20,904 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:20] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.017
2025-06-25 10:09:21,180 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:21] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.006
2025-06-25 10:09:21,230 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:21] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.016
2025-06-25 10:09:21,231 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:21] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.010
2025-06-25 10:09:21,550 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:21] "POST /website/get_languages HTTP/1.1" 200 - 7 0.003 0.004
2025-06-25 10:09:38,927 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:38] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.166 0.481
2025-06-25 10:09:38,927 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:38] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.137 0.643
2025-06-25 10:09:38,930 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:38] "POST /website/get_suggested_links HTTP/1.1" 200 - 67 0.079 1.245
2025-06-25 10:09:39,981 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:39] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.022 0.032
2025-06-25 10:09:40,380 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:40] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.027 0.023
2025-06-25 10:09:42,405 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:42] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.025 0.024
2025-06-25 10:09:42,912 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:42] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.021 0.021
2025-06-25 10:09:44,188 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:44] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.030 0.026
2025-06-25 10:09:45,109 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:45] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.039 0.027
2025-06-25 10:09:46,090 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:46] "POST /website/get_suggested_links HTTP/1.1" 200 - 46 0.029 0.029
2025-06-25 10:09:48,993 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:09:48,994 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:48] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 38 0.029 0.036
2025-06-25 10:09:49,342 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:09:49,343 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:49] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 16 0.009 0.018
2025-06-25 10:09:49,737 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:09:49,738 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:49] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.044 0.134
2025-06-25 10:09:49,776 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:49] "POST /website/get_seo_data HTTP/1.1" 200 - 10 0.008 0.009
2025-06-25 10:09:50,841 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:50] "GET / HTTP/1.1" 200 - 184 0.146 0.627
2025-06-25 10:09:51,628 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:51] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.000 0.006
2025-06-25 10:09:53,534 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:53] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.136 0.154
2025-06-25 10:09:56,737 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.017
2025-06-25 10:09:56,743 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.020
2025-06-25 10:09:56,746 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.021
2025-06-25 10:09:56,752 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.026
2025-06-25 10:09:56,754 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.024
2025-06-25 10:09:56,757 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:56] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.014 0.022
2025-06-25 10:09:57,068 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.009
2025-06-25 10:09:57,070 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.011
2025-06-25 10:09:57,071 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.009
2025-06-25 10:09:57,088 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.011
2025-06-25 10:09:57,092 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.011
2025-06-25 10:09:57,095 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.012
2025-06-25 10:09:57,394 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.007
2025-06-25 10:09:57,395 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.010
2025-06-25 10:09:57,396 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.008
2025-06-25 10:09:57,724 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:09:57] "POST /website/get_languages HTTP/1.1" 200 - 7 0.002 0.008
2025-06-25 10:10:32,379 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.008
2025-06-25 10:10:32,706 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.016
2025-06-25 10:10:32,708 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.018
2025-06-25 10:10:32,711 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.020 0.015
2025-06-25 10:10:32,716 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.019 0.015
2025-06-25 10:10:32,720 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.018 0.024
2025-06-25 10:10:32,725 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:32] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.018
2025-06-25 10:10:33,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.016
2025-06-25 10:10:33,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.015 0.014
2025-06-25 10:10:33,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.011
2025-06-25 10:10:33,066 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.014 0.018
2025-06-25 10:10:33,067 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.020
2025-06-25 10:10:33,070 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.017 0.014
2025-06-25 10:10:33,380 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.004
2025-06-25 10:10:33,380 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.008
2025-06-25 10:10:33,891 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 5 0.004 0.006
2025-06-25 10:10:33,895 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 5 0.005 0.008
2025-06-25 10:10:33,896 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:33] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 5 0.008 0.007
2025-06-25 10:10:34,134 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:34] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.001 0.006
2025-06-25 10:10:34,210 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:34] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 10:10:35,942 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.007
2025-06-25 10:10:36,271 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.017
2025-06-25 10:10:36,274 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.015 0.015
2025-06-25 10:10:36,275 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.018
2025-06-25 10:10:36,280 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.015 0.019
2025-06-25 10:10:36,282 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.022
2025-06-25 10:10:36,287 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.017
2025-06-25 10:10:36,599 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.007
2025-06-25 10:10:36,600 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.008
2025-06-25 10:10:36,601 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.011
2025-06-25 10:10:36,622 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.011
2025-06-25 10:10:36,623 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.012
2025-06-25 10:10:36,623 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.010
2025-06-25 10:10:36,924 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.010
2025-06-25 10:10:36,925 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:36] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.009
2025-06-25 10:10:42,398 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:42] "GET /contactus HTTP/1.1" 200 - 42 0.032 0.082
2025-06-25 10:10:43,523 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:43] "GET /contactus HTTP/1.1" 200 - 39 0.026 0.082
2025-06-25 10:10:47,576 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:47] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 12 0.007 0.010
2025-06-25 10:10:47,727 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:47] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.004 0.010
2025-06-25 10:10:47,833 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:47] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.003 0.010
2025-06-25 10:10:48,007 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.005 0.010
2025-06-25 10:10:48,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 11 0.004 0.008
2025-06-25 10:10:48,916 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 11 0.015 0.013
2025-06-25 10:10:48,925 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 11 0.023 0.013
2025-06-25 10:10:48,927 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 11 0.020 0.017
2025-06-25 10:10:48,942 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 11 0.011 0.012
2025-06-25 10:10:48,943 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:10:48] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 11 0.007 0.015
2025-06-25 10:11:17,891 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:17] "GET /website_event/static/src/img/event_previews/event_2.jpg HTTP/1.1" 200 - 0 0.000 0.035
2025-06-25 10:11:17,891 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:17] "GET /website_event/static/src/img/event_previews/event_1.jpg HTTP/1.1" 200 - 0 0.000 0.036
2025-06-25 10:11:17,891 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:17] "GET /website_event/static/src/img/event_previews/event_3.jpg HTTP/1.1" 200 - 0 0.000 0.034
2025-06-25 10:11:20,918 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:20] "GET /website_sale/static/src/img/product_previews/product_1.jpg HTTP/1.1" 200 - 0 0.000 0.038
2025-06-25 10:11:21,227 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:21] "GET /website_sale/static/src/img/product_previews/product_3.jpg HTTP/1.1" 200 - 0 0.000 0.041
2025-06-25 10:11:21,227 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:21] "GET /website_sale/static/src/img/product_previews/product_4.jpg HTTP/1.1" 200 - 0 0.000 0.040
2025-06-25 10:11:21,227 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:21] "GET /website_sale/static/src/img/product_previews/product_2.jpg HTTP/1.1" 200 - 0 0.000 0.042
2025-06-25 10:11:22,427 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /website/static/src/img/snippets_previews/s_dynamic_snippet_carousel_preview.png HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 10:11:22,427 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /website/static/src/img/snippets_previews/s_dynamic_snippet_preview.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 10:11:23,998 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:23] "GET /website/static/src/img/snippets_previews/s_instagram_preview.jpg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 10:11:24,336 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:24] "GET /website/static/src/img/snippets_previews/s_map_preview.png HTTP/1.1" 200 - 0 0.000 0.040
2025-06-25 10:11:25,676 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:25] "GET /web/image/website.s_cover_default_image HTTP/1.1" 200 - 8 0.004 0.012
2025-06-25 10:11:25,702 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:25] "GET /web_editor/shape/website_payment/s_donation_gift.svg?c1=o-color-1 HTTP/1.1" 200 - 15 0.015 0.025
2025-06-25 10:11:27,175 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.005 0.006
2025-06-25 10:11:27,506 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_company_team_image_2 HTTP/1.1" 200 - 10 0.016 0.018
2025-06-25 10:11:27,534 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.022 0.024
2025-06-25 10:11:27,539 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web_editor/image_shape/website.s_company_team_image_5/web_editor/geometric/geo_door.svg HTTP/1.1" 200 - 17 0.027 0.041
2025-06-25 10:11:27,544 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web_editor/image_shape/website.s_company_team_image_1/web_editor/geometric_round/geo_round_square.svg HTTP/1.1" 200 - 16 0.028 0.040
2025-06-25 10:11:27,546 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web_editor/image_shape/website.s_company_team_image_6/web_editor/geometric_round/geo_round_circle.svg HTTP/1.1" 200 - 16 0.033 0.039
2025-06-25 10:11:27,546 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web_editor/image_shape/website.s_company_team_image_3/web_editor/geometric/geo_sonar.svg HTTP/1.1" 200 - 16 0.039 0.035
2025-06-25 10:11:27,834 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.004 0.008
2025-06-25 10:11:27,848 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.004 0.007
2025-06-25 10:11:27,881 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.013 0.016
2025-06-25 10:11:27,882 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.018 0.013
2025-06-25 10:11:27,883 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.013 0.018
2025-06-25 10:11:27,885 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:27] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.012 0.019
2025-06-25 10:11:28,159 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.007 0.005
2025-06-25 10:11:28,175 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.005 0.010
2025-06-25 10:11:28,513 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_quotes_carousel_demo_image_4 HTTP/1.1" 200 - 10 0.012 0.011
2025-06-25 10:11:28,514 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_quotes_carousel_demo_image_5 HTTP/1.1" 200 - 10 0.013 0.013
2025-06-25 10:11:28,538 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_quotes_carousel_demo_image_3 HTTP/1.1" 200 - 10 0.014 0.034
2025-06-25 10:11:28,549 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_quotes_carousel_demo_image_2 HTTP/1.1" 200 - 8 0.005 0.037
2025-06-25 10:11:28,769 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:28] "GET /web/image/website.s_picture_default_image HTTP/1.1" 200 - 8 0.006 0.015
2025-06-25 10:11:29,097 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_image_punchy_default_image HTTP/1.1" 200 - 8 0.023 0.013
2025-06-25 10:11:29,100 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_08 HTTP/1.1" 200 - 8 0.013 0.023
2025-06-25 10:11:29,127 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_13 HTTP/1.1" 200 - 8 0.009 0.039
2025-06-25 10:11:29,128 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_05 HTTP/1.1" 200 - 8 0.017 0.052
2025-06-25 10:11:29,128 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_10 HTTP/1.1" 200 - 8 0.013 0.054
2025-06-25 10:11:29,129 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_16 HTTP/1.1" 200 - 8 0.016 0.053
2025-06-25 10:11:29,473 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_masonry_block_default_image_1 HTTP/1.1" 200 - 8 0.009 0.020
2025-06-25 10:11:29,475 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_quadrant_default_image_1 HTTP/1.1" 200 - 8 0.013 0.018
2025-06-25 10:11:29,475 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_14 HTTP/1.1" 200 - 8 0.004 0.059
2025-06-25 10:11:29,475 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_03 HTTP/1.1" 200 - 8 0.007 0.056
2025-06-25 10:11:29,479 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_quadrant_default_image_2 HTTP/1.1" 200 - 8 0.017 0.017
2025-06-25 10:11:29,479 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_quadrant_default_image_3 HTTP/1.1" 200 - 8 0.015 0.017
2025-06-25 10:11:29,793 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.s_quadrant_default_image_4 HTTP/1.1" 200 - 8 0.004 0.008
2025-06-25 10:11:29,844 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:29] "GET /web/image/website.library_image_02 HTTP/1.1" 200 - 8 0.004 0.046
2025-06-25 10:11:30,186 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web/image/website.s_carousel_default_image_3 HTTP/1.1" 200 - 8 0.005 0.027
2025-06-25 10:11:30,187 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web/image/website.s_carousel_default_image_2 HTTP/1.1" 200 - 8 0.007 0.026
2025-06-25 10:11:30,208 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web/image/website.s_carousel_default_image_1 HTTP/1.1" 200 - 8 0.009 0.014
2025-06-25 10:11:30,209 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web/image/website.s_parallax_default_image HTTP/1.1" 304 - 8 0.006 0.015
2025-06-25 10:11:30,218 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/shape/web_editor/Airy/10.svg?c5=%23191A1E HTTP/1.1" 200 - 6 0.009 0.023
2025-06-25 10:11:30,442 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_mosaic_default_image_1/web_editor/geometric/geo_diamond.svg HTTP/1.1" 200 - 16 0.007 0.021
2025-06-25 10:11:30,578 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_mosaic_default_image_3/web_editor/geometric_round/geo_round_circle.svg HTTP/1.1" 200 - 16 0.027 0.025
2025-06-25 10:11:30,579 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_constellation_default_image/web_editor/geometric/geo_sonar.svg HTTP/1.1" 200 - 16 0.019 0.032
2025-06-25 10:11:30,582 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_mosaic_default_image_5/web_editor/geometric/geo_door.svg HTTP/1.1" 200 - 16 0.022 0.032
2025-06-25 10:11:30,585 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_mosaic_default_image_2/web_editor/geometric/geo_sonar.svg HTTP/1.1" 200 - 16 0.026 0.034
2025-06-25 10:11:30,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_mosaic_default_image_4/web_editor/geometric/geo_tear.svg HTTP/1.1" 200 - 16 0.020 0.051
2025-06-25 10:11:30,785 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web_editor/image_shape/website.s_images_constellation_default_image_1/web_editor/composite/composite_double_pill.svg HTTP/1.1" 200 - 16 0.007 0.021
2025-06-25 10:11:30,910 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:30] "GET /web/image/website.s_images_constellation_default_image_2 HTTP/1.1" 200 - 10 0.008 0.007
2025-06-25 10:11:31,348 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/shape/web_editor%2FWavy%2F11.svg?c1=%232D3142&c4=%23FFFFFF HTTP/1.1" 200 - 6 0.006 0.030
2025-06-25 10:11:31,365 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/image_shape/website.s_shape_image_default_image/web_editor/composite/composite_double_pill.svg HTTP/1.1" 200 - 16 0.022 0.031
2025-06-25 10:11:31,380 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/image_shape/website.s_mockup_image_default_image/web_editor/devices/macbook_front.svg HTTP/1.1" 200 - 17 0.026 0.041
2025-06-25 10:11:31,386 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/image_shape/website.s_cta_mockups_default_image/web_editor/devices/macbook_front.svg HTTP/1.1" 200 - 17 0.027 0.046
2025-06-25 10:11:31,396 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/image_shape/website.s_cta_mockups_default_image_1/web_editor/devices/iphone_front_portrait.svg HTTP/1.1" 200 - 17 0.033 0.047
2025-06-25 10:11:31,399 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web_editor/image_shape/website.s_image_hexagonal_default_image_1/web_editor/geometric/geo_hexagon.svg HTTP/1.1" 200 - 17 0.032 0.046
2025-06-25 10:11:31,680 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:31] "GET /web/image/website.s_image_hexagonal_default_image HTTP/1.1" 200 - 8 0.005 0.007
2025-06-25 10:11:32,004 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_text_cover_default_image HTTP/1.1" 200 - 8 0.002 0.011
2025-06-25 10:11:32,255 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /theme_cobalt/static/src/img/pictures/s_image_3.jpg HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 10:11:32,331 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web_editor/shape/web_editor%2FOrigins%2F04_001.svg?c3=%23FFFFFF&flip=y HTTP/1.1" 200 - 6 0.001 0.015
2025-06-25 10:11:32,457 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_popup_default_image HTTP/1.1" 200 - 10 0.004 0.011
2025-06-25 10:11:32,461 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web_editor/shape/web_editor/Blobs/12.svg?c1=%2380BBFF HTTP/1.1" 200 - 6 0.004 0.017
2025-06-25 10:11:32,584 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web_editor/shape/web_editor/Blocks/01_001.svg?c1=%2380BBFF&c3=%23FAFAFA&c5=%23191A1E HTTP/1.1" 200 - 6 0.002 0.017
2025-06-25 10:11:32,598 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_media_list_default_image_1 HTTP/1.1" 200 - 8 0.004 0.008
2025-06-25 10:11:32,610 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_media_list_default_image_2 HTTP/1.1" 200 - 8 0.005 0.009
2025-06-25 10:11:32,654 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_media_list_default_image_3 HTTP/1.1" 200 - 8 0.005 0.006
2025-06-25 10:11:32,776 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web_editor/shape/web_editor/Wavy/11.svg?c1=%2380BBFF&c4=%23FFFFFF HTTP/1.1" 200 - 6 0.000 0.008
2025-06-25 10:11:32,981 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /web/image/website.s_showcase_default_image HTTP/1.1" 200 - 8 0.004 0.008
2025-06-25 10:11:33,338 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:33] "GET /web/image/website.s_accordion_image_default_image HTTP/1.1" 200 - 8 0.002 0.010
2025-06-25 10:11:33,592 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:11:33] "GET /website/static/src/img/snippets_previews/s_embed_code_preview.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 10:13:06,245 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.018 0.013
2025-06-25 10:13:06,247 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.015 0.017
2025-06-25 10:13:06,251 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.014 0.020
2025-06-25 10:13:06,255 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.012 0.024
2025-06-25 10:13:06,473 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_1 HTTP/1.1" 304 - 10 0.006 0.008
2025-06-25 10:13:06,567 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "POST /web_editor/get_image_info HTTP/1.1" 200 - 12 0.007 0.008
2025-06-25 10:13:06,579 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /theme_cobalt/static/src/img/pictures/s_company_team_image_1.jpg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 10:13:06,599 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_6 HTTP/1.1" 304 - 10 0.010 0.012
2025-06-25 10:13:06,606 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_3 HTTP/1.1" 304 - 10 0.010 0.015
2025-06-25 10:13:06,797 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "GET /web/image/website.s_company_team_image_4 HTTP/1.1" 304 - 10 0.005 0.009
2025-06-25 10:13:06,893 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:06] "POST /web_editor/get_image_info HTTP/1.1" 200 - 11 0.005 0.009
2025-06-25 10:13:07,203 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:07] "POST /web_editor/get_image_info HTTP/1.1" 200 - 11 0.010 0.009
2025-06-25 10:13:07,205 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:07] "POST /web_editor/get_image_info HTTP/1.1" 200 - 11 0.010 0.010
2025-06-25 10:13:07,254 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:07] "GET /theme_cobalt/static/src/img/pictures/s_company_team_image_6.jpg HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 10:13:07,280 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:07] "GET /theme_cobalt/static/src/img/pictures/s_company_team_image_3.jpg HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 10:13:07,521 25596 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:07] "GET /theme_cobalt/static/src/img/pictures/s_company_team_image_4.jpg HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 10:13:48,506 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:13:48,507 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:48] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 16 0.006 0.056
2025-06-25 10:13:49,029 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:13:49,030 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:49] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.046 0.144
2025-06-25 10:13:49,397 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:49] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.018 0.010
2025-06-25 10:13:49,593 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:49] "GET / HTTP/1.1" 200 - 119 0.077 0.430
2025-06-25 10:13:49,723 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:49] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.007 0.013
2025-06-25 10:13:50,038 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.004 0.019
2025-06-25 10:13:50,055 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.024 0.015
2025-06-25 10:13:50,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.023 0.017
2025-06-25 10:13:50,057 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.020 0.020
2025-06-25 10:13:50,059 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.010 0.031
2025-06-25 10:13:50,302 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:13:50] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.004 0.008
2025-06-25 10:16:21,345 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:16:21] "POST /web/action/load HTTP/1.1" 200 - 13 0.010 0.006
2025-06-25 10:16:21,597 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:16:21] "POST /web/dataset/call_kw/slide.channel/onchange HTTP/1.1" 200 - 14 0.006 0.015
2025-06-25 10:18:26,105 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.002 0.006
2025-06-25 10:18:26,106 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 5 0.002 0.005
2025-06-25 10:18:26,362 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.003 0.003
2025-06-25 10:18:26,486 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "GET /website/iframefallback HTTP/1.1" 200 - 18 0.033 0.022
2025-06-25 10:18:26,529 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "GET / HTTP/1.1" 200 - 34 0.033 0.063
2025-06-25 10:18:26,823 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:26] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 10:18:29,853 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:29] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 239 0.109 0.160
2025-06-25 10:18:32,602 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.004 0.009
2025-06-25 10:18:32,916 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.011 0.009
2025-06-25 10:18:32,917 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.007 0.012
2025-06-25 10:18:32,939 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.013 0.014
2025-06-25 10:18:32,943 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.012 0.018
2025-06-25 10:18:32,943 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:32] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.009 0.018
2025-06-25 10:18:42,693 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:42] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.006
2025-06-25 10:18:42,695 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:42] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.002 0.008
2025-06-25 10:18:42,697 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:42] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.008
2025-06-25 10:18:42,935 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:42] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.003 0.003
2025-06-25 10:18:43,011 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:18:43] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.003
2025-06-25 10:19:06,302 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:19:06,303 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:06] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.055 0.180
2025-06-25 10:19:06,655 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:06] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.009 0.008
2025-06-25 10:19:07,161 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET / HTTP/1.1" 200 - 119 0.073 0.453
2025-06-25 10:19:07,298 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-25 10:19:07,630 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.018 0.020
2025-06-25 10:19:07,637 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.007 0.028
2025-06-25 10:19:07,639 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.021 0.025
2025-06-25 10:19:07,641 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.021 0.026
2025-06-25 10:19:07,645 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.015 0.031
2025-06-25 10:19:07,859 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:07] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.007 0.007
2025-06-25 10:19:10,043 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-06-25 10:19:10,044 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:10] "POST /web/dataset/call_kw/website.page/website_publish_button HTTP/1.1" 200 - 19 0.013 0.032
2025-06-25 10:19:10,296 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:10] "POST /website/get_seo_data HTTP/1.1" 200 - 30 0.017 0.018
2025-06-25 10:19:25,688 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:25] "GET / HTTP/1.1" 200 - 161 0.109 0.525
2025-06-25 10:19:26,201 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:26] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.003
2025-06-25 10:19:32,204 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET / HTTP/1.1" 200 - 34 0.029 0.067
2025-06-25 10:19:32,654 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 13 0.012 0.012
2025-06-25 10:19:32,655 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 11 0.015 0.010
2025-06-25 10:19:32,657 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 12 0.011 0.015
2025-06-25 10:19:32,887 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 11 0.005 0.007
2025-06-25 10:19:32,984 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 11 0.006 0.014
2025-06-25 10:19:32,986 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:19:32] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 11 0.008 0.012
2025-06-25 10:20:35,350 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 10:20:35,356 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.006s 
2025-06-25 10:20:35,358 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 10:20:35,362 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 10:20:35,368 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 10:20:35,377 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.009s 
2025-06-25 10:20:35,379 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 10:20:35,383 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 10:20:35,396 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 10:20:35,401 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.005s 
2025-06-25 10:20:35,405 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 10:20:35,409 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 10:20:35,415 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 10:20:35,426 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.010s 
2025-06-25 10:20:35,428 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 10:20:35,433 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 10:20:35,441 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 10:20:35,444 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.004s 
2025-06-25 10:20:35,446 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 10:20:35,450 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 10:23:59,014 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:23:59] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 240 0.133 0.131
2025-06-25 10:24:08,034 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.003 0.010
2025-06-25 10:24:08,352 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.008 0.010
2025-06-25 10:24:08,353 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.008 0.010
2025-06-25 10:24:08,384 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-25 10:24:08,387 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.011 0.012
2025-06-25 10:24:08,389 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:08] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.008 0.017
2025-06-25 10:24:18,661 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:18] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 5 0.004 0.005
2025-06-25 10:24:18,662 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:18] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 5 0.002 0.007
2025-06-25 10:24:18,688 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:18] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 5 0.030 0.006
2025-06-25 10:24:18,903 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:18] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 10:24:18,995 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:24:18] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.005 0.012
2025-06-25 10:29:35,556 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 10:29:35,562 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.007s 
2025-06-25 10:29:35,565 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 10:29:35,570 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 10:29:35,578 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 10:29:35,590 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.011s 
2025-06-25 10:29:35,592 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 10:29:35,597 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 10:33:28,011 25596 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-06-25 10:33:28,013 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:28] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 14 0.074 0.234
2025-06-25 10:33:28,057 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:28] "POST /website/get_seo_data HTTP/1.1" 200 - 9 0.007 0.011
2025-06-25 10:33:28,937 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:28] "GET / HTTP/1.1" 200 - 120 0.083 0.502
2025-06-25 10:33:29,490 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.010 0.098
2025-06-25 10:33:29,537 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.035 0.118
2025-06-25 10:33:29,537 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.031 0.118
2025-06-25 10:33:29,537 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.039 0.115
2025-06-25 10:33:29,538 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.021 0.126
2025-06-25 10:33:29,539 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:29] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.032 0.122
2025-06-25 10:33:33,975 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:33] "GET / HTTP/1.1" 200 - 34 0.023 0.090
2025-06-25 10:33:37,220 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:37] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 49 0.034 0.244
2025-06-25 10:33:52,332 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.004 0.008
2025-06-25 10:33:52,674 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 10 0.019 0.020
2025-06-25 10:33:52,679 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 10 0.012 0.031
2025-06-25 10:33:52,681 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 10 0.025 0.020
2025-06-25 10:33:52,682 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 10 0.022 0.023
2025-06-25 10:33:52,686 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:33:52] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 10 0.022 0.025
2025-06-25 10:43:35,769 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 10:43:35,781 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.013s 
2025-06-25 10:43:35,784 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 10:43:35,789 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 10:43:45,208 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:43:45] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.002
2025-06-25 10:43:54,378 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:43:54] "GET /blog/2 HTTP/1.1" 301 - 9 0.006 0.010
2025-06-25 10:43:54,961 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:43:54] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 69 0.041 0.232
2025-06-25 10:43:55,296 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:43:55] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 10:43:59,103 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:43:59] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 296 0.157 0.141
2025-06-25 10:44:05,222 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:05] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.001 0.006
2025-06-25 10:44:05,542 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:05] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.001 0.007
2025-06-25 10:44:05,542 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:05] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 10:44:05,862 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:05] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.001 0.005
2025-06-25 10:44:05,864 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:05] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 10:44:07,330 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.007
2025-06-25 10:44:07,880 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.235
2025-06-25 10:44:07,881 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.234
2025-06-25 10:44:07,881 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.234
2025-06-25 10:44:07,891 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.244
2025-06-25 10:44:07,893 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.239
2025-06-25 10:44:07,893 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:07] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.241
2025-06-25 10:44:08,197 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.006
2025-06-25 10:44:08,198 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.007
2025-06-25 10:44:08,204 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.013
2025-06-25 10:44:08,219 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.012
2025-06-25 10:44:08,220 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.012
2025-06-25 10:44:08,221 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:08] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.008
2025-06-25 10:44:16,280 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.023
2025-06-25 10:44:16,281 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.020
2025-06-25 10:44:16,281 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.015
2025-06-25 10:44:16,281 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.023
2025-06-25 10:44:16,285 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.016
2025-06-25 10:44:16,292 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.018 0.019
2025-06-25 10:44:16,621 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.012
2025-06-25 10:44:16,625 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.011
2025-06-25 10:44:16,625 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.014
2025-06-25 10:44:16,629 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.016
2025-06-25 10:44:16,635 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.016
2025-06-25 10:44:16,636 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.022
2025-06-25 10:44:16,944 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:16] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.005
2025-06-25 10:44:23,746 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:23] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 10 0.015 0.016
2025-06-25 10:44:24,056 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:24] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 39 0.023 0.055
2025-06-25 10:44:24,078 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:24] "POST /website/get_seo_data HTTP/1.1" 200 - 8 0.008 0.004
2025-06-25 10:44:24,542 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:24] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.001 0.004
2025-06-25 10:44:31,994 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:31] "GET / HTTP/1.1" 200 - 34 0.023 0.060
2025-06-25 10:44:32,222 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:32] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 37 0.031 0.050
2025-06-25 10:44:38,265 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /shop HTTP/1.1" 200 - 157 0.110 0.399
2025-06-25 10:44:38,366 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 37 0.022 0.054
2025-06-25 10:44:38,819 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /web/image/product.template/4/image_512/Bacon%20Burger?unique=92adb9b HTTP/1.1" 200 - 12 0.017 0.056
2025-06-25 10:44:38,827 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /web/image/product.template/6/image_512/Pizza%20Margherita?unique=92adb9b HTTP/1.1" 200 - 7 0.052 0.030
2025-06-25 10:44:38,830 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /web/image/product.template/80/image_512/12314?unique=92adb9b HTTP/1.1" 200 - 11 0.014 0.071
2025-06-25 10:44:38,836 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /web/image/product.template/78/image_512/123?unique=92adb9b HTTP/1.1" 200 - 6 0.057 0.032
2025-06-25 10:44:38,837 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:38] "GET /web/image/product.template/5/image_512/Cheese%20Burger?unique=92adb9b HTTP/1.1" 200 - 7 0.061 0.028
2025-06-25 10:44:39,169 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/71/image_512/shopee?unique=92adb9b HTTP/1.1" 200 - 6 0.009 0.020
2025-06-25 10:44:39,193 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/19/image_512/Coca-Cola?unique=92adb9b HTTP/1.1" 200 - 7 0.007 0.014
2025-06-25 10:44:39,196 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/20/image_512/Water?unique=92adb9b HTTP/1.1" 200 - 7 0.006 0.015
2025-06-25 10:44:39,487 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/7/image_512/Pizza%20Vegetarian?unique=92adb9b HTTP/1.1" 200 - 7 0.003 0.008
2025-06-25 10:44:39,530 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/9/image_512/Funghi?unique=92adb9b HTTP/1.1" 200 - 7 0.010 0.015
2025-06-25 10:44:39,542 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/8/image_512/Pasta%204%20Formaggi?unique=92adb9b HTTP/1.1" 200 - 7 0.004 0.047
2025-06-25 10:44:39,546 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/83/image_512/Event%20Registration?unique=92adb9b HTTP/1.1" 200 - 6 0.006 0.016
2025-06-25 10:44:39,812 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/10/image_512/Pasta%20Bolognese?unique=92adb9b HTTP/1.1" 200 - 7 0.006 0.006
2025-06-25 10:44:39,894 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:39] "GET /web/image/product.template/11/image_512/Chicken%20Curry%20Sandwich?unique=92adb9b HTTP/1.1" 200 - 7 0.006 0.042
2025-06-25 10:44:54,807 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:54] "GET /blog/2 HTTP/1.1" 301 - 7 0.004 0.006
2025-06-25 10:44:55,177 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:55] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 39 0.013 0.058
2025-06-25 10:44:55,571 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:44:55] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 37 0.018 0.049
2025-06-25 10:58:36,027 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:36] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 296 0.193 0.181
2025-06-25 10:58:41,418 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:41] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 10:58:41,722 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:41] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.000 0.007
2025-06-25 10:58:41,727 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:41] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.006 0.005
2025-06-25 10:58:42,043 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.002 0.003
2025-06-25 10:58:42,047 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.005 0.004
2025-06-25 10:58:42,543 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.175
2025-06-25 10:58:42,544 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.171
2025-06-25 10:58:42,544 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.171
2025-06-25 10:58:42,555 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.180
2025-06-25 10:58:42,557 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.016 0.178
2025-06-25 10:58:42,558 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.021 0.176
2025-06-25 10:58:42,863 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.009
2025-06-25 10:58:42,863 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.006
2025-06-25 10:58:42,864 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.010
2025-06-25 10:58:42,905 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.009
2025-06-25 10:58:42,908 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.009
2025-06-25 10:58:42,910 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:42] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.011
2025-06-25 10:58:43,174 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:43] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 10:58:53,566 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.013
2025-06-25 10:58:53,571 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.018
2025-06-25 10:58:53,577 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.019
2025-06-25 10:58:53,577 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.018
2025-06-25 10:58:53,579 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.019
2025-06-25 10:58:53,582 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.021
2025-06-25 10:58:53,904 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.013
2025-06-25 10:58:53,904 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.009
2025-06-25 10:58:53,904 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.012
2025-06-25 10:58:53,905 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.013 0.007
2025-06-25 10:58:53,918 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.014
2025-06-25 10:58:53,922 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:53] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.008
2025-06-25 10:58:54,229 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:58:54] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 10:59:04,178 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:04] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 7 0.012 0.033
2025-06-25 10:59:04,462 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:04] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 39 0.020 0.062
2025-06-25 10:59:04,513 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:04] "POST /website/get_seo_data HTTP/1.1" 200 - 8 0.004 0.005
2025-06-25 10:59:04,742 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:04] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.002 0.002
2025-06-25 10:59:26,603 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:26] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 39 0.020 0.057
2025-06-25 10:59:39,886 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 10:59:39] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 37 0.026 0.043
2025-06-25 11:04:23,854 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 11:04:24,182 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:24] "POST /website/get_switchable_related_views HTTP/1.1" 200 - 297 0.134 0.199
2025-06-25 11:04:26,871 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:26] "POST /web/dataset/call_kw/utm.campaign/name_search HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 11:04:27,175 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /web/dataset/call_kw/utm.medium/name_search HTTP/1.1" 200 - 4 0.003 0.004
2025-06-25 11:04:27,176 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /web/dataset/call_kw/utm.source/name_search HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 11:04:27,255 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 11:04:27,286 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.031s 
2025-06-25 11:04:27,289 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 11:04:27,293 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 11:04:27,492 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /web/dataset/call_kw/utm.medium/read HTTP/1.1" 200 - 4 0.002 0.004
2025-06-25 11:04:27,495 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /web/dataset/call_kw/utm.source/read HTTP/1.1" 200 - 4 0.003 0.005
2025-06-25 11:04:27,751 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.006
2025-06-25 11:04:27,845 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.015
2025-06-25 11:04:27,845 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.018
2025-06-25 11:04:27,846 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.012
2025-06-25 11:04:27,854 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.025
2025-06-25 11:04:27,858 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:27] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.026
2025-06-25 11:04:28,071 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.005
2025-06-25 11:04:28,170 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.006 0.011
2025-06-25 11:04:28,170 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.009
2025-06-25 11:04:28,171 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.005
2025-06-25 11:04:28,185 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.003 0.010
2025-06-25 11:04:28,185 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.006
2025-06-25 11:04:28,389 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:28] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.004 0.003
2025-06-25 11:04:35,195 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.016
2025-06-25 11:04:35,196 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.015
2025-06-25 11:04:35,206 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.009 0.023
2025-06-25 11:04:35,206 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.012 0.021
2025-06-25 11:04:35,211 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.025
2025-06-25 11:04:35,212 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.010 0.026
2025-06-25 11:04:35,517 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.002 0.008
2025-06-25 11:04:35,520 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.006
2025-06-25 11:04:35,541 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.005 0.015
2025-06-25 11:04:35,543 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.008 0.014
2025-06-25 11:04:35,545 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.007 0.015
2025-06-25 11:04:35,545 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.011 0.009
2025-06-25 11:04:35,838 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:35] "POST /website/theme_customize_data_get HTTP/1.1" 200 - 7 0.001 0.007
2025-06-25 11:04:43,278 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:43] "POST /web/dataset/call_kw/ir.ui.view/save HTTP/1.1" 200 - 7 0.014 0.073
2025-06-25 11:04:43,545 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:43] "GET /blog/tin-tuc-2 HTTP/1.1" 200 - 39 0.016 0.078
2025-06-25 11:04:43,611 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:43] "POST /website/get_seo_data HTTP/1.1" 200 - 8 0.004 0.008
2025-06-25 11:04:44,036 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:04:44] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 3 0.003 0.004
2025-06-25 11:19:36,391 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-25 11:19:36,540 25596 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 13 checked, 5 removed 
2025-06-25 11:19:36,566 25596 INFO ecomplus odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667] 
2025-06-25 11:19:36,705 25596 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-25 11:19:36,727 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-25 11:19:36,736 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 3 entries 
2025-06-25 11:19:36,738 25596 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-25 11:19:36,748 25596 INFO ecomplus odoo.models.unlink: User #1 deleted base.module.update records with IDs: [21] 
2025-06-25 11:19:36,766 25596 INFO ecomplus odoo.models.unlink: User #1 deleted base.module.uninstall records with IDs: [8] 
2025-06-25 11:19:36,787 25596 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-25 11:19:36,806 25596 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946] 
2025-06-25 11:19:37,039 25596 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-25 11:19:37,612 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 1.221s 
2025-06-25 11:19:37,615 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-25 11:19:37,619 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-25 11:19:37,626 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-06-25 11:19:37,634 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) done in 0.008s 
2025-06-25 11:19:37,636 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) processed 0 records, 0 records remaining 
2025-06-25 11:19:37,641 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) completed 
2025-06-25 11:20:37,663 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Delete Notifications older than 6 Month' (5) starting 
2025-06-25 11:20:37,684 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Delete Notifications older than 6 Month' (5) done in 0.021s 
2025-06-25 11:20:37,686 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Delete Notifications older than 6 Month' (5) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,690 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Delete Notifications older than 6 Month' (5) completed 
2025-06-25 11:20:37,696 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Post scheduled messages' (7) starting 
2025-06-25 11:20:37,702 25596 INFO ecomplus odoo.addons.mail.models.mail_scheduled_message: Posting 0 scheduled messages 
2025-06-25 11:20:37,703 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Post scheduled messages' (7) done in 0.008s 
2025-06-25 11:20:37,706 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Post scheduled messages' (7) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,709 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Post scheduled messages' (7) completed 
2025-06-25 11:20:37,716 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 11:20:37,720 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.004s 
2025-06-25 11:20:37,723 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,726 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 11:20:37,734 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: send web push notification' (9) starting 
2025-06-25 11:20:37,742 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: send web push notification' (9) done in 0.007s 
2025-06-25 11:20:37,744 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: send web push notification' (9) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,749 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: send web push notification' (9) completed 
2025-06-25 11:20:37,755 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: channel member unmute' (10) starting 
2025-06-25 11:20:37,758 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: channel member unmute' (10) done in 0.004s 
2025-06-25 11:20:37,762 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: channel member unmute' (10) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,766 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: channel member unmute' (10) completed 
2025-06-25 11:20:37,772 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: users settings unmute' (11) starting 
2025-06-25 11:20:37,779 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: users settings unmute' (11) done in 0.005s 
2025-06-25 11:20:37,781 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: users settings unmute' (11) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,784 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Discuss: users settings unmute' (11) completed 
2025-06-25 11:20:37,793 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 11:20:37,798 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.004s 
2025-06-25 11:20:37,800 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,805 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 11:20:37,812 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 11:20:37,818 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.006s 
2025-06-25 11:20:37,821 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,825 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 11:20:37,831 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Procurement: run scheduler' (17) starting 
2025-06-25 11:20:37,903 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Procurement: run scheduler' (17) done in 0.071s 
2025-06-25 11:20:37,906 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Procurement: run scheduler' (17) processed 4 records, 0 records remaining 
2025-06-25 11:20:37,910 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Procurement: run scheduler' (17) completed 
2025-06-25 11:20:37,917 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 11:20:37,926 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.009s 
2025-06-25 11:20:37,928 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,934 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 11:20:37,941 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Users: Notify About Unregistered Users' (12) starting 
2025-06-25 11:20:37,951 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Users: Notify About Unregistered Users' (12) done in 0.011s 
2025-06-25 11:20:37,954 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Users: Notify About Unregistered Users' (12) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,958 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Users: Notify About Unregistered Users' (12) completed 
2025-06-25 11:20:37,965 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 11:20:37,968 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-25 11:20:37,970 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 11:20:37,974 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 11:20:43,655 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Gamification: Goal Challenge Check' (18) starting 
2025-06-25 11:20:43,776 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Gamification: Goal Challenge Check' (18) done in 0.121s 
2025-06-25 11:20:43,779 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Gamification: Goal Challenge Check' (18) processed 0 records, 0 records remaining 
2025-06-25 11:20:43,783 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Gamification: Goal Challenge Check' (18) completed 
2025-06-25 11:21:37,988 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'HR Employee: check work permit validity' (20) starting 
2025-06-25 11:21:38,007 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'HR Employee: check work permit validity' (20) done in 0.020s 
2025-06-25 11:21:38,010 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'HR Employee: check work permit validity' (20) processed 0 records, 0 records remaining 
2025-06-25 11:21:38,013 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'HR Employee: check work permit validity' (20) completed 
2025-06-25 11:21:38,021 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Project: Send rating' (23) starting 
2025-06-25 11:21:38,046 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Project: Send rating' (23) done in 0.026s 
2025-06-25 11:21:38,050 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Project: Send rating' (23) processed 0 records, 0 records remaining 
2025-06-25 11:21:38,053 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Project: Send rating' (23) completed 
2025-06-25 11:21:38,060 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail Marketing: Process queue' (21) starting 
2025-06-25 11:21:38,067 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail Marketing: Process queue' (21) done in 0.008s 
2025-06-25 11:21:38,070 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail Marketing: Process queue' (21) processed 0 records, 0 records remaining 
2025-06-25 11:21:38,074 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail Marketing: Process queue' (21) completed 
2025-06-25 11:22:38,091 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Send invoices automatically' (26) starting 
2025-06-25 11:22:38,112 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Send invoices automatically' (26) done in 0.020s 
2025-06-25 11:22:38,114 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Send invoices automatically' (26) processed 0 records, 0 records remaining 
2025-06-25 11:22:38,117 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Send invoices automatically' (26) completed 
2025-06-25 11:22:38,123 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Sales: Send pending emails' (28) starting 
2025-06-25 11:22:38,134 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Sales: Send pending emails' (28) done in 0.010s 
2025-06-25 11:22:38,136 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Sales: Send pending emails' (28) processed 0 records, 0 records remaining 
2025-06-25 11:22:38,140 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Sales: Send pending emails' (28) completed 
2025-06-25 11:29:38,194 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 11:29:38,203 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.009s 
2025-06-25 11:29:38,206 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 11:29:38,210 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 11:29:38,217 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 11:29:38,224 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.008s 
2025-06-25 11:29:38,227 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 11:29:38,230 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 11:30:33,395 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:30:33] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.008
2025-06-25 11:30:33,739 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 11:30:33] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.005 0.008
2025-06-25 11:43:38,471 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 11:43:38,486 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.015s 
2025-06-25 11:43:38,489 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 11:43:38,493 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 12:04:27,737 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 12:04:27,770 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.032s 
2025-06-25 12:04:27,774 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 12:04:27,778 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 12:20:39,062 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 12:20:39,075 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.012s 
2025-06-25 12:20:39,078 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 12:20:39,081 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 12:20:39,087 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 12:20:39,092 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.005s 
2025-06-25 12:20:39,096 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 12:20:39,098 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 12:20:39,105 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 12:20:39,111 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.006s 
2025-06-25 12:20:39,113 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 12:20:39,117 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 12:20:39,124 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 12:20:39,132 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.008s 
2025-06-25 12:20:39,136 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 12:20:39,140 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 12:20:39,147 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 12:20:39,150 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-25 12:20:39,152 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 12:20:39,155 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 12:29:39,278 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 12:29:39,287 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.008s 
2025-06-25 12:29:39,290 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 12:29:39,293 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 12:29:39,299 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 12:29:39,316 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.017s 
2025-06-25 12:29:39,319 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 12:29:39,322 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 12:43:21,018 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 12:43:21,021 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 12:43:21] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 3 0.003 0.042
2025-06-25 12:43:39,600 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 12:43:39,616 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.016s 
2025-06-25 12:43:39,621 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 12:43:39,624 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 13:04:28,082 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-25 13:04:28,114 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.032s 
2025-06-25 13:04:28,118 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-25 13:04:28,121 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-25 13:20:40,086 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-25 13:20:40,102 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.015s 
2025-06-25 13:20:40,106 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-25 13:20:40,110 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-25 13:20:40,116 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-25 13:20:40,123 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.007s 
2025-06-25 13:20:40,127 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-25 13:20:40,130 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-25 13:20:40,135 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-25 13:20:40,143 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.007s 
2025-06-25 13:20:40,145 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-25 13:20:40,149 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-25 13:20:40,157 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Digest Emails' (16) starting 
2025-06-25 13:20:41,740 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Digest Emails' (16) done in 1.584s 
2025-06-25 13:20:41,743 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Digest Emails' (16) processed 0 records, 0 records remaining 
2025-06-25 13:20:41,746 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Digest Emails' (16) completed 
2025-06-25 13:20:41,754 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-25 13:20:44,601 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-25 13:20:44,610 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.009s 
2025-06-25 13:20:44,613 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-25 13:20:44,631 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-25 13:20:45,822 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 4.068s 
2025-06-25 13:20:45,866 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-25 13:20:45,870 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-25 13:29:45,991 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-25 13:29:45,995 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.004s 
2025-06-25 13:29:45,998 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-25 13:29:46,002 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-25 13:29:46,010 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-25 13:29:46,017 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.007s 
2025-06-25 13:29:46,021 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-25 13:29:46,024 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-25 13:43:46,401 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 13:43:46,417 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.016s 
2025-06-25 13:43:46,419 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 13:43:46,423 25596 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 13:48:38,010 25596 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (9Dym9PID2AaI1cUWmpdI9_okYJGFTypKX3TtJ1K3ny) 
2025-06-25 13:48:38,015 25596 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 13:48:38] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 3 0.002 0.031
