2025-06-25 16:53:39,501 8024 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 16:53:39,509 8024 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 16:53:39,545 8024 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-25 16:53:46,799 13732 INFO ? odoo: Odoo version 18.0-20250519 
2025-06-25 16:53:46,799 13732 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.20250519\server\odoo.conf 
2025-06-25 16:53:46,799 13732 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.20250519\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.20250519\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.20250519\\addons', 'c:\\program files\\odoo 18.0.20250519\\server\\odoo\\addons'] 
2025-06-25 16:53:46,799 13732 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 16:53:47,161 13732 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.20250519\thirdparty\wkhtmltopdf.exe 
2025-06-25 16:53:47,195 13732 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 16:53:47,491 23848 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 16:53:47,502 23848 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 16:53:47,510 23848 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 16:53:47,511 23848 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 16:53:47,524 23848 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 16:53:47] "GET / HTTP/1.1" 500 - 11 0.007 0.036
2025-06-25 16:54:00,212 13732 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 16:55:06,320 23848 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 16:55:06,341 23848 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-06-25 16:55:06,353 23848 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-06-25 16:55:06,354 23848 ERROR ? odoo.modules.registry: Failed to load registry 
2025-06-25 16:55:06,377 23848 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 16:55:06] "GET / HTTP/1.1" 500 - 11 0.014 0.061
