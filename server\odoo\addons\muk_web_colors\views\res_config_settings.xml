<?xml version="1.0" encoding="UTF-8"?>

<odoo>

	<record id="view_res_config_settings_form" model="ir.ui.view">
	    <field name="name">res.config.settings.form</field>
	    <field name="model">res.config.settings</field>
	    <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
	    <field name="arch" type="xml">
	    	<xpath expr="//block[@id='user_default_rights']" position="before">
	    		<block title="Branding" id="branding_settings">
	    			<setting string="Light Mode Colors" help="Customize the look and feel of the light mode">
                     	<div class="w-50 row">
                            <label for="color_brand_light" string="Brand" class="d-block w-75 py-2"/>
                            <field name="color_brand_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_primary_light" string="Primary" class="d-block w-75 py-2"/>
                            <field name="color_primary_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_info_light" string="Info" class="d-block w-75 py-2"/>
                            <field name="color_info_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_success_light" string="Success" class="d-block w-75 py-2"/>
                            <field name="color_success_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_warning_light" string="Warning" class="d-block w-75 py-2"/>
                            <field name="color_warning_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_danger_light" string="Danger" class="d-block w-75 py-2"/>
                            <field name="color_danger_light" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                        <button 
                            name="action_reset_light_color_assets" 
                            icon="oi-arrow-right" 
                            type="object" 
                            string="Reset Light Colors" 
                            class="btn-link"
                        />
                    </setting>
	    			<setting string="Dark Mode Colors" help="Customize the look and feel of the dark mode">
                     	<div class="w-50 row">
                            <label for="color_brand_dark" string="Brand" class="d-block w-75 py-2"/>
                            <field name="color_brand_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_primary_dark" string="Primary" class="d-block w-75 py-2"/>
                            <field name="color_primary_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_info_dark" string="Info" class="d-block w-75 py-2"/>
                            <field name="color_info_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_success_dark" string="Success" class="d-block w-75 py-2"/>
                            <field name="color_success_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_warning_dark" string="Warning" class="d-block w-75 py-2"/>
                            <field name="color_warning_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                     	<div class="w-50 row">
                            <label for="color_danger_dark" string="Danger" class="d-block w-75 py-2"/>
                            <field name="color_danger_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                        </div>
                        <button 
                            name="action_reset_dark_color_assets" 
                            icon="oi-arrow-right" 
                            type="object" 
                            string="Reset Dark Colors" 
                            class="btn-link"
                        />
                    </setting>
	    		</block>
	    	</xpath>
	    </field>
	</record>
	
</odoo>
