# SOME DESCRIPTIVE TITLE.
# Copyright (C) Odoo S.A.
# This file is distributed under the same license as the Odoo package.
# <AUTHOR> <EMAIL>, YEAR.
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-30 14:37+0000\n"
"PO-Revision-Date: 2024-10-08 06:34+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: ../../content/applications/websites.rst:5
msgid "Websites"
msgstr "المواقع الإلكترونية "

#: ../../content/applications/websites/blog.rst:3
msgid "Blog"
msgstr "المدونة"

#: ../../content/applications/websites/blog.rst:5
msgid ""
"**Odoo Blog** allows you to create and manage blog posts on your website to "
"engage your audience and build a community."
msgstr ""

#: ../../content/applications/websites/blog.rst:9
msgid ""
"If the Blog module is not yet installed, click :guilabel:`+New` on the "
"website builder, select :guilabel:`Blog Post`, and click "
":guilabel:`Install`."
msgstr ""

#: ../../content/applications/websites/blog.rst:13
msgid "Creating a blog"
msgstr ""

#: ../../content/applications/websites/blog.rst:15
msgid ""
"To create or edit a blog, go to :menuselection:`Website --> Configuration "
"--> Blogs: Blogs`. Click :guilabel:`New`, and enter the :guilabel:`Blog "
"Name` and the :guilabel:`Blog Subtitle`."
msgstr ""

#: ../../content/applications/websites/blog.rst:18
msgid ""
"The :guilabel:`Blog` menu item is added to your website's menu the first "
"time you create a blog and gathers all your blogs."
msgstr ""

#: ../../content/applications/websites/blog.rst:22
msgid "Adding a blog post"
msgstr ""

#: ../../content/applications/websites/blog.rst:24
msgid ""
"Go to your website, click :guilabel:`+New` in the top-right corner, and "
"select :guilabel:`Blog Post`. In the pop-up, **select the blog** where the "
"post should appear, write the post's :guilabel:`Title`, and "
":guilabel:`Save`. You can then write the post's content and customize the "
"page using the website builder."
msgstr ""

#: ../../content/applications/websites/blog.rst:30
msgid ""
"Illustrate your articles with copyright-free images from :doc:`Unsplash "
"</applications/general/integrations/unsplash>`."
msgstr ""

#: ../../content/applications/websites/blog.rst:32
msgid "Type `/` in the text editor to format and add elements to your text."
msgstr ""

#: ../../content/applications/websites/blog.rst:35
msgid ""
"Don't forget to toggle the :guilabel:`Unpublished` switch in the top-right "
"corner to publish your post."
msgstr ""

#: ../../content/applications/websites/blog.rst:39
msgid "Using tags"
msgstr ""

#: ../../content/applications/websites/blog.rst:41
msgid ""
"Tags let visitors filter all posts sharing the same tag. By default, they "
"are displayed at the bottom of posts, but can also be displayed on the "
"blog's main page. To do so, click :menuselection:`Edit --> Customize` and "
"enable the :guilabel:`Sidebar`. By default, the sidebar's :guilabel:`Tags "
"List` is enabled."
msgstr ""

#: ../../content/applications/websites/blog.rst:46
msgid ""
"To create a tag, go to :menuselection:`Website --> Configuration --> Blogs: "
"Tags` and click :guilabel:`New`. Fill in the:"
msgstr ""

#: ../../content/applications/websites/blog.rst:49
msgid ":guilabel:`Name`"
msgstr ":guilabel:`Name`"

#: ../../content/applications/websites/blog.rst:50
msgid ""
":guilabel:`Category`: tag categories let you group tags displayed on the "
"sidebar by theme."
msgstr ""

#: ../../content/applications/websites/blog.rst:51
msgid ""
":guilabel:`Used in`: to apply the tag to existing blog posts, click "
":guilabel:`Add a line`, select the posts, and click :guilabel:`Select`."
msgstr ""

#: ../../content/applications/websites/blog.rst:54
msgid ""
"You can add and create tags directly from posts by clicking "
":menuselection:`Edit --> Customize` and select the post's cover. Under "
":guilabel:`Tags`, click :guilabel:`Choose a record...`, and select or create"
" a tag."
msgstr ""

#: ../../content/applications/websites/blog.rst-1
msgid "Adding a tag to a blog post"
msgstr ""

#: ../../content/applications/websites/blog.rst:62
msgid ""
"To manage tag categories, go to :menuselection:`Website --> Configuration "
"--> Blogs: Tag Categories`."
msgstr ""

#: ../../content/applications/websites/blog.rst:66
msgid "Customizing blog homepages"
msgstr ""

#: ../../content/applications/websites/blog.rst:68
msgid ""
"Customize the content of blog homepages by opening a blog homepage and "
"clicking :menuselection:`Edit --> Customize`."
msgstr ""

#: ../../content/applications/websites/blog.rst:72
msgid "Settings apply to **all** blogs homepages."
msgstr ""

#: ../../content/applications/websites/blog.rst:74
msgid ""
":guilabel:`Top Banner`: :guilabel:`Name/Latest Post` displays the title of "
"the latest post on the top banner, while :guilabel:`Drop Zone for Building "
"Blocks` removes the top banner and lets you use any building block instead."
msgstr ""

#: ../../content/applications/websites/blog.rst:78
msgid ""
":guilabel:`Layout`: organizes posts as a :guilabel:`Grid` or "
":guilabel:`List`."
msgstr ""

#: ../../content/applications/websites/blog.rst:80
msgid ":guilabel:`Cards`: adds a *card* effect."
msgstr ""

#: ../../content/applications/websites/blog.rst:81
msgid ":guilabel:`Increase Readability`: improves the text's readability."
msgstr ""

#: ../../content/applications/websites/blog.rst:83
msgid ""
":guilabel:`Sidebar`: displays a sidebar containing an :guilabel:`About us` "
"section."
msgstr ""

#: ../../content/applications/websites/blog.rst:85
msgid ""
":guilabel:`Archives`: allows visitors to select a month and filter all posts"
" created during that month."
msgstr ""

#: ../../content/applications/websites/blog.rst:87
msgid ""
":guilabel:`Follow Us`: displays links to your social media networks. They "
"can be configured using the Social Media building block somewhere on your "
"website."
msgstr ""

#: ../../content/applications/websites/blog.rst:89
msgid ""
":guilabel:`Tags List`: displays all tags related to a blog. Visitors can "
"select a tag to filter all related posts."
msgstr ""

#: ../../content/applications/websites/blog.rst:92
msgid ""
":guilabel:`Posts List`: :guilabel:`Cover` displays the posts' images, and "
":guilabel:`No Cover` hides them."
msgstr ""

#: ../../content/applications/websites/blog.rst:95
msgid ":guilabel:`Author`: displays the posts' authors."
msgstr ""

#: ../../content/applications/websites/blog.rst:96
msgid ""
":guilabel:`Comments/Views Stats`: displays the posts' number of comments and"
" views."
msgstr ""

#: ../../content/applications/websites/blog.rst:97
msgid ""
":guilabel:`Teaser & Tags`: displays the posts' first sentences and tags."
msgstr ""

#: ../../content/applications/websites/blog.rst:100
msgid "Customizing blog posts"
msgstr ""

#: ../../content/applications/websites/blog.rst:102
msgid ""
"Customize posts by opening a blog post and clicking :menuselection:`Edit -->"
" Customize`."
msgstr ""

#: ../../content/applications/websites/blog.rst:105
msgid "Settings apply to **all** posts."
msgstr ""

#: ../../content/applications/websites/blog.rst:107
msgid ""
":guilabel:`Layout`: :guilabel:`Title Inside Cover` displays the title inside"
" the cover image, and :guilabel:`Title above Cover` displays it above."
msgstr ""

#: ../../content/applications/websites/blog.rst:110
msgid ":guilabel:`Increase Readability`: increases the text's readability."
msgstr ""

#: ../../content/applications/websites/blog.rst:112
msgid ""
":guilabel:`Sidebar`: displays the :guilabel:`Sidebar` and additional "
"options:"
msgstr ""

#: ../../content/applications/websites/blog.rst:114
msgid ""
":guilabel:`Archive`: allows visitors to select a month and filter all posts "
"created during that month."
msgstr ""

#: ../../content/applications/websites/blog.rst:116
msgid ":guilabel:`Author`: displays the post's author and creation date."
msgstr ""

#: ../../content/applications/websites/blog.rst:117
msgid ":guilabel:`Blog List`: displays links to all your blogs."
msgstr ""

#: ../../content/applications/websites/blog.rst:118
msgid ""
":guilabel:`Share Links`: displays share buttons to several social networks."
msgstr ""

#: ../../content/applications/websites/blog.rst:119
msgid ":guilabel:`Tags`: displays the post's tags."
msgstr ""

#: ../../content/applications/websites/blog.rst:121
msgid ":guilabel:`Breadcrumb`: displays the path to the post."
msgstr ""

#: ../../content/applications/websites/blog.rst:123
msgid ""
":guilabel:`Bottom`: :guilabel:`Next Article` displays the next post at the "
"bottom, and :guilabel:`Comments` enable visitors to comment on the post."
msgstr ""

#: ../../content/applications/websites/blog.rst:126
msgid ""
":guilabel:`Select To Tweet`: visitors are offered to tweet the text they "
"select."
msgstr ""

#: ../../content/applications/websites/blog.rst:129
msgid ""
"Use :ref:`Plausible <analytics/plausible>` to keep track of the traffic on "
"your blog."
msgstr ""

#: ../../content/applications/websites/ecommerce.rst:8
msgid "eCommerce"
msgstr "المتجر الإلكتروني"

#: ../../content/applications/websites/ecommerce.rst:10
msgid ""
"Run a modern open-source online store with Odoo eCommerce. Learn how to sell"
" online, promote products and increase your average cart sizes."
msgstr ""

#: ../../content/applications/websites/ecommerce.rst:14
#: ../../content/applications/websites/website.rst:47
msgid ""
"Odoo offers a :ref:`free custom domain name <domain-name/register>` to all "
"Odoo Online databases for one year. Visitors can then access your website "
"with an address such as `www.example.com` rather than the default "
"`example.odoo.com`."
msgstr ""

#: ../../content/applications/websites/ecommerce.rst:19
msgid ":doc:`Website Documentation <website>`"
msgstr ""

#: ../../content/applications/websites/ecommerce.rst:20
#: ../../content/applications/websites/website.rst:52
msgid "`Odoo Tutorials: Website <https://www.odoo.com/slides/website-25>`_"
msgstr ""

#: ../../content/applications/websites/ecommerce.rst:21
#: ../../content/applications/websites/website.rst:53
msgid ""
"`Odoo Tutorials: eCommerce <https://www.odoo.com/slides/ecommerce-26>`_"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:3
msgid "Add to cart"
msgstr "إضافة إلى عربة التسوق "

#: ../../content/applications/websites/ecommerce/cart.rst:5
msgid ""
"The :guilabel:`Add to Cart` button can be customized in multiple ways. You "
"can:"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:7
msgid ""
"Choose on which page customers go after clicking the 'Add to Cart' button;"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:8
msgid "Hide the 'Add to Cart' button to prevent sales;"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:9
msgid ""
"Add a 'Buy Now' button to skip the cart step and lead customers straight to "
"checkout;"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:10
msgid "Create additional 'Add to Cart / Buy Now' buttons;"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:11
msgid "Add an 'Order Again' button to the customer portal."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:14
#: ../../content/applications/websites/ecommerce/payments.rst:11
msgid ":doc:`checkout`"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:19
msgid "'Add to Cart' action customization"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:21
msgid ""
"When customers click on the :guilabel:`Add to Cart` button, the product is "
"added to their cart, and customers remain **by default** on the product's "
"page. However, customers can either immediately be **redirected** to their "
"cart, or given the choice on what to do through a **dialog box**."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:25
msgid ""
"To change the default behavior, go to :menuselection:`Website --> "
"Configuration --> Settings`. Under the :guilabel:`Shop - Checkout Process` "
"section, look for :guilabel:`Add to Cart` and select one of the options."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:30
msgid ""
"If a product has :doc:`optional products <products/cross_upselling>`, the "
"**dialog box** will always appear."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:34
msgid ":doc:`products/catalog`"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:39
msgid "Replace 'Add to Cart' button by 'Contact Us' button"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:41
msgid ""
"You can replace the 'Add to Cart' button with a 'Contact Us' button which "
"redirects users to the URL of your choice."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:45
msgid ""
"Hiding the :guilabel:`Add to Cart` button is often used by B2B eCommerces "
"that need to restrict purchases only to :ref:`customers with an account "
"<ecommerce/checkout/policy>`, but still want to display an online product "
"catalog for those without."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:49
msgid ""
"To do so, go to :menuselection:`Website --> Configuration --> Settings --> "
"Shop - Products` and tick :guilabel:`Prevent Sale of Zero Priced Product`. "
"This creates a new :guilabel:`Button url` field where you can enter the "
"**redirect URL** to be used. Then, set the price of the product to `0.00` "
"either from the **product's template**, or from a :doc:`pricelist "
"</applications/sales/sales/products_prices/prices/pricing>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst-1
msgid "Contact us button on product page"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:60
msgid ""
"The 'Contact Us' button and '*Not Available For Sale*' text can both be "
"modified using the **website builder** on the product's page "
"(:menuselection:`Edit --> Customize`) by clicking on them."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:65
#: ../../content/applications/websites/ecommerce/cart.rst-1
msgid "Customizable 'Add to Cart' button"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:67
msgid ""
"You can also create a customizable 'Add to Cart' button and link it to a "
"specific product. The **customized button** can be added on any page of the "
"website as an **inner content** building block, and is an *additional* "
"button to the regular :guilabel:`Add to Cart` button."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:71
msgid ""
"To add it, go on the :guilabel:`Shop` page of your choice, click "
":menuselection:`Edit --> Blocks` and place the building block. Once placed, "
"you have the following options:"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:74
msgid ""
":guilabel:`Product`: select the product to link the button with. Selecting a"
" product renders the :guilabel:`Action` field available;"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:76
msgid ""
":guilabel:`Action`: choose if the button should :guilabel:`Add to Cart` or "
":guilabel:`Buy Now` (instant checkout)."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:86
msgid "'Buy Now' button"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:88
msgid ""
"You can enable the :guilabel:`Buy Now` button to take customers directly to "
"the :ref:`review order <ecommerce/checkout/review_order>` step instead of "
"adding the product to the cart. This button is an *additional* option and "
"does not replace the :guilabel:`Add to Cart` button. To do so, go to "
":menuselection:`Website --> Configuration --> Settings`, scroll to the "
":guilabel:`Shop—Checkout Process` section, enable :guilabel:`Buy Now`, and "
":guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:95
msgid ""
"Alternatively, you can enable the :guilabel:`Buy Now` button directly from a"
" product page by clicking :guilabel:`Edit` and navigating to the "
":guilabel:`Customize` tab."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst-1
msgid "Buy Now button"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:102
msgid "Re-order from portal"
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst:104
msgid ""
"Customers have the possibility to **re-order** items from **previous sales "
"orders** on the customer portal. To do so, go to :menuselection:`Website -->"
" Configuration --> Settings --> Shop - Checkout Process` and enable "
":guilabel:`Re-order From Portal`. Customers can find the :guilabel:`Order "
"Again` button on their **sales order** from the **customer portal**."
msgstr ""

#: ../../content/applications/websites/ecommerce/cart.rst-1
msgid "Re-order button"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:3
msgid "Checkout"
msgstr "الدفع والخروج "

#: ../../content/applications/websites/ecommerce/checkout.rst:5
msgid ""
"Once customers have added their desired products to the cart, they can "
"access it by clicking the :icon:`fa-shopping-cart` button in the header to "
"start the checkout process. In Odoo eCommerce, this process consists of "
"sequential :ref:`steps <ecommerce/checkout/steps>`, some of which support "
"additional features. The related checkout pages can be :ref:`customized "
"<ecommerce/checkout/customize_steps>` using the website editor."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:14
msgid "Checkout policy"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:16
msgid ""
"To allow customers to checkout as guests or force them to sign in/create an "
"account, go to :menuselection:`Website --> Configuration --> Settings`, "
"scroll down to the :guilabel:`Shop - Checkout Process` section, and "
"configure the :guilabel:`Sign in/up at checkout` setting. The following "
"options are available:"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:21
msgid ""
":guilabel:`Optional`: Customers can check out as guests and register later "
"via the order confirmation email to track their order."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:23
msgid ""
":guilabel:`Disabled (buy as guest)`: Customers can checkout as guests "
"without creating an account."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:24
msgid ""
":guilabel:`Mandatory (no guest checkout)`: Customers must sign in or create "
"an account at the :ref:`Review Order <ecommerce/checkout/review_order>` step"
" to complete their purchase."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:28
msgid "B2B access management"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:30
msgid "To restrict checkout to selected B2B customers, follow these steps:"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:32
msgid ""
"Go to :menuselection:`Website --> Configuration --> Settings`, and in the "
":guilabel:`Shop - Checkout Process` section, enable the :ref:`Mandatory (no "
"guest checkout) <ecommerce/checkout/policy>` option."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:35
msgid ""
"Scroll down to the :guilabel:`Privacy` section, go to :guilabel:`Customer "
"Account`, and select :guilabel:`On invitation`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:37
msgid ""
"Go to :menuselection:`Website --> eCommerce --> Customers`, switch to the "
":guilabel:`List` view, and select the customers you wish to grant access to "
"your :doc:`portal </applications/general/users/portal>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:40
msgid ""
"Click the :icon:`fa-cog` :guilabel:`Actions` button, then :guilabel:`Grant "
"portal access`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:41
msgid ""
"Review the selected customers in the :guilabel:`Portal Access Management` "
"pop-up and click :guilabel:`Grant Access`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:44
msgid ""
"Once done, the relevant customers receive an email confirming their account "
"creation, including instructions on setting a password and activating their "
"account."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:48
msgid ""
"You can revoke access or re-invite a customer using the related buttons in "
"the :guilabel:`Portal Access Management` pop-up."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:50
msgid ""
"Users can only have one :doc:`portal access "
"</applications/general/users/portal>` per email."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:51
msgid ""
"Settings are website-specific, so you could set up a B2C website that allows"
" guest checkout and B2B website with mandatory sign-in."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:55
msgid ":doc:`Customer accounts <customer_accounts>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:56
msgid ":doc:`Portal access </applications/general/users/portal>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:61
msgid "Checkout steps"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:63
msgid ""
"During the checkout process, customers are taken through the following "
"steps:"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:65
msgid ":ref:`Review order <ecommerce/checkout/review_order>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:66
msgid ":ref:`Delivery <ecommerce/checkout/delivery>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:67
msgid ":ref:`Extra info (if enabled) <ecommerce/checkout/extra_step>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:68
msgid ":ref:`Payment <ecommerce/checkout/payment>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:69
msgid ":ref:`Order confirmation <ecommerce/checkout/order_confirmation>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:73
msgid ""
"Each step can be customized using the website editor: Click :guilabel:`Edit`"
" to add :doc:`building blocks <../website/web_design/building_blocks>` from "
"the :guilabel:`Blocks` tab or open to the :guilabel:`Customize` tab to "
"enable various checkout options."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:78
msgid "Content added through building blocks is **specific** to each step."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:83
msgid "Review order"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:85
msgid ""
"The :guilabel:`Review Order` step allows customers to see the items they "
"added to their cart, adjust quantities, or :guilabel:`Remove` products. "
"Information related to the product prices and taxes applied are also "
"displayed. Customers can then click the :guilabel:`Checkout` button to "
"continue to the :ref:`Delivery <ecommerce/checkout/delivery>` step."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:90
msgid ""
"Open the website editor to :ref:`enable "
"<ecommerce/checkout/customize_steps>` checkout options such as:"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:93
msgid ""
":guilabel:`Suggested Accessories`: to showcase :ref:`accessory products "
"<ecommerce/cross_upselling/accessory>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:95
msgid ""
":guilabel:`Promo Code`: to allow customers to redeem :ref:`gift cards "
"<ewallet_gift/gift-cards>` or apply :doc:`discount codes "
"</applications/sales/sales/products_prices/loyalty_discount>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:97
msgid ""
":guilabel:`Add to Wishlist`: To allow signed-in users to remove a product "
"from their cart and add it to their wishlist, go to :menuselection:`Website "
"--> Configuration --> Settings`, scroll to the :guilabel:`Shop - Products` "
"section, and enable :guilabel:`Wishlists`. The :guilabel:`Add to Wishlist` "
"option is then enabled by default in the website editor."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:103
msgid ""
"If a :doc:`fiscal position </applications/finance/fiscal_localizations>` is "
"detected automatically, the product tax is determined based on the "
"customer's IP address."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:105
msgid ""
"If the installed :doc:`payment provider "
"</applications/finance/payment_providers>` supports :ref:`express checkout "
"<payment_providers/express_checkout>`, a dedicated button is displayed, "
"allowing customers to go straight from the cart to the confirmation page "
"without filling out the contact form."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:113
#: ../../content/applications/websites/ecommerce/shipping.rst:3
msgid "Delivery"
msgstr "التوصيل "

#: ../../content/applications/websites/ecommerce/checkout.rst:115
msgid "Once they have reviewed their order:"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:117
msgid ""
"Unsigned-in customers are prompted to :guilabel:`Sign in` or enter their "
":guilabel:`Email address`, along with their delivery address and phone "
"details;"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:119
msgid ""
"Signed-in customers can select the appropriate :guilabel:`Delivery address`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:121
msgid ""
"They can then :doc:`choose a delivery method <shipping>`, select or enter "
"their :guilabel:`Billing Address` (or toggle the :guilabel:`Same as delivery"
" address` switch if the billing and delivery addresses are identical), and "
"click :guilabel:`Confirm` to proceed to the next step."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:126
msgid ""
"For B2B customers, you can also :ref:`enable "
"<ecommerce/checkout/customize_steps>` optional :guilabel:`VAT` and "
":guilabel:`Company name` fields by toggling the :guilabel:`Show B2B Fields` "
"option in the website editor."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:133
msgid "Extra info"
msgstr "معلومات إضافية "

#: ../../content/applications/websites/ecommerce/checkout.rst:135
msgid ""
"You can add an :guilabel:`Extra Info` step in the checkout process to "
"collect additional customer information through an online form, which is "
"then included in the :ref:`sales order <handling/sales>`. To do so "
":ref:`enable <ecommerce/checkout/customize_steps>` the :guilabel:`Extra "
"Step` option in the website editor. The form can be :ref:`customized "
"<website/dynamic_content/form>` as needed."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:142
msgid ""
"Alternatively, go to :menuselection:`Website --> Configuration --> "
"Settings`, scroll to the :guilabel:`Shop - Checkout Process` section, enable"
" :guilabel:`Extra Step During Checkout`, and click :guilabel:`Save`. Click "
":icon:`fa-arrow-right` :guilabel:`Configure Form` to customize the form."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:150
msgid "Payment"
msgstr "الدفع "

#: ../../content/applications/websites/ecommerce/checkout.rst:152
msgid ""
"At the :guilabel:`Payment` step, customers :guilabel:`Choose a payment "
"method`, enter their payment details, and click :guilabel:`Pay now`."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:155
msgid ""
"You can require customers to agree to your :doc:`terms and conditions "
"</applications/finance/accounting/customer_invoices/terms_conditions>` "
"before payment. To :ref:`enable <ecommerce/checkout/customize_steps>` this "
"option, go to the website editor and toggle the :guilabel:`Accept Terms & "
"Conditions` feature."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:161
msgid ""
"Enable the :ref:`developer mode <developer-mode>` and click the :icon:`fa-"
"bug` :guilabel:`bug` icon to display an :ref:`availability "
"<payment_providers/availability>` report for payment providers and payment "
"methods, which helps diagnose potential availability issues on the payment "
"form."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:169
msgid "Order confirmation"
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:171
msgid ""
"The final step of the checkout process is the :guilabel:`Order "
"confirmation`, which provides a summary of the customer's purchase details."
msgstr ""

#: ../../content/applications/websites/ecommerce/checkout.rst:175
msgid ":doc:`Order handling <order_handling>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:3
#: ../../content/applications/websites/website/configuration/multi_website.rst:124
msgid "Customer accounts"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:5
msgid ""
"Having customer accounts on an eCommerce allows customers to access all "
"their documents from a single place. To access their account, customers must"
" be **logged-in** on the eCommerce website, click on their **username** in "
"the top-right corner of the screen, and click :guilabel:`My Account`. From "
"there, customers can access their :guilabel:`quotations`, "
":guilabel:`orders`, :guilabel:`invoices`, etc."
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst-1
msgid "Customer account log-in"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:16
msgid ""
"Customers can only have an account if the :ref:`sign in/up at checkout "
"<ecommerce/checkout/policy>` option allows for account creation."
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:20
msgid ""
"Similarly to the rest of the website, the customer account page can be "
"customized with **content blocks** and other features through the **website "
"builder**."
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:24
msgid ":doc:`/applications/general/users/portal`"
msgstr ":doc:`/applications/general/users/portal`"

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:27
msgid "Access restriction"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:29
msgid ""
"It is possible to allow or restrict the documents to which customers have "
"access through the website builder. Log in your **own** account with your "
"Odoo database credentials, and go to :menuselection:`Edit --> Customize`. "
"From the website builder menu, enable or disable the documents customers can"
" have access to."
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst-1
msgid "Documents to which customers have access to from their account"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:39
msgid "Multi-website account"
msgstr ""

#: ../../content/applications/websites/ecommerce/customer_accounts.rst:41
msgid ""
"If you own multiple websites, you can make customer accounts available "
"across **all** websites. Then, the customer only needs one account. To do "
"so, go to :menuselection:`Website --> Configuration --> Settings --> Privacy"
" section`, and enable :guilabel:`Shared Customer Accounts`."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:3
msgid "Order handling"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:5
msgid ""
"When a customer orders on your eCommerce, there are **three** record types "
"required to be handle in Odoo:"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:8
msgid ":ref:`Sales orders <handling/sales>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:9
msgid ":ref:`Delivery orders <handling/delivery>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:10
msgid ":ref:`Invoices & legal requirements <handling/legal>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:15
msgid "Sales orders"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:18
msgid "Order and payment status"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:20
msgid ""
"The first step when a customer adds a product to his cart is the creation of"
" a quotation. Orders can be managed either from the **Website** or "
":doc:`Sales </applications/sales/sales>` app. eCommerce orders can "
"automatically be assigned to a specific sales team by going to "
":menuselection:`Website --> Configuration --> Settings`. In the **Shop - "
"Checkout Process** section, select a :guilabel:`Sales Team` or "
":guilabel:`Salesperson` to handle eCommerce orders."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst-1
msgid "Assignment of online orders to a sales team or salesperson"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:30
msgid ""
"Orders can be found under :menuselection:`Website --> eCommerce --> "
"Orders/Unpaid Orders`. Each order goes through a different status:"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:33
msgid ""
"**Quotation**: a new product is added to the cart, but the customer has "
"*not* gone through the checkout process yet;"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:35
msgid ""
"**Quotation sent**: the customer has gone through the checkout process and "
"confirmed the order, but the payment is not yet confirmed;"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:37
msgid ""
"**Order**: the customer has gone through the checkout process, confirmed the"
" order, and the payment is received."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst-1
msgid "Statuses of eCommerce orders"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:45
msgid "Abandoned cart"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:47
msgid ""
"An **abandoned cart** represents an order for which the customer did **not "
"finish** the checkout confirmation process. For these orders, it is possible"
" to send an **email reminder** to the customer automatically. To enable that"
" feature, go to :menuselection:`Website --> Configuration --> Settings` and "
"in the :guilabel:`Email & Marketing` section, enable "
":guilabel:`Automatically send abandoned checkout emails`. Once enabled, you "
"can set the **time-lapse** after which the email is sent and customize the "
"**email template** used."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:55
msgid ""
"For abandoned cart emails, the customer must either have entered their "
"contact details during the checkout process; or be logged-in when they added"
" the product to their cart."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:61
msgid "Delivery orders"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:64
msgid "Delivery flow"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:66
msgid ""
"Once a quotation has been confirmed, a delivery order is automatically "
"created. The next step is to process this delivery."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:69
msgid ""
"Packing eCommerce orders usually requires picking the product, preparing the"
" packaging, printing the shipping label(s) and shipping to the customer. "
"Depending on the number of orders, strategy, or resources, those steps can "
"be considered as one or multiple actions in Odoo."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:73
msgid ""
"An automatic email can be sent to the customer when the transfer status in "
"Odoo is “done”. To do so, enable the feature in the settings of the "
":doc:`Inventory </applications/inventory_and_mrp/inventory>` app."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:78
msgid ""
"If customers are allowed to pay when picking up their order in stores or by "
"wire transfer, the quotation is **not** be confirmed and the stock is "
"**not** be reserved. Orders must be confirmed manually to reserve products "
"in stock."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:83
msgid ""
":doc:`../../inventory_and_mrp/inventory/shipping_receiving/setup_configuration/invoicing`"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:84
msgid ""
":doc:`../../inventory_and_mrp/inventory/shipping_receiving/setup_configuration/labels`"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:85
msgid ""
":doc:`../../inventory_and_mrp/inventory/shipping_receiving/setup_configuration/multipack`"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:88
msgid "Returns and refunds"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:90
msgid ""
"Customers can only return an order through an online form. It may not be "
"possible to return products depending on the return strategy or type of "
"product."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:93
msgid ""
"Full refunds can be directly sent to customers from within the order "
"interface. A refund-compatible payment provider needs to be enabled first."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:97
msgid ":doc:`/applications/sales/sales/products_prices/returns`"
msgstr ":doc:`/applications/sales/sales/products_prices/returns`"

#: ../../content/applications/websites/ecommerce/order_handling.rst:98
msgid ":doc:`/applications/services/helpdesk/advanced/after_sales`"
msgstr ":doc:`/applications/services/helpdesk/advanced/after_sales`"

#: ../../content/applications/websites/ecommerce/order_handling.rst:99
msgid ":doc:`/applications/finance/payment_providers`"
msgstr ":doc:`/applications/finance/payment_providers`"

#: ../../content/applications/websites/ecommerce/order_handling.rst:104
msgid "Invoice and legal requirements"
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:106
msgid ""
"The final step of an ecommerce order is to generate the invoice and send it "
"to the customer. Depending on the type of business (B2B or B2C), an invoice "
"can either be generated automatically (B2B) or on demand of the customer "
"(B2C). This process can be automated if (and when) the online payment is "
":ref:`confirmed <handling/sales>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/order_handling.rst:111
msgid ""
"To automate invoicing, go to :menuselection:`Website --> Configuration --> "
"Settings` and in the :guilabel:`Invoicing` section, enable "
":guilabel:`Automatic Invoice`."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:3
msgid "Payment providers"
msgstr "مزودو الدفع "

#: ../../content/applications/websites/ecommerce/payments.rst:5
msgid ""
"Odoo supports a multitude of online :doc:`payment providers "
"</applications/finance/payment_providers>` for your website, allowing your "
"customers to pay with their preferred payment methods."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:10
#: ../../content/applications/websites/ecommerce/payments.rst:54
msgid ":doc:`/applications/sales/sales/products_prices/ewallets_giftcards`"
msgstr ":doc:`/applications/sales/sales/products_prices/ewallets_giftcards`"

#: ../../content/applications/websites/ecommerce/payments.rst:14
#: ../../content/applications/websites/website/configuration.rst:5
#: ../../content/applications/websites/website/configuration/cookies_bar.rst:15
#: ../../content/applications/websites/website/reporting/link_tracker.rst:10
msgid "Configuration"
msgstr "التهيئة "

#: ../../content/applications/websites/ecommerce/payments.rst:16
msgid ""
"To set up payment providers on the eCommerce app, go to "
":menuselection:`Website --> Configuration --> Payment Providers`. From here,"
" :guilabel:`Activate` the payment providers you wish to have available on "
"your shop, and configure them according to your needs."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:20
msgid ""
"Alternatively, you can access **payment providers** via "
":menuselection:`Website --> Configuration --> Settings`. In the "
":guilabel:`Shop - Payment` section, you can :guilabel:`Configure SEPA Direct"
" Debit` if you wish to use it, as well as :guilabel:`View other providers`. "
"If you use the :guilabel:`Authorize.net` payment provider, the :ref:`Payment"
" Capture Method <payment_providers/manual_capture>` can be configured in "
"that same menu."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:26
msgid ""
"If you are using :doc:`/applications/finance/payment_providers/paypal`, you "
"can also enable and configure it here."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:30
msgid "Checkout payment options"
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:32
msgid ""
"Once activated, customers can choose the payment provider of their choice "
"during the **checkout process**, at the :guilabel:`Confirm Order` step."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst-1
msgid "Payment provider selection at checkout"
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:40
msgid "eWallets and gift cards"
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:42
msgid ""
"When checking out, customers can pay with an eWallet or gift cards. To "
"enable these, go to :menuselection:`Website --> Configuration --> Settings`,"
" and in the :guilabel:`Shop-Products` section, enable "
":menuselection:`Discounts, Loyalty & Gift Card`."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst:46
msgid ""
"Once enabled, customers can enter their gift card **code** or pay with their"
" eWallet at the checkout step."
msgstr ""

#: ../../content/applications/websites/ecommerce/payments.rst-1
msgid "Enter gift card code to process checkout"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:3
msgid "Performance management"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:5
msgid ""
"Odoo integrates a variety of tools to analyze and improve the performance of"
" your eCommerce website."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:9
msgid "Data monitoring"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:11
msgid ""
"**Website** allows monitoring and analysis of the sales performance of your "
"eCommerce. To access the **reporting view**, go to :menuselection:`Website "
"--> Reporting --> eCommerce`. This dashboard helps you monitor everything "
"related to sales, such as sales performance per product, category, day, etc."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst-1
msgid "Performance reporting of eCommerce"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:19
msgid ""
"By clicking :guilabel:`Measures`, you can select the type of measurement "
"used, such as:"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:21
msgid ":guilabel:`Margin`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:22
msgid ":guilabel:`Qty Invoiced`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:23
msgid ":guilabel:`Untaxed Total`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:24
msgid ":guilabel:`Volume`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:25
msgid "..."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:27
msgid ""
"Other options include **multiple views (Pivot, etc.), comparison** by "
"periods or years, and directly :guilabel:`insert in spreadsheet`, etc."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:31
#: ../../content/applications/websites/website/configuration/multi_website.rst:147
msgid "Analytics"
msgstr "التحليلات"

#: ../../content/applications/websites/ecommerce/performance.rst:33
msgid ""
"It is possible to link your Odoo website with :ref:`analytics/plausible` and"
" :ref:`analytics/google-analytics`."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:39
msgid "Email queue optimization"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:41
msgid ""
"For websites handling flash sales (e.g., event ticket sales) or experiencing"
" high traffic spikes, order confirmation emails can become a performance "
"bottleneck, potentially slowing down the checkout process for other "
"customers."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:45
msgid ""
"To improve performance, these emails can be queued and processed separately "
"from the order confirmation flow. This is managed by the :guilabel:`Sales: "
"Send pending emails` scheduled action, which sends queued emails as soon as "
"possible."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:49
msgid "To enable asynchronous email sending:"
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:51
msgid ""
"Enable the :doc:`developer mode </applications/general/developer_mode>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:52
msgid ""
"Go to :menuselection:`Apps`, remove the :guilabel:`Apps` filter, and install"
" the :guilabel:`Sales - Async Emails` module."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:54
msgid ""
"Go to :menuselection:`Settings --> Technical --> System Parameters` and set "
"the :guilabel:`sale.async_emails` system parameter to `True`."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:56
msgid ""
"Go to :menuselection:`Settings --> Technical --> Scheduled Actions` and "
"ensure that the :guilabel:`Sales: Send pending emails` scheduled action is "
"enabled."
msgstr ""

#: ../../content/applications/websites/ecommerce/performance.rst:60
msgid ""
"Enabling this feature may delay order confirmation and invoice emails by a "
"few minutes. It is recommended only for high-traffic websites, as it can "
"introduce unnecessary delays for e-commerce websites with moderate traffic."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:5
#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:76
msgid "Products"
msgstr "المنتجات"

#: ../../content/applications/websites/ecommerce/products.rst:7
msgid ""
"**Odoo eCommerce** allows you to :ref:`add products <ecommerce/products/add-"
"products>` and manage your :ref:`product pages <ecommerce/products/product-"
"page>` directly from the Website app. It also allows you to add "
":ref:`product variants <ecommerce/products/product-variants>` and "
":ref:`digital files <ecommerce/products/digital-files>`, :ref:`translating "
"<ecommerce/products/translation>` the product page content, :ref:`managing "
"stock <ecommerce/products/stock-management>`, and enabling :ref:`product "
"comparisons <ecommerce/products/product-comparison>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:18
msgid "Add products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:23
msgid "Create products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:25
msgid ""
"To create a product from the frontend, click :guilabel:`+ New` in the top-"
"right corner, then :guilabel:`Product`. Enter the :guilabel:`Product Name`, "
":guilabel:`Sales Price`, the default :guilabel:`Customer Taxes` for local "
"transactions, and :guilabel:`Save`. You can then update the product's "
"details, add an image, and :ref:`customize "
"<ecommerce/products/customization>` the product page. When you "
":guilabel:`Save`, the product page is automatically published."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:32
msgid ""
"You can also create a product from the backend by going to "
":menuselection:`Website --> eCommerce --> Products` and clicking "
":guilabel:`New`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:34
msgid ""
"Products created from the frontend are automatically :ref:`published "
"<website/un-publish-page>`, while products created from the backend are not."
" To publish a product, click the :guilabel:`Go to Website` smart button to "
"access the product page, then toggle the switch from :guilabel:`Unpublished`"
" to :guilabel:`Published`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:40
msgid ""
":doc:`Create new products using the Barcode Lookup database "
"</applications/general/integrations/barcodelookup>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:46
msgid "Import products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:48
msgid ""
"To :ref:`import product data <essentials/export_import_data/import-data>` "
"using XLSX or CSV files, go to :menuselection:`Website --> eCommerce --> "
"Products`, click the :icon:`fa-cog` (:guilabel:`gear`) icon, then "
":ref:`Import records <essentials/export_import_data/import-data>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:53
msgid "To publish **large batches** of products, follow these steps:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:55
msgid "Go to :menuselection:`Website --> eCommerce --> Products`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:56
msgid ""
"Remove the :guilabel:`Published` filter and switch to the :guilabel:`List` "
"view."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:57
msgid ""
"Click the :icon:`fa-sliders` (:guilabel:`dropdown toggle`) icon and enable "
":guilabel:`Is published`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:59
msgid ""
"Click the :guilabel:`Is Published` column to re-order it by **published** or"
" **unpublished** products."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:61
msgid "Select the products to publish by ticking their box."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:62
msgid ""
"In the :guilabel:`Is Published` column, tick the box for any of the selected"
" products, then :guilabel:`Confirm` to publish them."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:68
msgid "Shop page"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:70
msgid ""
"To customize the layout of the main :guilabel:`Shop` page or modify its "
"content, click :guilabel:`Edit`. Go to the :guilabel:`Blocks` tab to add "
":doc:`building blocks <../../websites/website/web_design/building_blocks>` "
"or to the :guilabel:`Customize` tab to change the page layout or add "
"features:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:75
msgid ":guilabel:`Layout`: Select :guilabel:`Grid` or :guilabel:`List`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:77
msgid ""
":guilabel:`Size`: Set the number of products displayed per page and line."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:78
msgid ""
":guilabel:`Style`: Select :guilabel:`Default`, :guilabel:`Cards`, "
":guilabel:`Thumbnails`, or :guilabel:`Grid`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:80
msgid ""
":guilabel:`Image Size`: Choose the aspect ratio for the product images: "
":guilabel:`Landscape (4/3)`, :guilabel:`Default (1/1)`, :guilabel:`Portrait "
"(4/5)`, or :guilabel:`Vertical (2/3)`. You can also adjust the display by "
"changing the :guilabel:`Fill` options to best fit your design preferences."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:86
msgid ""
":guilabel:`Search Bar`: Toggle the switch to display a search bar at the top"
" of the products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:86
msgid "page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:88
msgid ""
":guilabel:`Prod. Desc.`: Toggle the switch to display the product "
"description below the product's name."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:91
msgid ""
":guilabel:`Categories`: display product categories on the :guilabel:`Left`, "
"on the :guilabel:`Top`, or both. If :guilabel:`Left` is selected, you can "
"enable :guilabel:`Collapse Categories` to make the category menu "
"collapsible."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:95
msgid ""
":guilabel:`Datepicker`: Toggle the switch to display a date range calendar "
"to check the availability of rental products over a specific period. The "
":doc:`Rental app <../../sales/rental>` must be installed to use this "
"feature."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:99
msgid ""
":guilabel:`Attributes`: Show product attributes on the :guilabel:`Left` "
"and/or display a :icon:`fa-sliders` (:guilabel:`dropdown toggle`) icon at "
"the :guilabel:`Top` allowing customers to filter products based on their "
"attributes."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:103
msgid ""
":guilabel:`Price Filter`: Toggle the switch to display a :guilabel:`Price "
"Range` bar, which allows customers to filter products according to a "
"specific price range by dragging adjustable handles."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:106
msgid ""
":guilabel:`Product Tags`: Toggle the switch to display the "
":guilabel:`Product Template Tags` on the product page and allow customers to"
" filter products using those tags by going to the :guilabel:`Tags` section "
"in the left column."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:110
msgid ""
":guilabel:`Top Bar`: Select :guilabel:`Sort By` to display a dropdown list "
"in the top bar for sorting products and/or :guilabel:`Layout` to allow "
"customers to switch to the grid or list view using the related icons."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:114
msgid ""
":guilabel:`Default Sort`: Choose how products are sorted by default: "
":guilabel:`Featured`, :guilabel:`Newest Arrivals`, :guilabel:`Name (A-Z)`, "
":guilabel:`Price - Low to High`, or :guilabel:`Price - High to Low`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:118
msgid ":guilabel:`Buttons`:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:120
msgid ""
"Select the :icon:`fa-shopping-cart` (:guilabel:`Shopping cart`) option to "
"display an :icon:`fa-shopping-cart` (:guilabel:`Add to cart`) icon on each "
"product's image, which takes the customer to the checkout page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:126
msgid ""
"Select the :icon:`fa-heart-o` (:guilabel:`Wishlist`) option to display an "
":icon:`fa-shopping-cart` (:guilabel:`Add to wishlist`) icon on each "
"product's image allowing logged-in customers to save products to a wishlist."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:130
msgid ""
"Select the :icon:`fa-exchange` (:guilabel:`Compare`) option to display the "
":icon:`fa-exchange` (:guilabel:`Compare`) icon on each product's image "
"allowing customers to :ref:`compare products <ecommerce/products/product-"
"comparison>` based on their attributes."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:135
msgid ""
"To feature a product, go to the :ref:`product form "
"<ecommerce/products/product-form>` and click the :icon:`fa-star-o` "
"(:guilabel:`Favorite`) icon next to the product's name."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:141
msgid "Product page"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:143
msgid ""
"To access a product's page, go to the :guilabel:`Shop` and click on the "
"product. Click :guilabel:`Edit` to :ref:`customize "
"<ecommerce/products/customization>` the page or :ref:`edit its images "
"<ecommerce/products/image-customization>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:149
msgid ""
"To access the backend **product form**, click the :icon:`fa-cog` "
":guilabel:`Product` button in the top-right corner of the product page. "
"Alternatively, navigate to :menuselection:`Website --> eCommerce --> "
"Products` and select the product. You can configure the product page from "
"the form by adding :ref:`variants <ecommerce/products/product-variants>`, "
":ref:`digital documents <ecommerce/products/digital-files>`, or "
":ref:`translating <ecommerce/products/translation>` content."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:156
msgid ""
"Click the :guilabel:`Go to Website` smart button to return to the frontend "
"product's page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:161
#: ../../content/applications/websites/website/configuration/cookies_bar.rst:27
msgid "Customization"
msgstr "التخصيصات "

#: ../../content/applications/websites/ecommerce/products.rst:163
msgid ""
"To customize a product page, click :guilabel:`Edit`. Go to the "
":guilabel:`Blocks` tab to add :doc:`building blocks "
"<../../websites/website/web_design/building_blocks>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:167
msgid ""
"When dragging and dropping a building block on the product page, placing it "
"above or below the top or bottom blue lines makes it visible on all product "
"pages."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:169
msgid ""
"You can edit any text on your website simply by clicking on it while in "
":guilabel:`Edit` mode."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:171
msgid ""
"Go to the :guilabel:`Customize` tab to modify the page layout or add "
"features:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:173
msgid ""
":guilabel:`Terms and Conditions`: Toggle the switch to display a link to "
"your :doc:`terms and conditions "
"<../../finance/accounting/customer_invoices/terms_conditions>` on the "
"product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:177
msgid ":guilabel:`Customers`:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:179
msgid ""
":guilabel:`Rating`: Allow logged-in portal users to submit product reviews "
"by clicking the stars below the product's name and sharing their experience "
"in the :guilabel:`Customer Reviews` section at the bottom. Reviews are "
"visible from the product page using the :icon:`fa-plus` (:guilabel:`plus`) "
"icon next to the :guilabel:`Customer Reviews` heading or from the product "
"form's chatter. To restrict visibility to internal employees, toggle the "
":guilabel:`Public` switch next to the review comment."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:185
msgid ""
":guilabel:`Share`: Add social media and email icon buttons allowing "
"customers to share the product through those channels."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:188
msgid ""
":guilabel:`Select Quantity`: Toggle the switch to allow customers to select "
"the product quantity they want to purchase."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:191
msgid ""
":guilabel:`Tax indication`: Toggle the switch to indicate if the price is "
":ref:`VAT included or excluded <ecommerce-price-management-tax-display>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:194
msgid ""
":guilabel:`Variants`: Show all possible product :ref:`variants "
"<ecommerce/products/product-variants>` vertically as a :guilabel:`Products "
"List` or horizontally as selectable :guilabel:`Options` to compose the "
"variant yourself."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:198
msgid ""
":guilabel:`Product Tags`: Toggle the switch to display the "
":guilabel:`Product Template Tags` on the product page and allow customers to"
" filter products using those tags."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:201
msgid ":guilabel:`Cart`:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:203
msgid ""
":guilabel:`Buy Now`: Add a :icon:`fa-bolt` :guilabel:`Buy Now` option to "
"take the customer to the checkout page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:205
msgid ""
":guilabel:`Wishlist`: Add an :icon:`fa-heart-o` :guilabel:`Add to wishlist` "
"option allowing logged-in customers to save products in a wishlist."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:207
msgid ""
":guilabel:`Compare`: Add a :icon:`fa-exchange` :guilabel:`Compare` option, "
"allowing customers to :ref:`compare products <ecommerce/products/product-"
"comparison>` based on their attributes."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:210
msgid ""
":guilabel:`Specification`: Select :guilabel:`Bottom of Page` to display a "
"detailed list of the attributes and their values available for the product. "
"This option only works for products with :ref:`variants "
"<ecommerce/products/product-variants>` if the :ref:`Product comparison tool "
"<ecommerce/products/product-comparison>` is enabled in the Website "
":guilabel:`Settings`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:217
msgid ""
":guilabel:`Variants`, :icon:`fa-heart-o` :guilabel:`Wishlist`, and "
":icon:`fa-exchange` :guilabel:`Compare` options must be enabled by going to "
":menuselection:`Website --> Configuration --> Settings`, in the "
":guilabel:`Shop - Products` section."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:221
msgid "Enabled functions apply to all product pages."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:222
msgid ""
"Products with single values for their attributes do not generate variants "
"but are still displayed in the :guilabel:`Product Specifications`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:228
msgid "Image customization"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:230
msgid ""
"To customize the images available on the product page, go to the "
":guilabel:`Customize` tab:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:232
msgid ""
":guilabel:`Images Width`: Changes the width of the product images displayed "
"on the page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:233
msgid ""
":guilabel:`Layout`: The :guilabel:`Carousel` layout allows customers to "
"navigate from one image to the next using the :icon:`fa-angle-left` "
"(:guilabel:`left arrow`) or :icon:`fa-angle-right` (:guilabel:`right "
"arrow`); whereas the :guilabel:`Grid` displays four images in a square "
"layout."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:236
msgid ""
":guilabel:`Image Zoom`: Select the zoom effect for product images: "
":guilabel:`Magnifier on hover` :guilabel:`Pop-up on Click`, "
":guilabel:`Both`, or :guilabel:`None`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:238
msgid ""
":guilabel:`Thumbnails`: Align thumbnails on the :icon:`fa-long-arrow-left` "
"(:guilabel:`Left`) or"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:239
msgid "at the :icon:`fa-long-arrow-down` (:guilabel:`Bottom`)."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:240
msgid ""
":guilabel:`Main Image`: Click :guilabel:`Replace` to change the product's "
"main image."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:241
msgid ""
":guilabel:`Extra Images`: :guilabel:`Add` extra images or videos (including "
"via URL) or :guilabel:`Remove all` product images."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:245
msgid ""
"Images must be in PNG or JPG format and with a minimum size of 1024x1024 to "
"trigger the zoom."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:250
msgid "Product variants"
msgstr "متغيرات المنتجات "

#: ../../content/applications/websites/ecommerce/products.rst:252
msgid ""
":doc:`Product variants "
"<../../sales/sales/products_prices/products/variants>` are different "
"versions of the same product, such as various colors or materials, with "
"potential differences in price and availability."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:256
msgid "To configure product variants for a product:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:258
#: ../../content/applications/websites/ecommerce/shipping.rst:103
#: ../../content/applications/websites/website/configuration/multi_website.rst:26
#: ../../content/applications/websites/website/configuration/multi_website.rst:136
msgid "Go to :menuselection:`Website --> Configuration --> Settings`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:259
msgid ""
"Scroll down to the :guilabel:`Shop - Products` section and enable the "
":guilabel:`Product Variants` feature."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:261
msgid ""
"Access the :ref:`product forms <ecommerce/products/product-form>` and go to "
"the :guilabel:`Attributes & Variants` tab, where you can add attributes and "
"values, allowing customers to configure and select product variants on the "
"product page. For multiple attributes, you can combine them to create "
"specific variants."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:266
msgid ""
"To display or hide an attribute on the :guilabel:`Shop` page and allow "
"visitors to filter them, go to :menuselection:`Website --> eCommerce --> "
"Attributes`, click on the attribute, and select :guilabel:`Visible` or "
":guilabel:`Hidden` in the :guilabel:`eCommerce Filter Visibility` field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:271
msgid ""
"To display the product attributes on :ref:`the main Shop page "
"<ecommerce/products/shop-page>`, set the :guilabel:`Attributes` feature to "
":guilabel:`Left` using the website editor."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:273
msgid ""
"To group attributes under the same section when :ref:`comparing products "
"<ecommerce/products/product-comparison>`, go to the :guilabel:`eCommerce "
"Category` field and either select an :doc:`existing category or create a new"
" one <../../websites/ecommerce/products>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:279
msgid "Two attribute values are needed to make the filter visible."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:282
msgid ""
":doc:`Product variants "
"<../../sales/sales/products_prices/products/variants>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:287
msgid "Digital files"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:289
msgid ""
"You can link digital files like certificates, eBooks, or user manuals to the"
" products. These documents are available :ref:`before payment <ecommerce-"
"products-digital-files-before-payment>` on the product page or in the "
"customer portal :ref:`after checkout <ecommerce-products-digital-files-"
"after-payment>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:293
msgid ""
"To link a digital file to a product, go to the :ref:`product form "
"<ecommerce/products/product-form>` and click the :guilabel:`Documents` smart"
" button. Then, click :guilabel:`Upload` to upload a file directly, or for "
"additional options, click :guilabel:`New`, then :guilabel:`Upload your "
"file`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:298
msgid ""
"You can link a URL instead of a digital file. To do so, click "
":guilabel:`New`, go to the :guilabel:`Type` field, and select "
":guilabel:`URL`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:300
msgid ""
"To edit an existing file, click the :icon:`fa-ellipsis-v` "
"(:guilabel:`dropdown menu`) in the top-right corner of the document card and"
" click :guilabel:`Edit`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:306
msgid "Digital files available before payment"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:308
msgid ""
"To make the file available on the product page (before payment), leave the "
":guilabel:`Visibility` field blank and toggle the :guilabel:`Show on product"
" page` switch."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst-1
msgid "digital file available before payment on the  product page"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:317
msgid "Digital files available after payment"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:319
msgid ""
"To make the file available (after payment), set the :guilabel:`Visibility` "
"field to :guilabel:`Confirmed order` and turn off the :guilabel:`Show on "
"product page` switch."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:325
msgid "Translation"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:327
msgid ""
"If multiple languages are available on your website, you can translate a "
"product's information directly on the :ref:`product form "
"<ecommerce/products/product-form>`. Fields that support multiple languages "
"are identifiable by their abbreviation language (e.g., EN) next to their "
"field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:331
msgid "The eCommerce-related fields to translate are:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:333
msgid ":guilabel:`Product name`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:334
msgid ":guilabel:`Out-of-Stock Message` (under the :guilabel:`Sales` tab)."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:335
msgid ":guilabel:`Sales Description` (under the :guilabel:`Sales` tab)."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:338
msgid ""
"Having untranslated content on a web page may be detrimental to the user "
"experience and :doc:`SEO <../../websites/website/pages/seo>`. You can use "
"the :doc:`Translate <../website/configuration/translate>` feature to "
"translate the page's content."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:341
msgid ""
"To check the language(s) of your website, go to :menuselection:`Website --> "
"Configuration --> Settings` and go to the :guilabel:`Website Info` section."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:347
msgid "Website availability"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:349
msgid ""
"To set the product's website availability, navigate to the :ref:`product "
"form <ecommerce/products/product-form>`, go to the :guilabel:`Sales` tab, "
"and in the :guilabel:`eCommerce shop` section, select the "
":guilabel:`Website` you wish the product to be available on. Leave the field"
" blank to make the product available on *all* websites."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:355
msgid ""
"You can make a product available on either *one* website or *all* websites, "
"but selecting only *some* websites is not possible."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:361
msgid "Stock management"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:363
msgid ""
"To enable and configure inventory management options, go to "
":menuselection:`Website --> Configuration --> Settings`, scroll down to the "
":guilabel:`Shop - Products` section and the :guilabel:`Inventory Defaults` "
"sub-section."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:368
msgid ""
"The **Inventory** app must be installed to see the inventory management "
"options."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:369
msgid ""
"To display the stock level on the product page, the :guilabel:`Product Type`"
" field must be set to :guilabel:`Storable` in the :ref:`product form "
"<ecommerce/products/product-form>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:373
msgid "Inventory"
msgstr "المخزون "

#: ../../content/applications/websites/ecommerce/products.rst:375
msgid ""
"In the :guilabel:`Inventory Defaults` sub-section, fill in those fields:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:377
msgid ""
":doc:`Warehouse "
"<../../inventory_and_mrp/inventory/warehouses_storage/inventory_management/warehouses>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:378
msgid ""
":guilabel:`Out-of-Stock`: Enable :guilabel:`Continue Selling` to allow "
"customers to place orders even when the product is **out of stock**. Leave "
"the box unchecked to **prevent orders**."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:380
msgid ""
":guilabel:`Show Available Qty`: Displays the available quantity left under a"
" specified threshold on the product page. The available quantity is "
"calculated based on the :guilabel:`On hand` quantity minus the quantity "
"already reserved for outgoing transfers."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:387
msgid "Product comparison"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:389
msgid ""
"To allow website visitors to compare products based on their attributes, go "
"to :menuselection:`Website --> Configuration --> Settings`, scroll down to "
"the :guilabel:`Shop - Products` section, and enable :guilabel:`Product "
"Comparison Tool`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:393
msgid ""
"The :icon:`fa-exchange` (:guilabel:`Compare`) icon is now available on each "
"product card on the main shop page when customers hover their mouse over it."
" To compare products, customers can click the :icon:`fa-exchange` "
"(:guilabel:`Compare`) option on the products they want to compare, then "
"click :icon:`fa-exchange` :guilabel:`Compare` in the pop-up window at the "
"bottom of the page to reach the comparison summary."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst-1
msgid "Product comparison window"
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:403
msgid ""
"The :guilabel:`Product Comparison Tool` is only available for products with "
":ref:`attributes <ecommerce/products/product-variants>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products.rst:405
msgid ""
"Selecting the :icon:`fa-exchange` (:guilabel:`Compare`) option from a "
"product page is also possible."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:3
msgid "Catalog"
msgstr "كتالوج "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:5
msgid ""
"The eCommerce catalog is the equivalent of your physical store shelves: it "
"allows customers to see what you have to offer. Clear categories, available "
"options, sorting, and navigation threads help you structure it efficiently."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:10
msgid ""
"Go to :menuselection:`Website --> Configuration --> Settings`, scroll down "
"to the :guilabel:`Privacy` section to restrict :guilabel:`Ecommerce Access` "
"to logged-in users and/or enable :guilabel:`Shared Customer Accounts` to "
"allow access to all websites with a single account."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:16
msgid "Categorize the product catalog"
msgstr "تحديد فئات كتالوج المنتجات "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:18
msgid ""
"In Odoo, there is a **specific category model** for your eCommerce. Using "
"eCommerce categories for your products allows you to add a navigation menu "
"on your eCommerce page. Visitors can then use it to view all products under "
"the category they select."
msgstr ""
"في أودو، يوجد **نموذج للفئات المحددة** لمتجرك الإلكتروني. استخدام فئات "
"التجارة الإلكترونية لمنتجاتك يمكنك من إضافة قائمة تنقل على صفحة متجرك "
"الإلكتروني. بإمكان الزائرين عندها استخدامه لعرض كافة المنتجات ضمن الفئة التي"
" يقومون باختيارها. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:22
msgid ""
"To do so, go to :menuselection:`Website --> eCommerce --> Products`, select "
"the product you wish to modify, click on the :guilabel:`Sales` tab, and "
"select the :guilabel:`Categories` you want under :guilabel:`eCommerce Shop`."
msgstr ""
"للقيام بذلك، اذهب إلى :menuselection:`الموقع الإلكتروني --> المتجر "
"الإلكتروني --> المنتجات`، ثم قم بتحديد المنتج الذي ترغب في تعديله، واضغط على"
" علامة تبويب :guilabel:`المبيعات` وحدد :guilabel:`الفئات` التي تريدها في "
":guilabel:`المتجر الإلكتروني`. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "eCommerce categories under the \"Sales\" tab"
msgstr "فئات المتجر الإلكتروني ضمن علامة تبويب \"المبيعات\" "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:31
msgid "A single product can appear under multiple eCommerce categories."
msgstr "يمكن أن يظهر المنتج الواحد ضمن عدة فئات للمتجر الإلكتروني. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:33
msgid ""
"When your product's categories are configured, go to your **main shop page**"
" and click on :menuselection:`Edit --> Customize tab`. In the "
":guilabel:`Categories` option, you can either enable a menu on the "
":guilabel:`Left`, on the :guilabel:`Top`, or both. If you select the "
":guilabel:`Left` category, the option :guilabel:`Collapsable Category "
"Recursive` appears and allows to render the :guilabel:`Left` category menu "
"collapsable."
msgstr ""
"عندما تتم تهيئة فئات المنتجات، اذهب إلى **صفة المتجر الرئيسي** واضغط على "
":menuselection:`تحرير --> تحرير علامة البويب`. في خيار :guilabel:`الفئات`، "
"يمكنك إما تفعيل قائمة إما إلى :guilabel:`اليسار`، أو إلى :guilabel:`الأعلى`،"
" أو كليهما. إذا قمت بتحديد الفئة إلى :guilabel:`اليسار`، سيظهر الخيار "
":guilabel:`القائمة المتكررة القابلة للتصغير` وتسمح بتكوين فئة القائمة "
"القابلة للتصغير :guilabel:`إلى اليسار`. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Categories options for your eCommerce website"
msgstr "خيارات الفئات لموقع متجرك الإلكتروني "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:44
msgid ":doc:`../products`"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:49
msgid "Browsing"
msgstr "التصفح "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:51
msgid ""
"The eCommerce category is the first tool to organize and split your "
"products. However, if you need an extra level of categorization in your "
"catalog, you can activate various **filters** such as attributes or sort-by "
"search."
msgstr ""
"فئة المتجر الإلكتروني هي أول أداة تقوم بتنظيم وتقسيم منتجاتك، ولكن، إذا كنت "
"بحاجة إلى مستوى آخر من الفئات في المتالوج الخاص بك، يمكنك تفعيل مختلف "
"**عوامل التصفي** كالخصائص أو بحث الفرز حسب. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:56
msgid "Attributes"
msgstr "الخصائص"

#: ../../content/applications/websites/ecommerce/products/catalog.rst:58
msgid ""
"Attributes refer to **characteristics** of a product, such as **color** or "
"**material**, whereas variants are the different combinations of attributes."
" :guilabel:`Attributes and Variants` can be found under "
":menuselection:`Website --> eCommerce --> Products`, select your product, "
"and :guilabel:`Attributes & Variants` tab."
msgstr ""
"تشير الخصائص إلى **أوصاف** المنتج، مثل **اللون** أو **المادة**، بينما "
"المتغيرات هي  التركيبات المختلفة للخصائص. يمكن إيجاد :guilabel:`الخصائص "
"والمتغيرات` ضمن :menuselection:`الموقع الإلكتروني --> المتجر الإلكتروني --> "
"المنتجات`، واختر منتجك، وعلامة تبويب :guilabel:`الخصائص والمتغيرات`. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:64
msgid ":doc:`../../../sales/sales/products_prices/products/variants`"
msgstr ":doc:`../../../sales/sales/products_prices/products/variants`"

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Attributes and variants of your product"
msgstr "خصائص ومتغيرات منتجك "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:70
msgid ""
"To enable **attribute filtering**, go to your **main shop page**, click on "
":menuselection:`Edit --> Customize tab` and select either :guilabel:`Left`, "
":guilabel:`Top`, or both. Additionally, you can also enable :guilabel:`Price"
" Filtering` to enable price filters."
msgstr ""
"لتمكين **تصفية الخصائص**، اذهب إلى **صفحة المتجر الرئيسية**، واضغط على "
":menuselection:`تحرير --> تخصيص علامة التبويب` واختر إما :guilabel:`اليسار` "
"أو :guilabel:`الأعلى` أو كليهما. إضافة إلى ذلك، يمكنك أيضاً تمكين "
":guilabel:`التصفية حسب السعر` لتمكين عوامل تصفية السعر. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:75
msgid ""
":guilabel:`Price Filter` works independently from **attributes** and, "
"therefore, can be enabled on its own if desired."
msgstr ""
"يعمل :guilabel:`عامل تصفية السعر` مستقلاً عن **الخصائص** وبالتالي، يمكن "
"تمكينه بمفرده إذا أردت. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:79
msgid ""
"You can use **attribute filters** even if you do not work with product "
"variants. When adding attributes to your products, make sure only to specify"
" *one* value per attribute. Odoo does not create variants if no combination "
"is possible."
msgstr ""
"يمكنك استخدام **عوامل تصفية الخصائص** حتى وإن لم تكن تستخدم متغيرات "
"المنتجات. عند إضافة الخصائص إلى منتجاتك، تأكد من تحديد قيمة *واحدة* لكل "
"خاصية. لا يقوم أودو بإنشاء متغيرات إذا لم يكن هناك تركيبة ممكنة. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:84
msgid "Sort-by search"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:86
msgid ""
"It is possible to allow the user to manually **sort the catalog** using the "
"search bar. From your **main shop page**, click on :menuselection:`Edit --> "
"Customize tab`; you can enable or disable the :guilabel:`Sort-By` option as "
"well as the :guilabel:`Layout` button. You can also select the "
":guilabel:`Default Sort` of the :guilabel:`Sort-By` button. The default sort"
" applies to *all* categories."
msgstr ""
"يمكن السماح للمستخدم **بفرز الكتالوج** يدوياً، باستخدام شريط البحث. من "
"**صفحة المتجر الرئيسية**، اضغط على :menuselection:`تحرير  --> تخصيص علامة "
"التبويب`؛ يمكنك تمكين أو تعطيل خيار :guilabel:`الفرز حسب` إضافة إلى زر "
":guilabel:`المخطط`. يمكنك أيضا تحديد :guilabel:`الفرز الافتراضي` لزر "
":guilabel:`الفرز حسب`. ينطبق الفرز الافتراضي على *كافة* الفئات. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:92
msgid "The **sorting** options are:"
msgstr "خيارات **الفرز** هي: "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:94
msgid "Featured"
msgstr "مميز "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:95
msgid "Newest Arrivals"
msgstr "وصل حديثاً "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:96
msgid "Name (A-Z)"
msgstr "الاسم (بالترتيب الأبجدي) "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:97
msgid "Price - Low to High"
msgstr "السعر - الأقل إلى الأعلى "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:98
msgid "Price - High to Low"
msgstr "السعر - الأعلى إلى الأقل "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:100
msgid ""
"In addition, you can **manually edit** the catalog's order of a product by "
"going to **the main shop page** and clicking on the product. Under the "
":guilabel:`Product` section of the :guilabel:`Customize` section, you can "
"rearrange the order by clicking on the arrows. `<<` `>>` move the product to"
" the **extreme** right or left, and `<` `>` move the product by **one** row "
"to the right or left. It is also possible to change the catalog's order of "
"products in :menuselection:`Website --> eCommerce --> Products` and drag-"
"and-dropping the products within the list."
msgstr ""
"إضافة إلى ذلك، يمكنك **التحرير يدوياً** لطلب منتج الكتالوج  عن طريق الذهاب "
"إلى **الصفحة الرئيسية للمتجر** والضغط على المنتج. تحت قسم :guilabel:`المنتج`"
" ضمن قسم :guilabel:`التخصيص` يمكنك تغيير ترتيب الطلب عن طريق الضغط على "
"الأسهم. `<<` `>>` وقم بتحريك المنتج من **أقصى** اليمين أو اليسار، وقم بتحريك"
" المنتج إلى صف `<` `>` **واحد ** إلى اليمين أو اليسار. من الممكن أيضاً تغيير"
" طلب منتجات الكتالوج في :menuselection:`الموقع الإلكتروني --> المتجر "
"الإلكتروني --> المنتجات` وثم بسحب وإفلات المنتجات في القائمة. "

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Product rearrangement in the catalog"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:113
msgid "Page design"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:116
msgid "Category page"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:118
msgid ""
"You can customize the layout of the category page using the website builder."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:121
msgid ""
"Editing the layout of the category page is global; editing one category "
"layout affects *all* category pages."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:124
msgid ""
"To do so, go on to your :menuselection:`Category page --> Edit --> "
"Customize`. Here, you can choose the layout, the number of columns to "
"display the products, etc. The :guilabel:`Product Description` button makes "
"the product description visible from the category page, underneath the "
"product picture."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Layout options of the category pages."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:133
msgid ""
"You can choose the size of the grid, but be aware that displaying too many "
"products may affect performance and page loading speed."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:137
msgid "Product highlight"
msgstr "أهم ما يميز المنتج "

#: ../../content/applications/websites/ecommerce/products/catalog.rst:139
msgid ""
"You can highlight products to make them more visible on the category or "
"product page. On the page of your choice, go to :menuselection:`Edit --> "
"Customize` and click on the product to highlight. In the :guilabel:`Product`"
" section, you can choose the size of the product image by clicking on the "
"grid, and you can also add a **ribbon** or :guilabel:`Badge`. This displays "
"a banner across the product's image, such as:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:145
msgid "Sale;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:146
msgid "Sold out;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:147
msgid "Out of stock;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:148
msgid "New."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:150
msgid ""
"Alternatively, you can activate the :doc:`developer mode "
"<../../../general/developer_mode>` on the **product's template**, and under "
"the :guilabel:`Sales` tab, change or create the ribbon from the "
":guilabel:`Ribbon` field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:155
msgid ""
"The :doc:`developer mode <../../../general/developer_mode>` is only intended"
" for experienced users who wish to have access to advanced tools. Using the "
"**developer mode** is *not* recommended for regular users."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Ribbon highlight"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:164
msgid "Additional features"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:166
msgid ""
"You can access and enable additional feature buttons such as **add to "
"cart**, **comparison list**, or a **wishlist**. To do so, go to your **main "
"shop page**, and at the end of the :guilabel:`Products Page` category, click"
" on the feature buttons you wish to use. All three buttons appear when "
"hovering the mouse over a product's image."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:171
msgid ""
":guilabel:`Add to Cart`: adds a button to :doc:`add the product to the cart "
"<../cart>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:173
msgid ""
":guilabel:`Comparison List`: adds a button to **compare** products based on "
"their price, variant, etc.;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:175
msgid ""
":guilabel:`Wishlist Button`: adds a button to **wishlist** the product."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Feature buttons for add to cart, comparison list, and wishlist"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Appearance of buttons when hoovering over the mouse"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:186
msgid "Add content"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:188
msgid ""
"You can use **building blocks** to add content on the category page, with a "
"variety of blocks ranging from :guilabel:`Structure` to :guilabel:`Dynamic "
"Content`. Specific areas are defined to use blocks are defined and "
"highlighted on the page when **dragging-and-dropping** a block."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst-1
msgid "Building blocks areas"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:196
msgid ""
"If you drop a building block **on top** of the product list, it creates a "
"new category header specific to *that* category."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:198
msgid ""
"If you drop a building **on the top** or **bottom** of the page, it becomes "
"visible on *all* category pages."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/catalog.rst:202
msgid ""
"Adding content to an eCommerce category page is beneficial in terms of "
"**SEO** strategy. Using **keywords** linked to the products or the eCommerce"
" categories improves organic traffic. Additionally, each category has its "
"own specific URL that can be pointed to and is indexed by search engines."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:3
msgid "Cross-selling and upselling"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:5
msgid ""
":ref:`Cross-selling <ecommerce/cross_selling>` and :ref:`upselling "
"<ecommerce/cross_upselling/alternative>` are sales techniques designed to "
"encourage customers to purchase additional or higher-priced products and "
"services from your :doc:`catalog`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:12
msgid "Cross-selling"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:14
msgid ""
"Cross-selling can be achieved by suggesting :ref:`optional products "
"<ecommerce/cross_upselling/optional>` when items are added to the cart or by"
" recommending :ref:`accessory products "
"<ecommerce/cross_upselling/accessory>` on the checkout page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:21
msgid "Optional products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:23
msgid ""
"**Optional products** are suggested when the customer selects a product and "
"clicks :guilabel:`Add to cart`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:26
msgid ""
"To enable **optional products**, go to :menuselection:`Website --> eCommerce"
" --> Products`, select a product, go to the :guilabel:`Sales` tab, and enter"
" the products you wish to feature in the :guilabel:`Optional Products` "
"field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst-1
msgid "Optional products cross-selling"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:36
msgid "Accessory products"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:38
msgid ""
"**Accessory products** are showcased in the :guilabel:`Suggested "
"Accessories` section during the :guilabel:`Review Order` step, just before "
"proceeding to checkout."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:41
msgid ""
"To enable **accessory products**, go to :menuselection:`Website --> "
"eCommerce --> Products`, select a product, go to the :guilabel:`Sales` tab, "
"and enter the products you wish to feature in the :guilabel:`Accessory "
"Products` field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst-1
msgid "Suggested accessories at checkout during cart review"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:51
msgid "Upselling"
msgstr "الارتقاء بالصفقة "

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:53
msgid ""
"You can display **alternative products** at the bottom of the product page "
"as an upselling technique, encouraging customers to consider a more "
"expensive variant or product than their initial choice by presenting "
"appealing alternatives."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst-1
msgid "Alternative products on the product page"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:60
msgid ""
"To enable alternative products, navigate to :menuselection:`Website --> "
"eCommerce --> Products`, select the desired product, and go to the "
":guilabel:`Sales` tab. In the :guilabel:`Alternative Products` field, add "
"the products you want to feature."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:64
msgid ""
"Click :guilabel:`Go to Website` to view the alternative products displayed "
"at the bottom of the product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/cross_upselling.rst:67
msgid ""
"To adjust the settings, click :guilabel:`Edit` and select the related "
":doc:`building block <../../website/web_design/building_blocks>`. In the "
":guilabel:`Customize` tab, scroll to the :guilabel:`Alternative Products` "
"section and modify the settings as needed to tailor the display to your "
"preferences."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:3
msgid "Price management"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:5
msgid ""
"Odoo offers multiple options to select the prices displayed on a website, as"
" well as condition-specific prices based on set criteria."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:9
msgid "Taxes"
msgstr "الضرائب"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:12
msgid "Tax configuration"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:14
msgid ""
"To add a tax on a product, you can either set a tax in the "
":guilabel:`Customer Taxes` field of the **product template** or use "
":doc:`fiscal positions "
"</applications/finance/accounting/taxes/fiscal_positions>`."
msgstr ""
"لإضافة ضريبة في منتج، يمكنك إما إعداد ضريبة في حقل :guilabel:`ضرائب العميل` "
"لـ **قالب المنتج** أو استخدام :doc:`الأوضاع المالية "
"</applications/finance/accounting/taxes/fiscal_positions>`. "

#: ../../content/applications/websites/ecommerce/products/price_management.rst:19
msgid ":doc:`/applications/finance/accounting/taxes`"
msgstr ":doc:`/applications/finance/accounting/taxes`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:20
msgid ":doc:`/applications/finance/accounting/taxes/avatax`"
msgstr ":doc:`/applications/finance/accounting/taxes/avatax`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:21
msgid ":doc:`/applications/finance/accounting/taxes/fiscal_positions`"
msgstr ":doc:`/applications/finance/accounting/taxes/fiscal_positions`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:26
msgid "Tax display"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:28
msgid ""
"Choosing the displayed price tax usually depends on a country's regulations "
"or the type of customers **(B2B vs. B2C)**. To select the type of price "
"displayed, go to :menuselection:`Website --> Configuration --> Settings`, "
"scroll down to the :guilabel:`Shop - Products` category, and select "
":guilabel:`Tax Excluded` or :guilabel:`Tax Included`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:33
msgid ""
":guilabel:`Tax Excluded`: the price displayed on the website is **tax-"
"excluded**, and the tax is computed at the cart-review step;"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:35
msgid ""
":guilabel:`Tax Included`: the price displayed on the website is **tax-"
"included**."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:38
msgid ""
"This setting is website specific, and therefore can be altered for each "
"website within a database."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:41
msgid ""
"To display the type of pricing next to the product price, navigate to "
":menuselection:`Website --> Site --> Homepage --> Shop`, select a product, "
"then click :guilabel:`Edit` and, in the :guilabel:`Customize` tab, enable "
":guilabel:`Tax Indication`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst-1
msgid "Tax type displayed on the product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:49
msgid ":doc:`/applications/finance/accounting/taxes/B2B_B2C`"
msgstr ":doc:`/applications/finance/accounting/taxes/B2B_B2C`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:52
msgid "Price per unit"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:54
msgid ""
"It is possible to display a :doc:`price per unit "
"<../../../inventory_and_mrp/inventory/product_management/configure/uom>` on "
"the product page. To do that, go to :menuselection:`Website --> "
"Configuration --> Settings` and enable :guilabel:`Product Reference Price` "
"under the :guilabel:`Shop - Products` section. When enabled, ensure an "
"amount is set in the :guilabel:`Base Unit Count` field of the product "
"template, and in the :guilabel:`Sales Price` field."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst-1
msgid "Cost per unit pricing on the product template."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:64
msgid ""
"The price per unit of measure can be found above the :guilabel:`Add to Cart`"
" button on the product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst-1
msgid "Cost per unit pricing on the product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:71
msgid ""
"Pay attention that having the price per unit may be **mandatory** in some "
"countries."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:74
msgid ""
":doc:`../../../inventory_and_mrp/inventory/product_management/configure/uom`"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:79
msgid "Pricelists"
msgstr "قوائم الأسعار"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:81
msgid ""
"Pricelists are the primary tool to manage prices on an eCommerce website. "
"They make it possible to define website-specific prices - different from the"
" price on the product template - based on the country group, currency, "
"minimum quantity, period, or variant."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:86
#: ../../content/applications/websites/ecommerce/products/price_management.rst:180
msgid ":doc:`/applications/sales/sales/products_prices/prices/pricing`"
msgstr ":doc:`/applications/sales/sales/products_prices/prices/pricing`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:89
msgid "Understanding default pricelists"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:91
msgid ""
"The concept of a default pricelist in Odoo depends on the application being "
"used. In the **Sales** app, a customer's default pricelist is determined by "
"their contact profile. If a pricelist is manually assigned to a contact, the"
" pricelist becomes their default. If no pricelist is assigned, the default "
"is the first pricelist listed."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:96
msgid ""
"In the **eCommerce** app,the default pricelist is assigned at the website "
"level. However, it is influenced by the user's login status and country "
"group settings."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:100
msgid "How pricelists are applied in eCommerce"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:102
msgid ""
"If a portal user has a specific pricelist assigned to their contact profile,"
" that pricelist is applied to their purchase. However, if that pricelist is "
"**not** assigned to the website they are visiting, the user sees the "
"website's default pricelist."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:107
msgid ""
"The default website pricelist is the first available pricelist assigned to a"
" website, without the country group setting configured."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:110
msgid "Public, non-logged in users, see the website's default pricelist."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:112
msgid ""
"If a pricelist includes a country group, Odoo checks the visitor's IP "
"address and applies the corresponding pricelist. If a visitor has a "
"pricelist assigned in their contact profile, that pricelist takes precedence"
" over the country-based pricelist, unless the assigned pricelist has a "
"different country group."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:118
msgid ""
"A customer from the United States visits the website. They do not have a "
"portal account. The :guilabel:`United States` pricelist is applied."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:121
msgid ""
"A different visitor, also from the United States, has the :guilabel:`Loyal "
"Customer Discount` pricelist assigned in their contact record. This "
"assignment takes precedence over the country group assignation, so the "
":guilabel:`Loyal Customer Discount` is applied."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:0
msgid "An example of various pricelists assigned to a website."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:129
msgid "Pricelist configuration"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:131
msgid ""
"To activate pricelists, navigate to :menuselection:`Website --> "
"Configuration --> Settings`, scroll down to the :guilabel:`Shop - Products` "
"section, enable the :guilabel:`Pricelist` feature, then click "
":guilabel:`Save`. Once pricelists have been activated, go to "
":menuselection:`Website --> eCommerce --> Pricelists` to configure them."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:137
msgid "Preventing sales if price is zero"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:139
msgid ""
"The :guilabel:`Prevent Sale of Zero Priced Product` feature prevents "
"customers from purchasing a product if the sales price is listed as `0`. "
"When this feature is enabled, instead of seeing :guilabel:`Add to Cart` when"
" attempting to purchase a product, they see :guilabel:`Contact Us`. This "
"feature is useful for companies that want to hide the prices of their "
"products."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:144
msgid ""
"To utilize this feature, first navigate to :menuselection:`Website --> "
"Configuration --> Settings` and tick the :guilabel:`Prevent Sale of Zero "
"Priced Product` checkbox, then click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:147
msgid ""
"Next, create a pricelist that sets all product prices to `0`. Ensure the "
"pricelist is assigned to the correct website and is listed first among the "
"website's pricelists."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:151
msgid "Selectable pricelists"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:153
msgid ""
"*Selectable pricelists* appear in the shop page's pricelist drop-down menu. "
"When a pricelist is designated as :guilabel:`Selectable`, it allows the "
"customer to choose between available pricelists."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:157
msgid ""
"If a pricelist is designated as :guilabel:`Selectable`, and is not assigned "
"to a specific website, then the pricelist is selectable on **all** websites."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:160
msgid ""
"If a pricelist is designated as :guilabel:`Selectable`, it appears in the "
"drop-down menu next to the search bar. However, if a pricelist does *not* "
"appear in the drop-down menu, it may be for one of the following reasons:"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:164
msgid ""
"If there is only one selectable pricelist, and the contact is assigned a "
"pricelist, the drop-down may not appear."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:166
msgid ""
"If multiple selectable pricelists exist and match a visitor's country group,"
" only those pricelists are shown in the drop-down."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:170
msgid "Foreign currency"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:172
msgid ""
"If you are selling in **multiple currencies** and have pricelists in foreign"
" currencies, customers can select their corresponding pricelist anywhere on "
"the :guilabel:`Shop` page from the drop-down menu next to the search bar."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst-1
msgid "Pricelists selection."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:181
msgid ":doc:`/applications/sales/sales/products_prices/prices/currencies`"
msgstr ":doc:`/applications/sales/sales/products_prices/prices/currencies`"

#: ../../content/applications/websites/ecommerce/products/price_management.rst:184
msgid "Permanent discount"
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:186
msgid ""
"If you have permanently reduced the price of a product, a popular means to "
"attract customers is the **strikethrough** strategy. The strategy consists "
"in displaying the previous price crossed out and the **new discounted "
"price** next to it."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst-1
msgid "Price strikethrough."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:193
msgid ""
"To display a 'striked' price, enable the :guilabel:`Comparison Price` option"
" under :menuselection:`Website --> Configuration --> Settings --> Shop - "
"Products category`. Then, head to the product's template "
"(:menuselection:`Website --> eCommerce --> Products`), and in the "
":guilabel:`Compare to Price` field, enter the **new** price."
msgstr ""

#: ../../content/applications/websites/ecommerce/products/price_management.rst:199
msgid ""
"If a pricelist contains a :ref:`Discount <sales/products/price-rules>` price"
" type, the striked price is visible to applicable customers. This is true "
"even if the :guilabel:`Comparison Price` feature has not been enabled."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:5
msgid ""
"Odoo eCommerce allows you to configure various delivery methods, enabling "
"customers to choose their preferred option at :doc:`checkout <checkout>`. "
"These methods include :ref:`external providers <ecommerce/shipping/external-"
"provider>`, :ref:`custom options <ecommerce/shipping/custom-method>` such as"
" flat-rate or free shipping, local carriers via :doc:`Sendcloud "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/sendcloud_shipping>`"
" or :ref:`Based on Rules <inventory/shipping/rules>`, and :ref:`in-store "
"pickup <ecommerce/shipping/instore-pickup>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:16
msgid "External provider integration"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:18
msgid ""
"To handle product delivery, you can connect your database to :doc:`third-"
"party shipping carriers "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/third_party_shipper>`"
" like :doc:`FedEx "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/fedex>`,"
" :doc:`UPS "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/ups_credentials>`,"
" or :doc:`DHL "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/dhl_credentials>`."
" A shipping connector links to these providers, automating :doc:`tracking "
"labels "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/labels>`"
" and shipping processes."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:27
msgid ""
"To enable a third-party delivery provider, go to :menuselection:`Website -->"
" Configuration --> Settings`, scroll to the :guilabel:`Delivery` section, "
"select the desired delivery provider(s), and :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:31
msgid ""
"Go to :menuselection:`Website --> Configuration --> Delivery Methods` and "
"select the delivery method in the list to :ref:`configure it "
"<inventory/shipping_receiving/configure-delivery-method>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:35
msgid ""
":doc:`Third-party shipping carriers "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/third_party_shipper>`"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:39
msgid ""
"The field used to define additional fees **must** be filled **in your third-"
"party delivery provider account**, even if you do not plan to charge "
"customers any additional fee. If you do not want to apply a fee, enter `0`. "
"If the field is left empty, the delivery price cannot be calculated, and an "
"error message prompts the customer to select an alternative delivery method."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:45
msgid "Margin on delivery rate"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:47
msgid ""
"To add an additional fee to the base shipping rate (e.g., to cover extra "
"costs), log into your carrier account and set the desired fee in the related"
" field. The shipping connector retrieves this fee and includes it in the "
"final price at checkout. Contact your carrier for further assistance with "
"this configuration."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:52
msgid ""
"Alternatively, enter `0` in your third-party shipping provider account, then"
" set the fee in Odoo. To do so, access the desired :ref:`shipping method's "
"form <inventory/shipping_receiving/configure-delivery-method>` and enter the"
" fee in the :guilabel:`Margin on Rate` field to add a percentage to the "
"shipping costs and/or the :guilabel:`Additional margin` field to add a fixed"
" amount."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:59
msgid ""
"The field used to define additional fees cannot be left empty in your third-"
"party shipping provider account."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:65
msgid "Custom delivery method"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:67
msgid "Custom delivery methods must be created, for example:"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:69
msgid ""
"to integrate delivery carriers through :doc:`Sendcloud "
"</applications/inventory_and_mrp/inventory/shipping_receiving/setup_configuration/sendcloud_shipping>`;"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:71
msgid ""
"to configure specific rules (e.g., to offer free shipping for orders above a"
" specific amount) for a specific provider;"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:73
msgid ""
"to configure :ref:`Fixed Price <inventory/shipping/fixed>` shipping, or "
"shipping :ref:`Based on Rules <inventory/shipping/rules>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:76
msgid ""
"To create a custom delivery method, go to :menuselection:`Website --> "
"Configuration --> Delivery Methods`, click :guilabel:`New`, and fill in the "
":ref:`fields <inventory/shipping_receiving/shipping-methods-details>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:80
msgid ""
"In the :guilabel:`Provider` field, select :ref:`Based on Rules "
"<inventory/shipping/rules>` or :ref:`Fixed Price "
"<inventory/shipping/fixed>`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:84
msgid ""
"Upon :ref:`configuring <inventory/shipping_receiving/configure-delivery-"
"method>` a delivery method, you can:"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:87
msgid ""
"Restrict it :doc:`to a specific website "
"<../website/configuration/multi_website>` by selecting it in the "
":guilabel:`Website` field."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:89
msgid ""
"Click the :guilabel:`Test Environment` smart button to switch to the "
":guilabel:`Production Environment`. Then, click :guilabel:`Unpublished` to "
":guilabel:`Publish` the delivery method and make it available to website "
"visitors."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:92
msgid ""
"Use the :guilabel:`Availability` tab to define :ref:`conditions "
"<inventory/shipping_receiving/availability>` for the delivery method based "
"on the order's content or destination."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:99
msgid "Click & Collect"
msgstr "انقر للتحصيل "

#: ../../content/applications/websites/ecommerce/shipping.rst:101
msgid ""
"To allow customers to reserve products online and pay for/collect them in-"
"store, follow these steps:"
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:104
msgid ""
"Scroll to the :guilabel:`Delivery` section, enable :guilabel:`Click & "
"Collect`, and :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:106
msgid ""
"Click :icon:`fa-arrow-right` :guilabel:`Configure Pickup Locations` to "
":ref:`configure <inventory/shipping_receiving/configure-delivery-method>` "
"the delivery method and ensure the :guilabel:`Provider` field is set to "
":guilabel:`Pick up in store`."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:109
msgid ""
"In the :guilabel:`Stores` tab, click :guilabel:`Add a line` and select the "
"warehouse(s) where customers can collect their orders."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:111
msgid ""
"Once your setup is complete, click the :guilabel:`Unpublish` button to "
"change the status to :guilabel:`Publish` and make the delivery method "
"available to customers."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:115
msgid ""
"When the product is in stock, a location selector is displayed on the "
":doc:`product <products>` and :doc:`checkout <checkout>` pages. Customers "
"cannot select a pickup location if the product is out of stock at that "
"location. The :ref:`Continue selling <ecommerce/products/stock-management>` "
"option for out-of-stock products is not supported."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:119
msgid ""
"If the :ref:`Show Available Qty <ecommerce/products/stock-management>` "
"option is enabled for a product, customers can view the stock quantity "
"available for each warehouse in the location selector on the product page."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:122
msgid ""
"Each warehouse must have a **complete address** to ensure its location is "
"accurately displayed to customers. Incomplete addresses prevent the "
"warehouse from being shown."
msgstr ""

#: ../../content/applications/websites/ecommerce/shipping.rst:124
msgid "The Click & Collect option is not available for services."
msgstr ""

#: ../../content/applications/websites/elearning.rst:3
msgid "eLearning"
msgstr "التعلم الإلكتروني "

#: ../../content/applications/websites/elearning.rst:5
msgid ""
"The **eLearning** app allows you to easily upload content, define learning "
"objectives, manage attendees, assess students' progress, and even set up "
"rewards. Engaging participants in a meaningful learning experience enhances "
"their attentiveness and fosters heightened productivity."
msgstr ""

#: ../../content/applications/websites/elearning.rst:10
msgid ""
"You can manage your eLearning content on the **front end** or the **back "
"end**. The **front end** allows you to create content quickly from your "
"website, while the **back end** provides additional options and allows "
"collaboration. This documentation focuses on using the back end to create "
"your content."
msgstr ""

#: ../../content/applications/websites/elearning.rst:16
msgid ""
"`Odoo Tutorials: eLearning <https://www.odoo.com/slides/elearning-56>`_"
msgstr ""
"`دروس أودو التعليمية: التعلّم الإلكتروني "
"<https://www.odoo.com/slides/elearning-56>`_ "

#: ../../content/applications/websites/elearning.rst:19
msgid "Courses"
msgstr "الدورات"

#: ../../content/applications/websites/elearning.rst:21
msgid ""
"By going to :menuselection:`eLearning --> Courses --> Courses`, you can get "
"an overview of all your courses."
msgstr ""

#: ../../content/applications/websites/elearning.rst:24
msgid ""
"Click on a course title to edit your course on the back end. Click on "
":guilabel:`View course` to access your course on the front end."
msgstr ""

#: ../../content/applications/websites/elearning.rst:28
msgid "Course creation"
msgstr ""

#: ../../content/applications/websites/elearning.rst:30
msgid ""
"Click :guilabel:`New` to create a new course. When the page pops up, you can"
" add your :guilabel:`Course Title` and one or more :guilabel:`Tags` to "
"describe your course. You can add an image to illustrate your course by "
"hovering your mouse on the camera placeholder image and clicking on the edit"
" icon. Four tabs allow you to edit your course further: :ref:`Content "
"<elearning/content>`, :ref:`Description <elearning/description>`, "
":ref:`Options <elearning/options>`, and :ref:`Karma <elearning/karma>`."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Create your elearning course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:44
msgid "Content tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:46
msgid ""
"This tab allows you to manage your course content. Click on :guilabel:`Add "
"Section` to divide your course into different sections. Click on "
":guilabel:`Add Content` to create :ref:`content <elearning/create-content>`."
" Click on :guilabel:`Add Certification` to assess the level of understanding"
" of your attendees, certify their skills, and motivate them. "
"**Certification** is part of the :doc:`Surveys "
"<../marketing/surveys/create>` app."
msgstr ""

#: ../../content/applications/websites/elearning.rst:55
#: ../../content/applications/websites/elearning.rst:204
msgid "Description tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:57
msgid ""
"You can add a short description or information related to your course in the"
" :guilabel:`Description` tab. It appears under your course title on your "
"website."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Add a description to your course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:67
#: ../../content/applications/websites/livechat.rst:91
msgid "Options tab"
msgstr "شريط الخيارات "

#: ../../content/applications/websites/elearning.rst:69
msgid ""
"In the :guilabel:`Options` tab, different configurations are available: "
":ref:`Course <elearning/course>`,  :ref:`Communication "
"<elearning/communication>`, :ref:`Access rights <elearning/access-rights>`, "
"and :ref:`Display <elearning/display>`."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Overview of the Options tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:80
msgid "Course"
msgstr "دورة"

#: ../../content/applications/websites/elearning.rst:82
msgid ""
"Assign a :guilabel:`Responsible` user for your course. If you have multiple "
"websites, use the :guilabel:`Website` field to only display the course on "
"the selected website."
msgstr ""

#: ../../content/applications/websites/elearning.rst:88
msgid "Communication"
msgstr "التواصل "

#: ../../content/applications/websites/elearning.rst:90
msgid ""
":guilabel:`Allow Reviews`: tick the box to allow attendees to like and "
"comment on your content and to submit reviews on your course;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:92
msgid ""
":guilabel:`Forum`: add a dedicated forum to your course (only shown if the "
"**Forum** feature is enabled in the app's settings);"
msgstr ""

#: ../../content/applications/websites/elearning.rst:94
msgid ""
":guilabel:`New Content Notification`: select an email template sent to your "
"attendees when you upload new content. Click on the internal link button "
"(:guilabel:`➜`) to have access to the email template editor;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:97
msgid ""
":guilabel:`Completion Notification`: select an email template sent to your "
"attendees once they reach the end of your course. Click on the internal link"
" button (:guilabel:`➜`) to access the email template editor;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:104
msgid "Access rights"
msgstr ""

#: ../../content/applications/websites/elearning.rst:106
msgid ""
":guilabel:`Prerequisites`: set one or more courses that users are advised to"
" complete before"
msgstr ""

#: ../../content/applications/websites/elearning.rst:107
msgid "accessing your course;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:108
msgid ""
":guilabel:`Show course to`: define who can access your course and their "
"content between :guilabel:`Everyone`, :guilabel:`Signed In` or "
":guilabel:`Course Attendees`;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:110
msgid ""
":guilabel:`Enroll Policy`: define how people enroll in your course. Select:"
msgstr ""

#: ../../content/applications/websites/elearning.rst:112
msgid ":guilabel:`Open`: if you want your course to be available to anyone;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:113
msgid ""
":guilabel:`On Invitation`: if only people who received an invitation can "
"enroll to your course. If selected, fill in the :guilabel:`Enroll Message` "
"explaining the course's enrollment process. This message appears on your "
"website under the course title;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:116
msgid ""
":guilabel:`On Payment`: if only people who bought your course can attend it."
" The :guilabel:`Paid Courses` feature must be enabled to get this option. If"
" you select :guilabel:`On Payment`, you must add a :guilabel:`Product` for "
"your course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:121
msgid ""
"Only products set up with :guilabel:`Course` as their :guilabel:`Product "
"Type` are displayed."
msgstr ""

#: ../../content/applications/websites/elearning.rst:127
msgid "Display"
msgstr "عرض"

#: ../../content/applications/websites/elearning.rst:129
msgid ""
":guilabel:`Training`: the course content appears as a training program, and "
"the courses must be taken in the proposed order."
msgstr ""

#: ../../content/applications/websites/elearning.rst:131
msgid ""
":guilabel:`Documentation`: the content is available in any order. If you "
"choose this option, you can choose which page should be promoted on the "
"course homepage by using the :guilabel:`Featured Content` field."
msgstr ""

#: ../../content/applications/websites/elearning.rst:138
msgid "Karma tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:140
msgid "This tab is about gamification to make eLearning fun and interactive."
msgstr ""

#: ../../content/applications/websites/elearning.rst:142
msgid ""
"In the :guilabel:`Rewards` section, choose how many karma points you want to"
" grant your students when they :guilabel:`Review` or :guilabel:`Finish` a "
"course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:145
msgid ""
"In the :guilabel:`Access Rights` section, define the karma needed to "
":guilabel:`Add Review`, :guilabel:`Add Comment`, or :guilabel:`Vote` on the "
"course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:149
msgid ""
"From your course, click the :guilabel:`Contact Attendees` button to reach "
"people who are enrolled in the course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:155
msgid "Course groups"
msgstr ""

#: ../../content/applications/websites/elearning.rst:157
msgid ""
"Use the **Course Groups** to inform users and allow them to filter the "
"courses from the :guilabel:`All Courses` dashboard."
msgstr ""

#: ../../content/applications/websites/elearning.rst:160
msgid ""
"You can manage them by going to :menuselection:`Configuration --> Course "
"Groups`. Click :guilabel:`New` to create a new course group. Add the "
":guilabel:`Course Group Name`, tick the :guilabel:`Menu Entry` box to allow "
"users to search by course group on the website, and add tags in the "
":guilabel:`Tag Name` column. For each tag, you can select a corresponding "
"color."
msgstr ""

#: ../../content/applications/websites/elearning.rst:166
msgid "Settings"
msgstr "الإعدادات"

#: ../../content/applications/websites/elearning.rst:168
msgid ""
"You can enable different features to customize your courses by going to "
":menuselection:`eLearning --> Configuration --> Settings`:"
msgstr ""

#: ../../content/applications/websites/elearning.rst:171
msgid ""
"**Certifications**: to evaluate the knowledge of your attendees and certify "
"their skills;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:172
msgid ""
"**Paid courses**: to sell access to your courses on your website and track "
"revenues;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:173
msgid ""
"**Mailing**: to update all your attendees at once through mass mailings;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:174
msgid ""
"**Forum**: to create a community and let attendees answer each other's "
"questions."
msgstr ""

#: ../../content/applications/websites/elearning.rst:179
msgid "Content"
msgstr "المحتوى"

#: ../../content/applications/websites/elearning.rst:181
msgid ""
"Manage your content by going to :menuselection:`eLearning --> Courses --> "
"Contents`. Click :guilabel:`New` to create content. Add your "
":guilabel:`Content Title`, and if you want :ref:`Tags <elearning/tags>`, "
"then fill in the related information among the different tabs."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Create your content."
msgstr ""

#: ../../content/applications/websites/elearning.rst:190
msgid "Document tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:192
msgid ":guilabel:`Course`: select the course your content belongs to;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:193
msgid ":guilabel:`Content Type`: select the type of your content;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:194
msgid ":guilabel:`Responsible`: add a responsible person for your content;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:195
msgid ""
":guilabel:`Duration`: indicate the time required to complete the course;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:196
msgid ""
":guilabel:`Allow Download`: allow users to download the content of the "
"slide. This option is only visible when the content is a document;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:198
msgid ":guilabel:`Allow Preview`: the course is accessible by anyone."
msgstr ""

#: ../../content/applications/websites/elearning.rst:199
msgid ""
":guilabel:`# of Public Views`: displays the number of views from non-"
"enrolled participants;"
msgstr ""

#: ../../content/applications/websites/elearning.rst:200
msgid ""
":guilabel:`# Total Views`: displays the total number of views (non-enrolled "
"and enrolled participants)."
msgstr ""

#: ../../content/applications/websites/elearning.rst:206
msgid ""
"You can add a description of your content that appears front end in the "
":guilabel:`About` section of your course content."
msgstr ""

#: ../../content/applications/websites/elearning.rst:210
msgid "Additional Resources tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:212
msgid ""
"Click :guilabel:`Add a line` to add a link or a file that supports your "
"participants' learning. It appears in the course content on your website."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Additional ressources"
msgstr ""

#: ../../content/applications/websites/elearning.rst:222
msgid "Quiz tab"
msgstr ""

#: ../../content/applications/websites/elearning.rst:224
msgid ""
"From this tab you can create a quiz to assess your students at the end of "
"the course."
msgstr ""

#: ../../content/applications/websites/elearning.rst:226
msgid ""
"The :guilabel:`Points Rewards` section lets you give a specific number of "
"karma points depending on how many tries they need to correctly answer the "
"question. Then, create your questions and the possible answers by clicking "
"on :guilabel:`Add a line`. A new window pops up, add the question by filling"
" in the :guilabel:`Question Name` and add multiple answers by clicking on "
":guilabel:`Add a line`. Tick the :guilabel:`Is correct answer` to mark one "
"or more answers as correct. You can also fill in the :guilabel:`Comment` "
"field to display additional information when the answer is chosen by the "
"participant."
msgstr ""

#: ../../content/applications/websites/elearning.rst:237
msgid "Content Tags"
msgstr "علامات تصنيف المحتوى "

#: ../../content/applications/websites/elearning.rst:239
msgid ""
"The **Content Tags** help users to classify the content from the "
":guilabel:`Contents` dashboard."
msgstr ""

#: ../../content/applications/websites/elearning.rst:241
msgid ""
"You can manage them by going to :menuselection:`eLearning --> Configuration "
"--> Content Tags`. Click :guilabel:`New` to create a new tag."
msgstr ""

#: ../../content/applications/websites/elearning.rst:245
msgid "Publish your content"
msgstr ""

#: ../../content/applications/websites/elearning.rst:247
msgid ""
"Everything created on the back end needs to be published from the front end."
" Unpublished content is always visible from your website but still needs to "
"be published to be available to your audience."
msgstr ""

#: ../../content/applications/websites/elearning.rst:250
msgid ""
"You must be on your website's front end to publish your content. To do so, "
"click on the :guilabel:`Go To Website` smart button, and tick the "
":guilabel:`Publish` option available in the right-hand corner."
msgstr ""

#: ../../content/applications/websites/elearning.rst-1
msgid "Publish your content."
msgstr ""

#: ../../content/applications/websites/forum.rst:3
msgid "Forum"
msgstr "المنتدى"

#: ../../content/applications/websites/forum.rst:5
msgid ""
"**Odoo Forum** is a question-and-answer forum designed with providing "
"customer support in mind. Adding a forum to a website enables you to build a"
" community, encourage engagement, and share knowledge."
msgstr ""

#: ../../content/applications/websites/forum.rst:12
msgid "Create a forum"
msgstr ""

#: ../../content/applications/websites/forum.rst:14
msgid ""
"To create or edit a forum, go to :menuselection:`Website --> Configuration "
"--> Forum: Forums`. Click :guilabel:`New` or select an existing forum and "
"configure the following elements."
msgstr ""

#: ../../content/applications/websites/forum.rst:17
msgid ":guilabel:`Forum Name`: add the name of the forum."
msgstr ""

#: ../../content/applications/websites/forum.rst:19
msgid ""
":guilabel:`Mode`: select :guilabel:`Questions` to enable marking an answer "
"as best, meaning questions then appear as *solved*, or "
":guilabel:`Discussions` if the feature is not needed."
msgstr ""

#: ../../content/applications/websites/forum.rst:23
msgid ""
"Regardless of the selected mode, only **one answer** per user is allowed on "
"a single post. Commenting multiple times is allowed, however."
msgstr ""

#: ../../content/applications/websites/forum.rst:26
msgid ":guilabel:`Default Sort`: choose how questions are sorted by default."
msgstr ""

#: ../../content/applications/websites/forum.rst:28
msgid ":guilabel:`Newest`: by latest question posting date"
msgstr ""

#: ../../content/applications/websites/forum.rst:29
msgid ""
":guilabel:`Last Updated`: by latest posting activity date (answers and "
"comments included)"
msgstr ""

#: ../../content/applications/websites/forum.rst:30
msgid ":guilabel:`Most Voted`: by highest vote tally"
msgstr ""

#: ../../content/applications/websites/forum.rst:31
msgid ":guilabel:`Relevance`: by post relevancy (determined by a formula)"
msgstr ""

#: ../../content/applications/websites/forum.rst:32
msgid ""
":guilabel:`Answered`: by likelihood to be answered (determined by a formula)"
msgstr ""

#: ../../content/applications/websites/forum.rst:35
msgid ""
"Users have several sorting options (total replies, total views, last "
"activity) on the forum front end."
msgstr ""

#: ../../content/applications/websites/forum.rst:38
msgid ""
":guilabel:`Privacy`: select :guilabel:`Public` to let anyone view the forum,"
" :guilabel:`Signed In` to make it visible only for signed-in users, or "
":guilabel:`Some users` to make it visible only for a specific user access "
"group by selecting one :guilabel:`Authorized Group`."
msgstr ""

#: ../../content/applications/websites/forum.rst:42
msgid ""
"Next, configure the :ref:`karma gains <forum/karma-gains>` and the "
":ref:`karma-related rights <forum/karma-related-rights>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:48
msgid "Karma points"
msgstr ""

#: ../../content/applications/websites/forum.rst:50
msgid ""
"Karma points can be given to users based on different forum interactions. "
"They can be used to determine which forum functionalities users can access, "
"from being able to vote on posts to having moderator rights. They are also "
"used to set user :ref:`ranks <forum/ranks>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:55
msgid ""
"A user's karma points are shared across all forums, courses, etc., of a "
"single Odoo website."
msgstr ""

#: ../../content/applications/websites/forum.rst:56
msgid ""
"eLearning users can earn karma points through different :ref:`course "
"interactions <elearning/karma>` and by :ref:`completing quizzes "
"<elearning/quiz>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:62
msgid "Karma gains"
msgstr ""

#: ../../content/applications/websites/forum.rst:64
msgid "Several forum interactions can give or remove karma points."
msgstr ""

#: ../../content/applications/websites/forum.rst:70
msgid "Interaction"
msgstr ""

#: ../../content/applications/websites/forum.rst:71
#: ../../content/applications/websites/forum.rst:129
msgid "Description"
msgstr "الوصف"

#: ../../content/applications/websites/forum.rst:72
msgid "Default karma gain"
msgstr ""

#: ../../content/applications/websites/forum.rst:73
msgid ":guilabel:`Asking a question`"
msgstr ""

#: ../../content/applications/websites/forum.rst:74
msgid "You post a question."
msgstr ""

#: ../../content/applications/websites/forum.rst:75
#: ../../content/applications/websites/forum.rst:90
msgid "2"
msgstr "2"

#: ../../content/applications/websites/forum.rst:76
msgid ":guilabel:`Question upvoted`"
msgstr ""

#: ../../content/applications/websites/forum.rst:77
msgid "Another user votes for a question you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:78
#: ../../content/applications/websites/forum.rst:139
msgid "5"
msgstr "5"

#: ../../content/applications/websites/forum.rst:79
msgid ":guilabel:`Question downvoted`"
msgstr ""

#: ../../content/applications/websites/forum.rst:80
msgid "Another user votes against a question you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:81
#: ../../content/applications/websites/forum.rst:87
msgid "-2"
msgstr "-2"

#: ../../content/applications/websites/forum.rst:82
msgid ":guilabel:`Answer upvoted`"
msgstr ""

#: ../../content/applications/websites/forum.rst:83
msgid "Another user votes for an answer you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:84
msgid "10"
msgstr "10"

#: ../../content/applications/websites/forum.rst:85
msgid ":guilabel:`Answer downvoted`"
msgstr ""

#: ../../content/applications/websites/forum.rst:86
msgid "Another user votes against an answer you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:88
msgid ":guilabel:`Accepting an answer`"
msgstr ""

#: ../../content/applications/websites/forum.rst:89
msgid "You mark an answer posted by another user as best."
msgstr ""

#: ../../content/applications/websites/forum.rst:91
msgid ":guilabel:`Answer accepted`"
msgstr ""

#: ../../content/applications/websites/forum.rst:92
msgid "Another user marks an answer you posted as best."
msgstr ""

#: ../../content/applications/websites/forum.rst:93
msgid "15"
msgstr "15"

#: ../../content/applications/websites/forum.rst:94
msgid ":guilabel:`Answer flagged`"
msgstr ""

#: ../../content/applications/websites/forum.rst:95
msgid ""
"A question or an answer you posted is :ref:`marked as offensive "
"<forum/moderation>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:96
msgid "-100"
msgstr "-100"

#: ../../content/applications/websites/forum.rst:99
msgid ""
"New users receive **three points** upon validating their email address."
msgstr ""

#: ../../content/applications/websites/forum.rst:101
msgid ""
"To modify the default values, go to :menuselection:`Website --> "
"Configuration --> Forum: Forums`, select the forum, and go to the "
":guilabel:`Karma Gains` tab. Select a value to edit it."
msgstr ""

#: ../../content/applications/websites/forum.rst:104
msgid ""
"If the value is positive (e.g., `5`), the number of points will be added to "
"the user's tally each time the interaction happens on the selected forum. "
"Conversely, if the value is negative (e.g., `-5`), the number of points will"
" be deducted. Use `0` if an interaction should not impact a user's tally."
msgstr ""

#: ../../content/applications/websites/forum.rst:112
msgid "Karma-related rights"
msgstr ""

#: ../../content/applications/websites/forum.rst:114
msgid ""
"To configure how many karma points are required to access the different "
"forum functionalities, go to :menuselection:`Website --> Configuration --> "
"Forum: Forums`, select the forum, and go to the :guilabel:`Karma Related "
"Rights` tab. Select a value to edit it."
msgstr ""

#: ../../content/applications/websites/forum.rst:119
msgid ""
"Some functionalities, such as :guilabel:`Edit all posts`, :guilabel:`Close "
"all posts`, :guilabel:`Delete all posts`, :guilabel:`Moderate posts`, and "
":guilabel:`Unlink all comments`, are rather sensitive. Make sure to "
"understand the consequences of giving *any* user reaching the set karma "
"requirements access to such functionalities."
msgstr ""

#: ../../content/applications/websites/forum.rst:128
msgid "Functionality"
msgstr ""

#: ../../content/applications/websites/forum.rst:130
msgid "Default karma requirement"
msgstr ""

#: ../../content/applications/websites/forum.rst:131
msgid ":guilabel:`Ask questions`"
msgstr ""

#: ../../content/applications/websites/forum.rst:132
msgid "Post questions."
msgstr ""

#: ../../content/applications/websites/forum.rst:133
#: ../../content/applications/websites/forum.rst:136
msgid "3"
msgstr "3"

#: ../../content/applications/websites/forum.rst:134
msgid ":guilabel:`Answer questions`"
msgstr ""

#: ../../content/applications/websites/forum.rst:135
msgid "Post answers to questions."
msgstr ""

#: ../../content/applications/websites/forum.rst:137
msgid ":guilabel:`Upvote`"
msgstr ""

#: ../../content/applications/websites/forum.rst:138
msgid "Vote for questions or answers."
msgstr ""

#: ../../content/applications/websites/forum.rst:140
msgid ":guilabel:`Downvote`"
msgstr ""

#: ../../content/applications/websites/forum.rst:141
msgid "Vote against questions or answers."
msgstr ""

#: ../../content/applications/websites/forum.rst:142
#: ../../content/applications/websites/forum.rst:182
#: ../../content/applications/websites/forum.rst:188
msgid "50"
msgstr "50"

#: ../../content/applications/websites/forum.rst:143
msgid ":guilabel:`Edit own posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:144
msgid "Edit questions or answers you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:145
#: ../../content/applications/websites/forum.rst:176
#: ../../content/applications/websites/forum.rst:179
msgid "1"
msgstr "1"

#: ../../content/applications/websites/forum.rst:146
msgid ":guilabel:`Edit all posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:147
msgid "Edit any question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:148
msgid "300"
msgstr "300"

#: ../../content/applications/websites/forum.rst:149
msgid ":guilabel:`Close own posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:150
msgid "Close questions or answers you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:151
#: ../../content/applications/websites/forum.rst:194
msgid "100"
msgstr "100"

#: ../../content/applications/websites/forum.rst:152
msgid ":guilabel:`Close all posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:153
msgid "Close any question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:154
#: ../../content/applications/websites/forum.rst:157
#: ../../content/applications/websites/forum.rst:164
#: ../../content/applications/websites/forum.rst:170
#: ../../content/applications/websites/forum.rst:185
#: ../../content/applications/websites/forum.rst:191
#: ../../content/applications/websites/forum.rst:197
msgid "500"
msgstr "500"

#: ../../content/applications/websites/forum.rst:155
msgid ":guilabel:`Delete own posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:156
msgid "Delete questions or answers you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:158
msgid ":guilabel:`Delete all posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:159
msgid "Delete any question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:160
#: ../../content/applications/websites/forum.rst:200
msgid "1,000"
msgstr "1,000"

#: ../../content/applications/websites/forum.rst:161
msgid ":guilabel:`Nofollow links`"
msgstr ""

#: ../../content/applications/websites/forum.rst:162
msgid ""
"If you are under the karma threshold, a *nofollow* attribute tells search "
"engines to ignore links you post."
msgstr ""

#: ../../content/applications/websites/forum.rst:165
msgid ":guilabel:`Accept an answer on own questions`"
msgstr ""

#: ../../content/applications/websites/forum.rst:166
msgid "Mark an answer as best on questions you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:167
msgid "20"
msgstr "20"

#: ../../content/applications/websites/forum.rst:168
msgid ":guilabel:`Accept an answer to all questions`"
msgstr ""

#: ../../content/applications/websites/forum.rst:169
msgid "Mark an answer as best on any question."
msgstr ""

#: ../../content/applications/websites/forum.rst:171
msgid ":guilabel:`Editor Features: image and links`"
msgstr ""

#: ../../content/applications/websites/forum.rst:172
msgid "Add links and images to your posts."
msgstr ""

#: ../../content/applications/websites/forum.rst:173
#: ../../content/applications/websites/forum.rst:206
msgid "30"
msgstr "30"

#: ../../content/applications/websites/forum.rst:174
msgid ":guilabel:`Comment own posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:175
msgid "Post comments under questions or answers you created."
msgstr ""

#: ../../content/applications/websites/forum.rst:177
msgid ":guilabel:`Comment all posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:178
msgid "Post comments under any question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:180
msgid ":guilabel:`Convert own answers to comments and vice versa`"
msgstr ""

#: ../../content/applications/websites/forum.rst:181
msgid "Convert comments you posted as answers."
msgstr ""

#: ../../content/applications/websites/forum.rst:183
msgid ":guilabel:`Convert all answers to comments and vice versa`"
msgstr ""

#: ../../content/applications/websites/forum.rst:184
msgid "Convert any comment as answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:186
msgid ":guilabel:`Unlink own comments`"
msgstr ""

#: ../../content/applications/websites/forum.rst:187
msgid "Delete comments you posted."
msgstr ""

#: ../../content/applications/websites/forum.rst:189
msgid ":guilabel:`Unlink all comments`"
msgstr ""

#: ../../content/applications/websites/forum.rst:190
msgid "Delete any comment."
msgstr ""

#: ../../content/applications/websites/forum.rst:192
msgid ":guilabel:`Ask questions without validation`"
msgstr ""

#: ../../content/applications/websites/forum.rst:193
msgid ""
"Questions you post do not require to be :ref:`validated <forum/moderation>` "
"first."
msgstr ""

#: ../../content/applications/websites/forum.rst:195
msgid ":guilabel:`Flag a post as offensive`"
msgstr ""

#: ../../content/applications/websites/forum.rst:196
msgid "Flag a question or answer as offensive."
msgstr ""

#: ../../content/applications/websites/forum.rst:198
msgid ":guilabel:`Moderate posts`"
msgstr ""

#: ../../content/applications/websites/forum.rst:199
msgid "Access all :ref:`moderation tools <forum/moderation>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:201
msgid ":guilabel:`Change question tags`"
msgstr ""

#: ../../content/applications/websites/forum.rst:202
msgid ""
"Change posted questions' :ref:`tags <forum/tags>` (if you have the right to "
"edit them)."
msgstr ""

#: ../../content/applications/websites/forum.rst:203
msgid "75"
msgstr "75"

#: ../../content/applications/websites/forum.rst:204
msgid ":guilabel:`Create new tags`"
msgstr ""

#: ../../content/applications/websites/forum.rst:205
msgid "Create new :ref:`tags <forum/tags>` when posting questions."
msgstr ""

#: ../../content/applications/websites/forum.rst:207
msgid ":guilabel:`Display detailed user biography`"
msgstr ""

#: ../../content/applications/websites/forum.rst:208
msgid ""
"When a user hovers their mouse on your avatar or username, a popover box "
"showcases your karma points, biography, and number of :ref:`badges "
"<forum/badges>` per level."
msgstr ""

#: ../../content/applications/websites/forum.rst:210
msgid "750"
msgstr "750"

#: ../../content/applications/websites/forum.rst:213
msgid ""
"Track all karma-related activity and add or remove karma manually by "
":ref:`enabling developer mode <developer-mode>` and going to "
":menuselection:`Settings --> Gamification Tools --> Karma Tracking`."
msgstr ""

#: ../../content/applications/websites/forum.rst:220
msgid "Gamification"
msgstr "التلعيب "

#: ../../content/applications/websites/forum.rst:222
msgid ""
"Ranks and badges can be used to encourage participation. Ranks are based on "
"the total :ref:`karma points <forum/karma>`, while badges can be granted "
"manually or automatically by completing challenges."
msgstr ""

#: ../../content/applications/websites/forum.rst:229
msgid "Ranks"
msgstr "الرتب "

#: ../../content/applications/websites/forum.rst:231
msgid ""
"To create new ranks or modify the default ones, go to "
":menuselection:`Website --> Configuration --> Forum: Ranks` and click "
":guilabel:`New` or select an existing rank."
msgstr ""

#: ../../content/applications/websites/forum.rst:234
msgid ""
"Add the :guilabel:`Rank Name`, the :guilabel:`Required Karma` points to "
"reach it, its :guilabel:`Description`, a :guilabel:`Motivational` message to"
" encourage users to reach it, and an image."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Default forum ranks"
msgstr ""

#: ../../content/applications/websites/forum.rst:244
msgid "Badges"
msgstr "الشارات"

#: ../../content/applications/websites/forum.rst:246
msgid ""
"To create new badges or modify the default ones, go to "
":menuselection:`Website --> Configuration --> Forum: Badges` and click "
":guilabel:`New` or select an existing badge."
msgstr ""

#: ../../content/applications/websites/forum.rst:249
msgid "Enter the badge name and description, add an image, and configure it."
msgstr ""

#: ../../content/applications/websites/forum.rst:252
msgid "Assign manually"
msgstr ""

#: ../../content/applications/websites/forum.rst:254
msgid ""
"If the badge should be granted manually, select which users can grant them "
"by selecting one of the following :guilabel:`Allowance to Grant` options:"
msgstr ""

#: ../../content/applications/websites/forum.rst:257
msgid ""
":guilabel:`Everyone`: all non-portal users (since badges are granted from "
"the backend)."
msgstr ""

#: ../../content/applications/websites/forum.rst:258
msgid ""
":guilabel:`A selected list of users`: users selected under "
":guilabel:`Authorized Users`."
msgstr ""

#: ../../content/applications/websites/forum.rst:259
msgid ""
":guilabel:`People having some badges`: users who have been granted the "
"badges selected under :guilabel:`Required Badges`."
msgstr ""

#: ../../content/applications/websites/forum.rst:262
msgid ""
"It is possible to restrict how many times per month each user can grant the "
"badge by enabling :guilabel:`Monthly Limited Sending` and entering a "
":guilabel:`Limitation Number`."
msgstr ""

#: ../../content/applications/websites/forum.rst:266
msgid "Assign automatically"
msgstr ""

#: ../../content/applications/websites/forum.rst:268
msgid ""
"If the badge should be granted **automatically** when certain conditions are"
" met, select :guilabel:`No one, assigned through challenges` under "
":guilabel:`Allowance to Grant`."
msgstr ""

#: ../../content/applications/websites/forum.rst:271
msgid ""
"Next, determine how the badge should be granted by clicking :guilabel:`Add` "
"under the :guilabel:`Rewards for challenges` section. Select a challenge to "
"add it or create one by clicking :guilabel:`New`."
msgstr ""

#: ../../content/applications/websites/forum.rst:276
msgid ""
"It is possible to give the badge a :guilabel:`Forum Badge Level` "
"(:guilabel:`Bronze`, :guilabel:`Silver`, :guilabel:`Gold`) to give it more "
"or less importance."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Default forum badges"
msgstr ""

#: ../../content/applications/websites/forum.rst:285
msgid "Tags"
msgstr "علامات التصنيف "

#: ../../content/applications/websites/forum.rst:287
msgid "Users can use tags to filter forum posts."
msgstr ""

#: ../../content/applications/websites/forum.rst:289
msgid ""
"To manage tags, go to :menuselection:`Website --> Configuration --> Forum: "
"Tags`. Click :guilabel:`New` to create a tag and select the related "
":guilabel:`Forum`."
msgstr ""

#: ../../content/applications/websites/forum.rst:293
msgid ""
"Use the :guilabel:`Tags` section on the forum's sidebar to filter all "
"questions assigned to the selected tag. Click :guilabel:`View all` to "
"display all tags."
msgstr ""

#: ../../content/applications/websites/forum.rst:295
msgid ""
"New tags can be created when posting a new message, provided the user has "
"enough :ref:`karma points <forum/karma-related-rights>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:301
msgid "Use a forum"
msgstr ""

#: ../../content/applications/websites/forum.rst:304
msgid ""
"Access to many functionalities depends on a user's :ref:`karma points "
"<forum/karma-related-rights>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:310
msgid "Post questions"
msgstr ""

#: ../../content/applications/websites/forum.rst:312
msgid ""
"To create a new post, access the forum's front end, click :guilabel:`New "
"Post`, and fill in the following:"
msgstr ""

#: ../../content/applications/websites/forum.rst:315
msgid ":guilabel:`Title`: add the question or the topic of the post."
msgstr ""

#: ../../content/applications/websites/forum.rst:316
msgid ":guilabel:`Description`: add a description for the question."
msgstr ""

#: ../../content/applications/websites/forum.rst:317
msgid ":guilabel:`Tags`: add up to five :ref:`tags <forum/tags>`."
msgstr ""

#: ../../content/applications/websites/forum.rst:319
msgid "Click :guilabel:`Post Your Question`."
msgstr ""

#: ../../content/applications/websites/forum.rst:324
msgid "Interact with posts"
msgstr ""

#: ../../content/applications/websites/forum.rst:326
msgid "Different actions are possible on a post."
msgstr ""

#: ../../content/applications/websites/forum.rst:328
msgid ""
"Mark a question as **favorite** by clicking the star button (:guilabel:`☆`)."
msgstr ""

#: ../../content/applications/websites/forum.rst:329
msgid ""
"Follow a post and get **notifications** (by email or within Odoo) when it is"
" answered by clicking the bell button (:guilabel:`🔔`)."
msgstr ""

#: ../../content/applications/websites/forum.rst:331
msgid ""
"**Vote** *for* (up arrow :guilabel:`▲`) or *against* (down arrow "
":guilabel:`▼`) a question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:333
msgid ""
"Mark an answer as **best** by clicking the check mark button "
"(:guilabel:`✔`). This option is only available if the :guilabel:`Forum Mode`"
" is set to :guilabel:`Questions`."
msgstr ""

#: ../../content/applications/websites/forum.rst:335
msgid ":guilabel:`Answer` a question."
msgstr ""

#: ../../content/applications/websites/forum.rst:336
msgid ""
"**Comment** on a question or answer by clicking the speech bubble button "
"(:guilabel:`💬`)."
msgstr ""

#: ../../content/applications/websites/forum.rst:337
msgid ""
"**Share** a question on Facebook, Twitter, or LinkedIn by clicking the "
"*share nodes* button."
msgstr ""

#: ../../content/applications/websites/forum.rst:339
msgid "Click the ellipsis button (:guilabel:`...`) to:"
msgstr ""

#: ../../content/applications/websites/forum.rst:341
msgid ":guilabel:`Edit` a question or answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:342
msgid ":guilabel:`Close` a question."
msgstr ""

#: ../../content/applications/websites/forum.rst:343
msgid ""
":guilabel:`Delete` a question, answer, or comment. It is possible to "
":guilabel:`Undelete` questions afterward."
msgstr ""

#: ../../content/applications/websites/forum.rst:345
msgid ":guilabel:`Flag` a question or answer as offensive."
msgstr ""

#: ../../content/applications/websites/forum.rst:346
msgid ":guilabel:`Convert` a comment into an answer."
msgstr ""

#: ../../content/applications/websites/forum.rst:347
msgid ""
":guilabel:`View` the related :ref:`Helpdesk ticket <helpdesk/forum>`, if "
"any."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Posts actions"
msgstr ""

#: ../../content/applications/websites/forum.rst:353
msgid ""
"By default, 150 karma points are required to view another user's profile. "
"This value can be configured when creating a new website."
msgstr ""

#: ../../content/applications/websites/forum.rst:359
msgid "Moderate a forum"
msgstr ""

#: ../../content/applications/websites/forum.rst:361
msgid ""
"On the forum's front end, the sidebar's :guilabel:`Moderation tools` section"
" gathers the essential moderator functionalities."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Forum sidebar moderation tools"
msgstr ""

#: ../../content/applications/websites/forum.rst:367
msgid ""
":guilabel:`To Validate`: access all questions and answers waiting for "
"validation before being displayed to non-moderator users."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Question to validate"
msgstr ""

#: ../../content/applications/websites/forum.rst:374
msgid ""
"A question is pending if a user does not have the required karma. The user "
"is not able to post questions or answers while awaiting validation. Only one"
" pending question per user is allowed per forum."
msgstr ""

#: ../../content/applications/websites/forum.rst:378
msgid ""
":guilabel:`Flagged`: access all questions and answers that have been flagged"
" as offensive. Click :guilabel:`Accept` to remove the offensive flag or "
":guilabel:`Offensive` to confirm it, then select a reason and click "
":guilabel:`Mark as offensive`. The post is then hidden from users without "
"moderation rights, and 100 karma points are deducted from the offending "
"user's tally."
msgstr ""

#: ../../content/applications/websites/forum.rst-1
msgid "Offensive reason selection"
msgstr ""

#: ../../content/applications/websites/forum.rst:386
msgid ""
":guilabel:`Closed`: access all questions that have been closed. It is "
"possible to :guilabel:`Delete` or :guilabel:`Reopen` them. To close a "
"question, open it, click the ellipsis button (:guilabel:`...`), then "
":guilabel:`Close`, select a :guilabel:`Close Reason`, and click "
":guilabel:`Close post`. The post is then hidden from users without "
"moderation rights."
msgstr ""

#: ../../content/applications/websites/forum.rst:392
msgid ""
"When selecting :guilabel:`Spam or advertising` or :guilabel:`Contains "
"offensive or malicious remarks` as the reason, 100 karma points are deducted"
" from the poster's tally."
msgstr ""

#: ../../content/applications/websites/forum.rst:396
msgid ""
"Create and edit close reasons by going to :menuselection:`Website --> "
"Configuration --> Forum: Close Reasons`. Select :guilabel:`Basic` as "
":guilabel:`Reason Type` if the reason should be used when closing a "
"question, and :guilabel:`Offensive` if it should be used for flagged posts."
msgstr ""

#: ../../content/applications/websites/forum.rst:399
msgid ""
"Manage all posts by going to :menuselection:`Website  --> Configuration --> "
"Forum: Forums`, selecting the forum, and clicking the :guilabel:`Posts` "
"smart button. By clicking the :guilabel:`Actions` button, it is possible to "
":guilabel:`Export`, :guilabel:`Archive`, :guilabel:`Unarchive`, or "
":guilabel:`Delete` one or multiple posts."
msgstr ""

#: ../../content/applications/websites/livechat.rst:5
msgid "Live Chat"
msgstr "الدردشة المباشرة "

#: ../../content/applications/websites/livechat.rst:7
msgid ""
"Odoo **Live Chat** allows users to communicate with website visitors in "
"real-time. With **Live Chat**, leads can be qualified for their sales "
"potential, support questions can be answered in real time, and issues can be"
" directed to the appropriate team for further investigation or follow up. "
"**Live Chat** also provides the opportunity for instant feedback from "
"customers."
msgstr ""

#: ../../content/applications/websites/livechat.rst:13
msgid "Enable Live Chat"
msgstr ""

#: ../../content/applications/websites/livechat.rst:15
msgid "The **Live Chat** application can be installed multiple ways:"
msgstr ""

#: ../../content/applications/websites/livechat.rst:17
msgid ""
"Go to the :menuselection:`Apps application`, search `Live Chat`, and click "
":guilabel:`Install`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:18
msgid ""
"Go to the :menuselection:`Helpdesk app --> Configuration --> Helpdesk Teams`"
" list view, select a team, and on the team's settings page, click the "
"checkbox next to :guilabel:`Live Chat`, under the :guilabel:`Channels` "
"section."
msgstr ""

#: ../../content/applications/websites/livechat.rst:21
msgid ""
"In the :menuselection:`Website` app, go to :menuselection:`Configuration -->"
" Settings`, scroll to the :guilabel:`Email & Marketing` section, check the "
"box next to :guilabel:`Livechat`, and click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:0
msgid ""
"View of the settings page and the live chat feature for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat.rst:29
msgid ""
"After the **Live Chat** application is installed, a live chat *Channel* is "
"created, by default."
msgstr ""

#: ../../content/applications/websites/livechat.rst:32
msgid "Create live chat channels"
msgstr ""

#: ../../content/applications/websites/livechat.rst:34
msgid ""
"To create a new live chat *Channel*, go to the :menuselection:`Live Chat "
"app`. Then, click :guilabel:`New` to open a blank channel detail form. Enter"
" the name of the new channel in the :guilabel:`Channel Name` field."
msgstr ""

#: ../../content/applications/websites/livechat.rst:38
msgid ""
"To configure the remaining tabs on the channel detail form (:ref:`Operators "
"<livechat/operators-tab>`, :ref:`Options <livechat/options-tab>`, "
":ref:`Channel Rules <livechat/channel-rules-tab>`, and :ref:`Widget "
"<livechat/widget-tab>`), follow the steps below."
msgstr ""

#: ../../content/applications/websites/livechat.rst-1
msgid "View of a live chat channel form for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat.rst:46
msgid ""
"The channel detail form for any channel can be accessed by navigating back "
"to the :guilabel:`Website Live Chat Channels` dashboard, via the "
"breadcrumbs. Find the Kanban card for the appropriate live chat channel, "
"hover over it, and then click on the :icon:`fa-ellipsis-v` "
":guilabel:`(vertical ellipsis)` icon to open the drop-down menu. Click "
":guilabel:`Configure Channel` to open the channel detail form."
msgstr ""

#: ../../content/applications/websites/livechat.rst:55
msgid "Operators tab"
msgstr ""

#: ../../content/applications/websites/livechat.rst:57
msgid ""
"*Operators* are the users who act as agents and respond to live chat "
"requests from customers. When a user is added as an operator in a live chat "
"channel, they can receive chats from website visitors wherever they are in "
"the database. Chat windows open in the bottom-right corner of the screen."
msgstr ""

#: ../../content/applications/websites/livechat.rst-1
msgid "A live chat pop-up window in an Odoo database."
msgstr ""

#: ../../content/applications/websites/livechat.rst:64
msgid ""
"On the channel detail form, click the :guilabel:`Operators` tab. The user "
"who originally created the live chat channel has been added as an operator "
"by default."
msgstr ""

#: ../../content/applications/websites/livechat.rst:68
msgid ""
"Current operators can be edited, or removed, by clicking on their respective"
" boxes in the :guilabel:`Operators` tab, which reveals a separate "
":guilabel:`Open: Operators` pop-up. In that pop-up, adjust any information, "
"as needed. Then, click :guilabel:`Save`, or click :guilabel:`Remove` to "
"remove that operator from the channel."
msgstr ""

#: ../../content/applications/websites/livechat.rst:73
msgid ""
"Click :guilabel:`Add` to reveal an :guilabel:`Add: Operators` pop-up window."
msgstr ""

#: ../../content/applications/websites/livechat.rst:75
msgid ""
"In the pop-up window, scroll to find the desired users, or enter their name "
"in the search bar. Then, tick the checkbox next to the users to be added, "
"and click :guilabel:`Select`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:78
msgid ""
"New operators can be created and added to the list directly from this pop-up"
" window, as well, by clicking :guilabel:`New`, and filling out the "
":guilabel:`Create Operators` form. When the form is complete, click "
":guilabel:`Save & Close`, or :guilabel:`Save & New` for multiple record "
"creations."
msgstr ""

#: ../../content/applications/websites/livechat.rst:83
msgid ""
"Creating a new user can impact the status of an Odoo subscription, as the "
"total number of users in a database counts towards the billing rate. Proceed"
" with caution before creating a new user. If a user already exists, adding "
"them as an operator will **not** alter the subscription or billing rate for "
"a database."
msgstr ""

#: ../../content/applications/websites/livechat.rst:93
msgid ""
"The :guilabel:`Options` tab on the live chat channel detail form contains "
"the visual and text settings for the live chat window."
msgstr ""

#: ../../content/applications/websites/livechat.rst:99
msgid "Livechat button"
msgstr ""

#: ../../content/applications/websites/livechat.rst:101
msgid ""
"The *Livechat Button* is the icon that appears in the bottom-right corner of"
" the website."
msgstr ""

#: ../../content/applications/websites/livechat.rst:103
msgid ""
"Change the text in the :guilabel:`Notification text` field to update the "
"greeting displayed in the text bubble when the live chat button appears on "
"the website."
msgstr ""

#: ../../content/applications/websites/livechat.rst:106
msgid ""
"The :guilabel:`Livechat Button Color` alters the color of the live chat "
"button as it appears on the website. To change the color, click on a color "
"bubble to open the color selection window, then click and drag the circle "
"along the color gradient. Click out of the selection window once complete. "
"Click the :icon:`fa-refresh` :guilabel:`(refresh)` icon to the right of the "
"color bubbles to reset the colors to the default selection."
msgstr ""

#: ../../content/applications/websites/livechat.rst:113
msgid ""
"Color selection, for the button or header, can be made manually using a "
"slider or through RGB, HSL, or HEX color code entries from the pop-up color "
"selection window that appears when either of the color bubbles are clicked. "
"Different options are available, depending on the operating system."
msgstr ""

#: ../../content/applications/websites/livechat.rst:119
msgid ""
"With the following settings, the live chat button appears on the website as "
"shown:"
msgstr ""

#: ../../content/applications/websites/livechat.rst:121
msgid ":guilabel:`Notification text`: \"Have a Question? Chat with us.\""
msgstr ""

#: ../../content/applications/websites/livechat.rst:122
msgid ":guilabel:`Livechat Button Color`: set to purple"
msgstr ""

#: ../../content/applications/websites/livechat.rst:0
msgid "View of an Odoo website emphasizing the livechat button."
msgstr ""

#: ../../content/applications/websites/livechat.rst:128
msgid "Livechat window"
msgstr ""

#: ../../content/applications/websites/livechat.rst:130
msgid ""
"The :guilabel:`Livechat Window` is the space where the live chat "
"conversation with website visitors takes place."
msgstr ""

#: ../../content/applications/websites/livechat.rst:133
msgid ""
"Edit the :guilabel:`Welcome Message` to change the message a visitor sees "
"when they open a new chat session. This message appears as though it is sent"
" by a live chat operator, and acts as both a greeting and an invitation to "
"continue the conversation."
msgstr ""

#: ../../content/applications/websites/livechat.rst:137
msgid ""
"Edit the :guilabel:`Chat Input Placeholder` to alter the text that appears "
"in the box where visitors type their replies. This message prompts the "
"visitor to initiate the chat."
msgstr ""

#: ../../content/applications/websites/livechat.rst:140
msgid ""
"The *Channel Header* is the colored bar at the top of the chat window. The "
":guilabel:`Channel Header Color` can be changed following the same steps as "
"the :ref:`Livechat button <livechat/livechat-button>`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:147
msgid ""
"The live chat window with a purple channel header and placeholder text."
msgstr ""

#: ../../content/applications/websites/livechat.rst:147
msgid ""
"The live chat window with a purple channel header and placeholder text that "
"reads, \"Say Something...\""
msgstr ""

#: ../../content/applications/websites/livechat.rst:153
msgid "Channel Rules tab"
msgstr ""

#: ../../content/applications/websites/livechat.rst:155
msgid ""
"To configure which website user actions open the live chat window, go to the"
" :guilabel:`Channel Rules` tab on the live chat channel detail form."
msgstr ""

#: ../../content/applications/websites/livechat.rst:158
msgid ""
"To create a new channel rule, click :guilabel:`Add a line`. This opens the "
":guilabel:`Create Rules` pop-up window."
msgstr ""

#: ../../content/applications/websites/livechat.rst-1
msgid "View of a channel's rules form for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat.rst:165
msgid "Create new rules"
msgstr ""

#: ../../content/applications/websites/livechat.rst:167
msgid ""
"Fill out the fields on the :guilabel:`Create Rules` pop-up window as "
"instructed below, then click :guilabel:`Save & Close`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:172
msgid "Live Chat Button"
msgstr "زر الدردشة المباشرة "

#: ../../content/applications/websites/livechat.rst:174
msgid ""
"The *Livechat Button* is the icon that appears in the bottom-right corner of"
" the website. Select from one of the following display options:"
msgstr ""

#: ../../content/applications/websites/livechat.rst:177
msgid ":guilabel:`Show`: displays the chat button on the page."
msgstr ""

#: ../../content/applications/websites/livechat.rst:178
msgid ""
":guilabel:`Show with notification`: displays the chat button, as well as a "
"floating text bubble next to the button."
msgstr ""

#: ../../content/applications/websites/livechat.rst:180
msgid ""
":guilabel:`Open automatically`: displays the button, and automatically opens"
" the chat window after a specified amount of time (designated in the "
":guilabel:`Open automatically timer` field, that appears when this option is"
" selected)."
msgstr ""

#: ../../content/applications/websites/livechat.rst:183
msgid ":guilabel:`Hide`: hides the chat button on the page."
msgstr ""

#: ../../content/applications/websites/livechat.rst:185
msgid "Chatbot"
msgstr "برنامج الدردشة الآلي "

#: ../../content/applications/websites/livechat.rst:187
msgid ""
"To include a :doc:`Chatbot <livechat/chatbots>` on this channel, select it "
"from the drop-down menu. If the chatbot should only be active when no "
"operators are active, check the box labeled :guilabel:`Enabled only if no "
"operator`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:191
msgid ""
"The :guilabel:`Enabled only if no operator` field is **only** visible if a "
"chatbot is selected in the :guilabel:`Chatbot` field."
msgstr ""

#: ../../content/applications/websites/livechat.rst:194
msgid "URL Regex"
msgstr "التعبير النمطي لرابط URL "

#: ../../content/applications/websites/livechat.rst:196
msgid ""
"The *URL Regex* specifies the web pages where this rule should be applied. "
"In the :guilabel:`URL Regex` field, input the relative URL of the page where"
" the chat button should appear."
msgstr ""

#: ../../content/applications/websites/livechat.rst:200
msgid ""
"For example, to apply the rule to the URL, "
"`https://mydatabse.odoo.com/shop`, enter `/shop` to the :guilabel:`URL "
"Regex` field."
msgstr ""

#: ../../content/applications/websites/livechat.rst:203
msgid ""
"To apply the rule to *all* pages on the database, enter `/` in the "
":guilabel:`URL Regex` field."
msgstr ""

#: ../../content/applications/websites/livechat.rst:206
msgid "Open automatically timer"
msgstr "مؤقت الفتح تلقائياً "

#: ../../content/applications/websites/livechat.rst:208
msgid ""
"This field designates the amount of time (in seconds) a page should be open "
"before the chat window opens. This field **only** appears if the "
":guilabel:`Live Chat Button` for this rule is set to :guilabel:`Open "
"automatically`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:212
msgid "Country"
msgstr "الدولة"

#: ../../content/applications/websites/livechat.rst:214
msgid ""
"If this channel should **only** be available to site visitors in specific "
"countries, add them to the :guilabel:`Country` field. If this field is left "
"blank, the channel is available to all site visitors, regardless of "
"location."
msgstr ""

#: ../../content/applications/websites/livechat.rst:219
msgid ""
"In order to track the geographical location of visitors, *GeoIP* **must** be"
" installed on the database. While this feature is installed by default on "
"*Odoo Online* databases, *On-Premise* databases require additional "
":doc:`setup steps </administration/on_premise/geo_ip>`."
msgstr ""

#: ../../content/applications/websites/livechat.rst:226
msgid "Widget tab"
msgstr ""

#: ../../content/applications/websites/livechat.rst:228
msgid ""
"The :guilabel:`Widget` tab on the live chat channel detail form provides an "
"embeddable code snippet for external, non-Odoo websites. This code can be "
"added to a website to provide access to a live chat window."
msgstr ""

#: ../../content/applications/websites/livechat.rst:233
msgid ""
"The live chat widget can be added to websites created through Odoo by "
"navigating to :menuselection:`Website app --> Configuration --> Settings`. "
"Then, scroll to the :menuselection:`Email & Marketing` section. In the "
":guilabel:`Channel` field, select the channel to add to the site. Click "
":guilabel:`Save` to apply."
msgstr ""

#: ../../content/applications/websites/livechat.rst:238
msgid ""
"To add the widget to a website created on a third-party platform, click the "
"first :icon:`fa-clipboard` :guilabel:`(copy)` icon on the :guilabel:`Widget`"
" tab, and paste the code into the `<head>` tag on the site."
msgstr ""

#: ../../content/applications/websites/livechat.rst:242
msgid ""
"Likewise, to send a live chat session to a customer, click the second "
":icon:`fa-clipboard` :guilabel:`(copy)` icon on the :guilabel:`Widget` tab. "
"This link can be sent directly to a customer. When they click the link, they"
" are redirected to a new chat window."
msgstr ""

#: ../../content/applications/websites/livechat.rst-1
msgid "View of the widget tab for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat.rst:250
msgid ":doc:`../productivity/discuss`"
msgstr ""

#: ../../content/applications/websites/livechat.rst:251
msgid ":doc:`livechat/responses`"
msgstr ""

#: ../../content/applications/websites/livechat.rst:252
msgid ":doc:`livechat/ratings`"
msgstr ""

#: ../../content/applications/websites/livechat.rst:253
msgid ":doc:`livechat/chatbots`"
msgstr ""

#: ../../content/applications/websites/livechat.rst:254
msgid ":doc:`livechat/participate`"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:3
msgid "Chatbots"
msgstr "برامج الدردشة الآلية "

#: ../../content/applications/websites/livechat/chatbots.rst:5
msgid ""
"A *Chatbot* is a program designed to mimic a conversation with a live human."
" Chatbots are assigned a script of pre-written steps to follow. The scripts "
"are designed to anticipate a visitor's potential response, and lead them "
"through a series of questions and answers the same way a live team member "
"would."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:10
msgid ""
"Chatbots can be customized to fill various roles, from customer support, to "
"creating leads, to collecting contact information. The goal of each chatbot "
"can vary based on several criteria, including the webpage where it is "
"located, and the information it captures."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst-1
#: ../../content/applications/websites/livechat/responses.rst:0
msgid ""
"View of the chat window with a helpdesk ticket created in Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:19
msgid "Build a chatbot"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:21
msgid ""
"Before creating a new chatbot, the *Live Chat* application must first be "
"installed on the database. This can be done directly from the "
":menuselection:`Apps` application, by searching for `Live Chat` in the "
":guilabel:`Search...` bar, and clicking :guilabel:`Install`."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:25
msgid ""
"Once the *Live Chat* application has been installed on the database, go to "
":menuselection:`Live Chat app --> Configuration --> Chatbots`."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:29
msgid ""
"When the *Live Chat* app is installed, a sample chatbot is created, named "
"*Welcome Bot*. This chatbot has a preconfigured script that walks through a "
"few basic steps, including asking for a visitor's email address, and "
"forwarding the conversation to an operator."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:33
msgid ""
"*Welcome Bot* can be used as a starting point. The existing steps can be "
"edited or removed, and new steps can be added to customize the script, as "
"needed."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:36
msgid "*Welcome Bot* can be deleted or archived, if necessary."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:0
msgid "View of the Welcome Bot script in Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:42
msgid ""
"To create a new chatbot, navigate to the :guilabel:`Chatbot` page "
"(:menuselection:`Live Chat app --> Configuration --> Chatbots`) and click "
":guilabel:`New`. This opens a blank chatbot details page."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:45
msgid ""
"On the blank chatbot details page, enter a name in the :guilabel:`Chatbot "
"Name` field and click on the :guilabel:`Edit Image` icon in the upper right "
"corner of the form to add a photo."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:49
msgid "Chatbot scripts"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:51
msgid ""
"After the new chatbot has been created and named, the next step is to create"
" a script. Chatbot conversations follow an accompanying script. These "
"scripts are comprised of lines of dialog, each designed to deliver or "
"capture information."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:55
msgid ""
"To create a chatbot script, click :guilabel:`Add a Line` in the "
":guilabel:`Script` tab of the chatbot detail page, and a :guilabel:`Create "
"Script Steps` modal appears."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:59
msgid ""
"This form must be filled out for **each** line of text (dialog) that the "
"chatbot could deliver during the conversation."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:62
msgid ""
"First, enter the content of the message in the :guilabel:`Message` field. "
"Then, select an option from the :guilabel:`Step Type` and :guilabel:`Only "
"If` drop-down menus."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:66
msgid "Step types"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:68
msgid ""
"The :guilabel:`Step Type` selected depends on the intended purpose of the "
"message. The available options in the :guilabel:`Step Type` drop-down menu "
"are detailed below:"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:72
msgid "Text"
msgstr "النص"

#: ../../content/applications/websites/livechat/chatbots.rst:74
msgid ""
"This step is used for messages where no answer is expected or necessary. "
"Text steps can be used for greetings, to offer resources, such as "
"documentation, or provide links to specific web pages."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:78
msgid ""
"*Text* step types are only intended to deliver information, and do **not** "
"allow for any visitor input. As such, they **must** be followed by "
"additional steps to continue the conversation."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:82
msgid "Question"
msgstr "السؤال"

#: ../../content/applications/websites/livechat/chatbots.rst:84
msgid ""
"This step asks a question and provides a set of answers. The visitor clicks "
"on one answer, which either leads to a new step in the conversation, or can "
"lead to an optional link to a new webpage."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:87
msgid ""
"Enter the question in the :guilabel:`Message` field. Then, under the "
":guilabel:`Answer` heading, click :guilabel:`Add a Line` to create a blank "
"answer line."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:90
msgid ""
"Proceed to enter the answer as it should appear to the visitor. To turn the "
"answer into a link that redirects the visitor when selected, add the URL to "
"the answer line under the :guilabel:`Optional Link` heading."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:94
msgid "Repeat these steps for every answer to be included for the question."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:96
msgid "Click :guilabel:`Save & Close` or :guilabel:`Save & New`."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:99
msgid ""
"It is helpful to add a catchall answer to question steps (e.g: `Something "
"else`). This helps visitors continue the conversation, even if their needs "
"do not exactly fit with any of the other answers."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:104
msgid "Email"
msgstr "البريد الإلكتروني"

#: ../../content/applications/websites/livechat/chatbots.rst:106
msgid ""
"This step prompts visitors to provide their email address, which is stored "
"and can be used by team members later to follow up with additional "
"information."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:109
msgid ""
"The **only** accepted inputs for this step type are email addresses that are"
" in a valid format. If a visitor attempts to enter anything other than a "
"valid email address, the chatbot responds with a message stating it does not"
" recognize the information submitted."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst-1
msgid "View of a chatbot responding to an invalid email."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:118
msgid "Phone"
msgstr "رقم الهاتف"

#: ../../content/applications/websites/livechat/chatbots.rst:120
msgid ""
"Similar to email, this step type prompts the visitor to enter their phone "
"number, which can be used to follow up with additional information, schedule"
" demos, and more."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:124
msgid ""
"Due to the vast number of formats used for phone numbers worldwide, "
"responses to this step type are **not** validated for formatting, and can "
"include both numbers and special characters."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:128
msgid "Forward to Operator"
msgstr "التحويل إلى الموظف "

#: ../../content/applications/websites/livechat/chatbots.rst:130
msgid ""
"This step forwards the conversation to an active live chat operator, so they"
" can continue assisting the visitor. As the conversation transcript is "
"passed on to the operator, they can pick up where the chatbot left off. This"
" not only saves time for all parties involved, it can also help qualify "
"conversations before they reach live operators."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:136
msgid ""
"If no active operator is available on the channel, the chatbot continues the"
" conversation with the visitor. Therefore, additional steps should be added "
"after this one to ensure there is no abrupt end to the conversation. The "
"additional steps can both inform visitors about the lack of available "
"operators (e.g. `Uh-oh, it looks like none of our operators are available`) "
"and continue the conversation (e.g. `Would you like to leave your email "
"address?`)."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:0
msgid ""
"View of a chatbot follow up messages when no live chat operator is "
"available."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:147
msgid "Free Input/Multi-Line"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:149
msgid ""
"The *free input* step allows visitors to respond to questions without "
"providing pre-written responses. Information provided in these responses is "
"stored in the chat transcripts."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:152
msgid ""
"Choose between :guilabel:`Free Input` and :guilabel:`Free Input (Multi-"
"Line)` depending on the type and amount of information the visitor is asked "
"to provide."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:156
msgid "Create Lead"
msgstr "إنشاء عميل مهتم "

#: ../../content/applications/websites/livechat/chatbots.rst:158
msgid ""
"This step creates a lead in the *CRM* application. Select an option from the"
" :guilabel:`Sales Team` drop-down field that appears to assign the created "
"lead to a specific team."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:162
msgid ""
"This step is **only** available if the *CRM* application is installed on the"
" database."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:165
msgid "Create Ticket"
msgstr "إنشاء تذكرة "

#: ../../content/applications/websites/livechat/chatbots.rst:167
msgid ""
"This step creates a :doc:`ticket "
"<../../services/helpdesk/overview/receiving_tickets>` in the *Helpdesk* "
"application. Select an option from the :guilabel:`Helpdesk Team` drop-down "
"field that appears to assign the created ticket to a specific team."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:172
msgid ""
"This step is **only** available if the *Helpdesk* application is installed "
"on the database."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:175
msgid "Only if"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:177
msgid ""
"Chatbot scripts operate on an if/then basis, which means the next question "
"presented to the visitor is determined by the answer provided to the "
"previous question."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:180
msgid ""
"To continue the progression of the conversation, the :guilabel:`Create "
"Script Steps` form for a new step contains a field labeled :guilabel:`Only "
"If`. This field is where the progression of questions is defined."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:184
msgid ""
"If a step is intended to follow all of the previous messages, this field can"
" be left empty. However, if a message should **only** be sent conditionally,"
" based on a previous response, or several previous responses, those "
"responses **must** be added to this field."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:189
msgid ""
"If any selections are made in the :guilabel:`Only If` field, they must "
"**all** be selected during the conversation *before* this step will be "
"included. Only include selections in this field if they are necessary for "
"this step to be displayed."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:194
msgid ""
"In the *Welcome Bot* script, a visitor can ask about pricing information. If"
" the visitor selects this response, a step is included to forward the "
"conversation to an operator. The chatbot first sends a message informing the"
" visitor that it is checking to see if an operator is available to chat."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:199
msgid ""
"However, this message should **only** be delivered if the visitor requests "
"pricing information. In that situation, the conversation would proceed as "
"below:"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:202
msgid "Welcome Bot: \"*What are you looking for?*\""
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:203
msgid "Visitor: \"**I have a pricing question.**\""
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:204
msgid ""
"Welcome Bot: \"*Hmmm, let me check if I can find someone that could help you"
" with that...*\""
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:206
msgid ""
"In the details form for the :guilabel:`Text` step, the *I have a pricing "
"question* response has been selected in the :guilabel:`Only If` field. As "
"such, this step is **only** shown in conversations where that response has "
"been selected."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:0
msgid "View of the new message form emphasizing the Only If field."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:215
msgid "Script testing"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:217
msgid ""
"To ensure all visitors have a satisfactory experience with the chatbot, each"
" message needs to lead to a natural conclusion. Chatbot scripts should be "
"tested to confirm there are no dead-ends, and to understand what the visitor"
" sees when they interact with the chatbot."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:222
msgid ""
"If the visitor provides an answer, or input, that is **not** assigned a "
"corresponding follow-up response, the conversation stops (*dead-ends*). "
"Since the visitor cannot re-engage the chatbot, they must restart the "
"conversation by refreshing the chat window, or their browser."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:226
msgid ""
"To test the performance of a chatbot, first click on the :guilabel:`Test` "
"button at the top-left of the chatbot script page. Then, upon being "
"redirected to the testing screen, answer the chatbot prompts the same way a "
"potential site visitor would."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:230
msgid ""
"When the script has reached an end-point, the message *Conversation "
"ended...Restart* appears at the bottom of the chat window. To begin the "
"conversation at the beginning of the script, click on :guilabel:`Restart`. "
"To return to the script page, click :guilabel:`Back to edit mode` at the top"
" of the page."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:236
msgid "Add chatbot to a channel"
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:238
msgid ""
"After a chatbot has been created and tested, it needs to be added to a live "
"chat channel."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:240
msgid ""
"First, open the :menuselection:`Live Chat` application, find the Kanban card"
" for the appropriate live chat channel, hover over it, and click the "
":guilabel:`⋮ (three dots)` icon to open the drop-down menu. Click "
":guilabel:`Configure Channel` to open the channel detail form."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:245
msgid ""
"To create a new live chat channel, open the :menuselection:`Live Chat app` "
"and click :guilabel:`New`. See :doc:`Live Chat <../livechat>` for more "
"information."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:248
msgid ""
"Click on the :guilabel:`Channel Rules` tab. Then, open an existing rule, or "
"create a new one by clicking :guilabel:`Add a line`."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:251
msgid ""
"On the :guilabel:`Create Rules` modal, choose the appropriate chatbot in the"
" :guilabel:`Chatbot` field."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:254
msgid ""
"If the chatbot should **only** be active if there are no live chat operators"
" available, check the box labeled :guilabel:`Enabled only if no operator`."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst-1
msgid "View of the channel rules emphasizing the chatbot field."
msgstr ""

#: ../../content/applications/websites/livechat/chatbots.rst:262
msgid ":doc:`Live chat channel rules </applications/websites/livechat>`"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:3
msgid "Participate in live chat"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:5
msgid ""
"*Operators* are the users who respond to live chat requests from customers "
"and website visitors. The information below outlines the necessary steps for"
" operators participating in live chat conversations on an Odoo database."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:10
msgid "Set an online chat name"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:12
msgid ""
"Before participating in a live chat, operators may choose to update their "
"*Online Chat Name*. This is the name displayed to site visitors in the live "
"chat conversation."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:15
msgid ""
"To update the *Online Chat Name*, click on the user avatar in the top-right "
"corner of any page in the database. Select :guilabel:`My Profile` from the "
"drop-down menu to open the profile page. On the right side of the "
":guilabel:`Preferences` tab, locate the :guilabel:`Online Chat Name` field, "
"and enter a preferred name."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:20
msgid ""
"If a user's :guilabel:`Online Chat Name` is not set, the name displayed "
"defaults to the user's name that is designated on their profile page."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:24
msgid ""
"Mitchell Admin has his full name as his :guilabel:`User Name`, but he does "
"not want to include his last name in a live chat conversation for privacy "
"reasons. He would then set his :guilabel:`Online Chat Name` to include only "
"his first name, Mitchell."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:0
msgid "View of user profile in Odoo, emphasizing the Online Chat name field."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:32
msgid "Set online chat languages"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:34
msgid ""
"If an operator speaks multiple languages, they can add this information to "
"their user profile. This information can then be used to determine which "
"conversations the operator is assigned. A visitor's language is determined "
"via their browser's language settings."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:39
msgid ""
"Conversations are assigned to operators based on a number of criteria, "
"including availability and the number of ongoing conversations. While the "
"operator's main language and additional languages are taken into "
"consideration, they do **not** supersede all other criteria."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:43
msgid ""
"To add *Online Chat Languages*, click on the user avatar in the top-right "
"corner of any page in the database. Select :guilabel:`My Profile` from the "
"drop-down menu to open the profile page. On the right side of the "
":guilabel:`Preferences` tab, click into the :guilabel:`Online Chat Language`"
" field, and select one or more languages from the drop-down menu."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:49
msgid ""
"Only :doc:`languages <../../general/users/language>` that are enabled on the"
" database can be selected in the :guilabel:`Online Chat Language` field."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:53
msgid "Join or leave a channel"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:55
msgid ""
"To join a live chat channel, go to the :menuselection:`Live Chat app --> "
"Channels`, and click the :guilabel:`Join` button on the Kanban card for the "
"appropriate channel."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:58
msgid ""
"Any channel where the user is currently active shows a :guilabel:`Leave` "
"button. Click this button to disconnect from the channel."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst-1
msgid ""
"View of a channel form and the option to join a channel for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:65
msgid ""
"Operators that do not show any activity in Odoo for more than thirty minutes"
" are considered disconnected, and subsequently removed from the channel."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:69
msgid "Manage live chat requests"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:71
msgid ""
"When an operator is active in a channel, chat windows open in the bottom-"
"right corner of the screen, no matter what page they are on in Odoo. This "
"allows them to access other pages and apps, while still participating in the"
" conversation."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:75
msgid ""
"Live chat conversations can also be viewed by navigating to the "
":menuselection:`Discuss app`. New conversations appear in bold under the "
"channel name, located along the left panel."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst-1
msgid ""
"View of the discuss application with a message sent through live chat in "
"Odoo."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:81
msgid ""
"Click on a conversation in the left panel to open it. From this view, an "
"operator can participate in the chat the same as they would in the normal "
"chat window."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:85
msgid ""
"Conversations can also be accessed by clicking the :icon:`fa-comments` "
":guilabel:`(messages)` icon in the menu bar."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:0
msgid "View of the menu bar in Odoo emphasizing the comments icon."
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:92
msgid ":doc:`../../productivity/discuss`"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:93
msgid ":doc:`../livechat`"
msgstr ""

#: ../../content/applications/websites/livechat/participate.rst:94
msgid ":doc:`../livechat/responses`"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:3
msgid "Ratings"
msgstr "التقييمات "

#: ../../content/applications/websites/livechat/ratings.rst:9
msgid ""
"At the end of a **Live Chat** conversation, customers have the opportunity "
"to rate the quality of support they received from the live chat *operator*. "
"Customers provide ratings as soon as they close the conversation. This "
"allows operators to receive immediate feedback on their performance. It also"
" allows customers to share any final comments before leaving the chat "
"window."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:15
msgid "Rate live chat conversations"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:17
msgid ""
"Customers end a live chat conversation by clicking the :icon:`oi-close` "
":guilabel:`(close)` icon in the upper right-hand corner of the chat window. "
"They are then prompted to select an icon that reflects their level of "
"satisfaction. The icons represent the following ratings:"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:21
msgid "**Satisfied** - |smile|"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:22
msgid "**Okay** - |meh|"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:23
msgid "**Dissatisfied** - |frown|"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid "View of the chat window from a user's side for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:29
msgid ""
"When customers end a conversation, a field marked :guilabel:`Receive a copy "
"of this conversation` appears under the *ratings* icons. Customers can enter"
" their email either before or after they submit a rating."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:33
msgid ""
"If the customer selects the |smile|, they are presented with a thank you "
"message and a :guilabel:`Close Conversation` link."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid "View of customer's live chat window with thank you message."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:39
msgid ""
"If the customer selects either the |meh| or the |frown|, a text box appears."
" Customers can add comments in this text box to explain why they chose this "
"rating. This message, along with the rating icon, is sent to the live chat "
"operator."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid ""
"View of a chat window from an operator's window highlighting a rating for "
"Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:47
msgid "Publish customer ratings"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:49
msgid ""
"To publish a channel's ratings on the website, first navigate to a live chat"
" channel's record by going to the :menuselection:`Live Chat` app and "
"clicking on the :icon:`fa-ellipsis-v` :guilabel:`(vertical ellipsis)` icon "
"on the Kanban card for that team. Then, click :guilabel:`Configure Channel` "
"to open the channel details form. Then click on the :guilabel:`Go to "
"Website` smart button. This opens the :guilabel:`Live Chat Channel "
"Statistics` page."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:55
msgid ""
"In the upper right corner of the page, click the red :guilabel:`Unpublished`"
" slider. The slider changes from :guilabel:`Unpublished` to "
":guilabel:`Published`."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid "View of the published ratings on the portal for Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:62
msgid ""
"The customer notes submitted with the rating are *not* published on the "
"website; they are kept internal. Only a statistical overview of the "
"operators' performance for the *channel* appears on the website."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:67
msgid "Add ratings page to site"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:69
msgid ""
"Once the rating page has been published, it has to be manually added to the "
"website. To do this, go to the main Odoo dashboard and open the **Website** "
"application. :menuselection:`Website app --> Site --> Pages`, then click "
":guilabel:`New`."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:73
msgid ""
"This opens a :guilabel:`New Page` pop-up window. In the :guilabel:`Page "
"Title` field, enter `livechat`. This acts as the URL for the published "
"webpage."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:77
msgid ""
"The URL *must* be named `livechat` in order for the database to recognize "
"and connect the ratings page. After the page has been published, the page "
"title can be changed later under the :guilabel:`Menu Editor`."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:81
msgid ""
"Click :guilabel:`Create`, and the newly created webpage opens. The "
":guilabel:`Web Editor` appears in the right panel."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:84
msgid ""
"The page lists the names of the :guilabel:`Live Chat Channels` whose ratings"
" pages have been published. On the left side of the channel name is an icon,"
" which users can click on to go to the ratings page for the respective "
"channel."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid ""
"View of the webpage for Live Chat ratings emphasizing the channel icon."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:92
msgid ""
"The icon displayed on this page is configured on the live chat channel's "
"configuration page. To update this image, navigate to the "
":menuselection:`Live Chat` app and click on the :icon:`fa-ellipsis-v` "
":guilabel:`(vertical ellipsis)` icon on the Kanban card for that team. Then,"
" click :guilabel:`Configure Channel` to open the channel details form. Click"
" the :icon:`fa-pencil` :guilabel:`(pencil)` icon in the image box to upload "
"an image."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:98
msgid ""
"Make any desired changes or additions to this page, then click "
":guilabel:`Save` in the top right of the webpage editor. The website editor "
"side panel closes, and the webpage remains on the screen."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:101
msgid ""
"To publish the `livechat` webpage, return to the list of webpages by "
"navigating to :menuselection:`Site --> Content --> Pages`. Click the "
"checkbox to the left of `livechat` in the list of pages to select the page "
"and highlight the line. Then, click the checkbox under the column labeled "
":guilabel:`Is Published`. The field with the checkbox is highlighted in "
"white. Click the checkbox a second time to activate the :guilabel:`Is "
"Published` box. The webpage is now published."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid ""
"View of the list of pages for a website with the 'is published' box "
"emphasized."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:110
msgid ""
"Once the page has been added to the site, ratings are set to be published by"
" default. However, individual ratings can be manually selected to be hidden "
"from the public. The rating is still included in internal reports, and can "
"still be viewed by internal teams. However, public website visitors and "
"portal users do not have access."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:115
msgid ""
"See :ref:`Hide individual ratings <livechat/overview/hide-ratings>` for more"
" information."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:118
msgid "Customer ratings report"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:120
msgid ""
"The :guilabel:`Customer Ratings` report (:menuselection:`Live Chat --> "
"Report --> Customer Ratings`) displays an overview of the ratings received "
"on live chat conversations, as well as any additional comments submitted "
"with the rating."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid "View of the customer ratings report in Odoo Live Chat."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:127
msgid ""
"The report defaults to a Kanban view, with each rating represented by a "
"different card. To switch to a different view, click on one of the icons in "
"the upper-right corner of the screen. The report is available in *list* "
"view, *pivot* view, and *graph* view."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:131
msgid ""
"Click on an individual rating to see additional details about the "
"conversation, and the rating."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:136
msgid "Hide individual ratings"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:138
msgid ""
"Ratings are set to be published by default. However, individual ratings can "
"be manually selected to be hidden from the public. The rating is still "
"included in internal reports, and can still be viewed by internal teams. "
"However, public website visitors and portal users do not have access."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:142
msgid ""
"To hide a rating, go to :menuselection:`Live Chat app --> Report --> "
"Customer Ratings`. Click on the Kanban card for the rating to be hidden. On "
"the individual rating's detail page, check the box labeled "
":guilabel:`Visible Internally Only`."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst-1
msgid ""
"View of an individual rating's detail page with the visible internally "
"setting checked."
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:150
msgid ":doc:`/applications/websites/livechat`"
msgstr ":doc:`/applications/websites/livechat`"

#: ../../content/applications/websites/livechat/ratings.rst:151
msgid ":doc:`responses`"
msgstr ""

#: ../../content/applications/websites/livechat/ratings.rst:152
msgid ":doc:`/applications/websites/website`"
msgstr ":doc:`/applications/websites/website`"

#: ../../content/applications/websites/livechat/reports.rst:3
msgid "Reports"
msgstr "التقارير"

#: ../../content/applications/websites/livechat/reports.rst:5
msgid ""
"Odoo *Live Chat* includes several reports that allow for the monitoring of "
"operator performance and the identification of trends in customer "
"conversations."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:9
msgid "Available reports"
msgstr "التقارير المتاحة "

#: ../../content/applications/websites/livechat/reports.rst:11
msgid "The following reports are included in the *Live Chat* app:"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:13
msgid ":ref:`Sessions History <livechat/sessions-history>`"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:14
msgid ":ref:`Session Statistics <livechat/session-statistics>`"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:15
msgid ":ref:`Operator Analysis <livechat/operator-analysis>`"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:18
msgid ""
"The *Live Chat Ratings Report* can also be accessed through the "
":guilabel:`Report` menu. For more information on this report, and on the "
"*Live Chat* rating process, see :doc:`Live Chat Ratings "
"<../livechat/ratings>`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:22
msgid ""
"To access a drop-down menu of all the available reports, navigate to "
":menuselection:`Live Chat app --> Report`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:28
msgid "Sessions History"
msgstr "سجل الجلسات "

#: ../../content/applications/websites/livechat/reports.rst:30
msgid ""
"The *Sessions History* report displays an overview of live chat sessions, "
"including session dates, participant name and country, session duration, the"
" number of messages, and the rating. It also provides access to the complete"
" transcripts of live chat sessions."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:34
msgid ""
"To access this report, navigate to :menuselection:`Live Chat app --> Report "
"--> Sessions History`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst-1
msgid "Example of the Sessions History report from the Live Chat application."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:40
msgid ""
"The information in this report can be exported, or inserted into a "
"spreadsheet."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:42
msgid ""
"Click the :guilabel:`⚙️ (gear)` icon to the right of the :guilabel:`History`"
" page title, in the top-left corner. Doing so reveals a drop-down menu."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:45
msgid ""
"From the drop-down menu, click :guilabel:`Export All` to export all sessions"
" to a spreadsheet, or :guilabel:`Insert list in spreadsheet` to insert the "
"information in a new, or existing, spreadsheet."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:48
msgid ""
"To only export select sessions, first select the desired sessions to be "
"exported from the list, by clicking the checkbox to the left of each "
"individual session. With the sessions selected, click the :guilabel:`⚙️ "
"(gear) Actions` icon in the top-center of the page, and click "
":guilabel:`Export` or :guilabel:`Insert list in spreadsheet`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:53
msgid ""
"To view the transcript of an individual conversation, click anywhere on the "
"entry line. This opens the *Discuss* thread for the conversation."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:56
msgid ""
"In the *Discuss* thread, the conversation view displays the entire "
"transcript of the conversation. At the top of the conversation, there is a "
"list of the web pages the visitor browsed before beginning their chat "
"session, along with corresponding time stamps. If the visitor left a rating,"
" it is included at the end of the transcript."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst-1
msgid "View of the chat transcript in the Discuss application."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:68
msgid "Session Statistics"
msgstr "إحصائيات الجلسة"

#: ../../content/applications/websites/livechat/reports.rst:70
msgid ""
"The *Session Statistics* report provides a statistical overview of live chat"
" sessions. The default view for this report displays sessions grouped by the"
" date of creation."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:73
msgid ""
"To access this report, navigate to :menuselection:`Live Chat app --> Reports"
" --> Session Statistics`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:80
msgid ""
"Example of the Sessions Statistics report from the Live Chat application."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:80
msgid ""
"The stacked bar graph view of the *Session Statistics* report, with results "
"grouped by Creation Date (hour), then by rating."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:83
#: ../../content/applications/websites/livechat/reports.rst:115
msgid ""
"To view a different measure, click the :guilabel:`Measures` drop-down menu "
"at the top-left of the report. The measures available for this report "
"include:"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:86
msgid ":guilabel:`# of speakers`: number of participants in the conversation."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:87
msgid ""
":guilabel:`Days of activity`: number of days since the operator's first "
"session."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:88
msgid ""
":guilabel:`Duration of Session (min)`: the duration of a conversation, in "
"minutes."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:89
msgid ""
":guilabel:`Is visitor anonymous`: denotes whether the conversation "
"participant is anonymous."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:90
msgid ""
":guilabel:`Messages per session`: the total number of messages sent in a "
"conversation. This measure is included in the default view."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:92
msgid ""
":guilabel:`Rating`: the rating received by an operator at the end of a "
"session, if one was provided."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:94
msgid ""
":guilabel:`Session not rated`: denotes if a session did **not** receive a "
"rating at the end of the conversation."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:96
msgid ""
":guilabel:`Time to answer (sec)`: the average time, in seconds, before an "
"operator responds to a chat request."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:98
msgid ""
":guilabel:`Visitor is Happy`: denotes whether a positive rating was "
"provided. If the visitor gave either a negative or neutral rating, they are "
"considered *unhappy*."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:100
#: ../../content/applications/websites/livechat/reports.rst:124
msgid ":guilabel:`Count`: the total number of sessions."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:105
msgid "Operator Analysis"
msgstr "تحليل موظف الدعم"

#: ../../content/applications/websites/livechat/reports.rst:107
msgid ""
"The *Operator Analysis* report is used to monitor the performance of "
"individual live chat operators."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:109
msgid ""
"To access the report, navigate to :menuselection:`Live Chat app --> Reports "
"--> Operator Analysis`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:111
msgid ""
"The default view for this report is a bar chart, which only displays "
"conversations from the current month, as indicated by the :guilabel:`This "
"Month` default search filter. Conversations are grouped by operator."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:118
msgid ""
":guilabel:`# of Sessions`: the number of sessions an operator participated "
"in. This measure is included by default."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:120
msgid ""
":guilabel:`Average duration`: the average duration of a conversation, in "
"seconds."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:121
msgid ""
":guilabel:`Average rating`: the average rating received by the operator."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:122
msgid ""
":guilabel:`Time to answer`: the average amount of time before the operator "
"responds to a chat request, in seconds."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst-1
msgid ""
"Example of the Operator Analysis report from the Live Chat application."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:131
msgid "View and filter options"
msgstr "عرض وتصفية الخيارات "

#: ../../content/applications/websites/livechat/reports.rst:133
msgid ""
"On any Odoo report, the view and filter options vary, depending on what data"
" is being analyzed, measured, and grouped. See below for additional "
"information on the available views for the *Live Chat* reports."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:138
msgid ""
"The :ref:`Sessions History <livechat/sessions-history>` report is **only** "
"available in list view."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:142
msgid "Pivot view"
msgstr "نافذة عرض Pivot "

#: ../../content/applications/websites/livechat/reports.rst:144
msgid ""
"The *pivot* view presents data in an interactive manner. The :ref:`Session "
"Statistics <livechat/session-statistics>` and :ref:`Operator Analysis "
"<livechat/operator-analysis>` reports are available in pivot view."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:148
msgid ""
"The pivot view can be accessed on a report by selecting the :guilabel:`grid "
"icon` at the top-right of the screen."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:151
msgid ""
"To add a group to a row or column, click the :guilabel:`➕ (plus sign)` icon "
"next to :guilabel:`Total`, and then select one of the groups from the drop-"
"down menu that appears. To remove one, click the :guilabel:`➖ (minus sign)` "
"icon, and de-select the appropriate option."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:156
msgid "Graph view"
msgstr "أداة عرض الرسم البياني "

#: ../../content/applications/websites/livechat/reports.rst:158
msgid ""
"The *graph* view presents data in either a *bar*, *line*, or *pie* chart. "
"The :ref:`Session Statistics <livechat/session-statistics>` and "
":ref:`Operator Analysis <livechat/operator-analysis>` reports are available "
"in graph view."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:162
msgid ""
"Switch to the graph view by selecting the :guilabel:`line chart` icon at the"
" top-right of the screen. To switch between the different charts, select the"
" desired view's corresponding icon at the top-left of the chart, while in "
"graph view."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:167
msgid ""
"Both the bar chart and line chart can utilize the *stacked* view option. "
"This presents two or more groups of data on top of each other, instead of "
"next to each other, making it easier to compare data."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:172
msgid "Save and share a favorite search"
msgstr "حفظ ومشاركة البحث المفضل "

#: ../../content/applications/websites/livechat/reports.rst:174
msgid ""
"The *Favorites* feature found on reports allows users to save their most "
"commonly used filters, without having to reconstruct them every time they "
"are needed."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:177
msgid ""
"To create and save a filter to the *Favorites* section on the search bar "
"drop-down menu, follow the steps below:"
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:180
msgid ""
"Set the necessary parameters using the :guilabel:`Filters` and "
":guilabel:`Group By` options available in the :guilabel:`Search...` bar "
"drop-down menu and the :guilabel:`Measures` drop-down menu at the top-left "
"of the report."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:183
msgid ""
"Click the :guilabel:`🔻(triangle pointed down)` icon next to the "
":guilabel:`Search...` bar to open the drop-down menu."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:185
msgid ""
"Under the :guilabel:`Favorites` heading, click :guilabel:`Save current "
"search`."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:186
msgid "Rename the search."
msgstr "إعادة تسمية البحث. "

#: ../../content/applications/websites/livechat/reports.rst:187
msgid ""
"Select :guilabel:`Default filter` to have these filter settings "
"automatically displayed when the report is opened. Otherwise, leave it "
"blank."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:189
msgid ""
"Select :guilabel:`Shared` to make this filter available to all other "
"database users. If this box is not checked, the filter is only available to "
"the user who creates it."
msgstr ""

#: ../../content/applications/websites/livechat/reports.rst:191
msgid "Click :guilabel:`Save` to preserve the configuration for future use."
msgstr "اضغط على :guilabel:`حفظ` لحفظ التهيئة لاستخدامها في المستقبل. "

#: ../../content/applications/websites/livechat/responses.rst:3
msgid "Commands and canned responses"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:5
msgid ""
"In the Odoo **Live Chat** application, *commands* allow the user to perform "
"specific actions both inside the chat window, and through other Odoo "
"applications. The **Live Chat** app also includes *canned responses*. These "
"are customized, preconfigured substitutions that allow users to replace "
"shortcut entries in place of longer, well-thought out responses to some of "
"the most common questions and comments."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:11
msgid ""
"Both commands and canned responses save time, and allow users to maintain a "
"level of consistency throughout their conversations."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:15
msgid "Execute a command"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:17
msgid ""
"Live chat *commands* are keywords that trigger preconfigured actions. When a"
" live chat *operator* is participating in a conversation with a customer or "
"website visitor, they can execute a command by typing `/`, followed by the "
"command."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:21
msgid ""
"Commands, and the resulting actions, are only visible in the conversation "
"window for the live chat operator. A customer does not see any commands that"
" an operator uses in a conversation from their view of the chat."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:26
msgid ""
"During a conversation with a customer, a live chat operator executes the "
"command to :ref:`create a ticket <live-chat/ticket>`. After entering the "
"command, `/ticket`, the system automatically creates a ticket with the "
"information from the conversation. It also includes a link to the new "
"ticket, so the operator can go there directly to add any additional "
"information, if necessary."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:34
msgid "More information about each available command can be found below."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:37
msgid "Help"
msgstr "المساعدة"

#: ../../content/applications/websites/livechat/responses.rst:39
msgid ""
"If an operator types `/help` in the chat window, an informative message that"
" includes the potential entry types an operator can make is displayed."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:42
msgid ""
"Type `@username` to mention a user in the conversation. A notification will "
"be sent to that user's inbox or email, depending on their notification "
"settings."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:44
msgid "Type `/command` to execute a command."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:45
msgid ""
"Type `:shortcut` to insert a :ref:`canned response <live-chat/canned-"
"responses>`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:48
msgid ":doc:`/applications/productivity/discuss`"
msgstr ":doc:`/applications/productivity/discuss`"

#: ../../content/applications/websites/livechat/responses.rst:49
msgid ":doc:`/applications/productivity/discuss/team_communication`"
msgstr ":doc:`/applications/productivity/discuss/team_communication`"

#: ../../content/applications/websites/livechat/responses.rst:52
msgid "Ticket & search tickets"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:54
msgid ""
"The `/ticket` and `/search_tickets` commands allow operators to create "
"helpdesk tickets directly from a conversation, and search through existing "
"tickets by keyword or ticket number."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:58
msgid ""
"The `/ticket` and `/search_tickets` commands can **only** be used if the "
"**Helpdesk** app has been installed, and *Live Chat* has been activated on a"
" *Helpdesk* team. To activate *Live Chat*, go to :menuselection:`Helpdesk "
"app --> Configuration --> Helpdesk Teams`, and select a team. Scroll to the "
":guilabel:`Channels` section, and check the box labeled, :guilabel:`Live "
"Chat`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:66
msgid "Create a ticket from a live chat"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:68
msgid ""
"If an operator types `/ticket` in the chat window, the conversation is used "
"to create a *Helpdesk* ticket."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:71
msgid ""
"After entering the `/ticket` command, type a title for the ticket into the "
"chat window, then press `Enter`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst-1
#: ../../content/applications/websites/livechat/responses.rst-1
msgid ""
"View of the results from a helpdesk search in a Live Chat conversation."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:77
msgid ""
"The newly created ticket will be added to the *Helpdesk* team that has live "
"chat enabled. If more than one team has live chat enabled, the ticket will "
"automatically be assigned based on the team's priority."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:81
msgid ""
"The transcript from the conversation will be added to the new ticket, under "
"the :guilabel:`Description` tab."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:84
msgid ""
"To access the new ticket, click on the link in the chat window, or go to the"
" :menuselection:`Helpdesk app` and click the :guilabel:`Tickets` button on "
"the Kanban card for the appropriate team."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:89
msgid "Search for a ticket from a live chat"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:91
msgid ""
"If an operator types `/search_tickets` in the chat window, they can search "
"through *Helpdesk* tickets, either by ticket number or keyword."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:94
msgid ""
"After entering the `/search_tickets` command, type a keyword or ticket "
"number, then press :kbd:`Enter`. If one or more related tickets are found, a"
" list of links is generated in the conversation window."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:102
msgid ""
"Results from the search command will only be seen by the operator, not the "
"customer."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:105
msgid "History"
msgstr "السجل"

#: ../../content/applications/websites/livechat/responses.rst:107
msgid ""
"If an operator types `/history` in the chat window, it generates a list of "
"the most recent pages the visitor has viewed on the website (up to 15)."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst-1
msgid ""
"View of the results from a /history command in a Live Chat conversation."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:114
msgid "Lead"
msgstr "عميل مهتم "

#: ../../content/applications/websites/livechat/responses.rst:116
msgid ""
"By typing `/lead` in the chat window, an operator can create a *lead* in the"
" **CRM** application."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst-1
msgid "View of the results from a /lead command in a Live Chat conversation."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:122
msgid ""
"The `/lead` command can only be used if the **CRM** app has been installed."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:124
msgid ""
"After typing `/lead`, create a title for this new lead, then press `Enter`. "
"A link with the lead title appears. Click the link, or navigate to the "
":menuselection:`CRM` app to view the :guilabel:`Pipeline`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:129
msgid ""
"The link to the new lead can only be seen and accessed by the operator, not "
"the customer."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:131
msgid ""
"The transcript of that specific live chat conversation (where the lead was "
"created) is added to the :guilabel:`Internal Notes` tab of the lead form."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:134
msgid ""
"On the :guilabel:`Extra Information` tab of the lead form, the "
":guilabel:`Source` will be listed as :guilabel:`Livechat`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:138
msgid "Leave"
msgstr "مغادرة"

#: ../../content/applications/websites/livechat/responses.rst:140
msgid ""
"If an operator types `/leave` in the chat window, they can automatically "
"exit the conversation. This command does not cause the customer to be "
"removed from the conversation, nor does it automatically end the "
"conversation."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:145
msgid ":doc:`/applications/sales/crm/acquire_leads`"
msgstr ":doc:`/applications/sales/crm/acquire_leads`"

#: ../../content/applications/websites/livechat/responses.rst:146
msgid ":doc:`../../services/helpdesk`"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:151
msgid "Canned responses"
msgstr "الردود الجاهزة "

#: ../../content/applications/websites/livechat/responses.rst:153
msgid ""
"*Canned responses* are customizable inputs where a *shortcut* stands in for "
"a longer response. An operator will enter the shortcut, and it is "
"automatically replaced by the expanded *substitution* response in the "
"conversation."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:158
msgid "Create canned responses"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:160
msgid ""
"To create a new canned response, go to :menuselection:`Live Chat app --> "
"Configuration --> Canned Responses --> New`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:163
msgid ""
"Type a shortcut command in the :guilabel:`Shortcut` field. Next, click the "
":guilabel:`Substitution` field, and type the message that should replace the"
" shortcut."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:167
msgid ""
"Try to connect the shortcut to the topic of the substitution. The easier it "
"is for the operators to remember, the easier it is to use the canned "
"responses in conversations."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:171
msgid "Authorized groups"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:173
msgid ""
"When a new canned response is created, it can **only** be utilized by the "
"operator that created it. To allow the response to be used by other "
"operators, select one or more :ref:`groups <access-rights/groups>` from the "
":guilabel:`Authorized Groups` drop-down list."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:178
msgid "Use canned responses in a live chat conversation"
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:180
msgid ""
"To use a canned response in a conversation, click the :icon:`fa-plus-circle`"
" :guilabel:`(plus)` icon in the message window. Then, click "
":guilabel:`Insert a Canned Response`. This opens a list of available canned "
"responses. Either select a response from the list, or type the appropriate "
"shortcut, then click the :icon:`fa-paper-plane` :guilabel:`(send)` icon or "
"hit :kbd:`Enter`."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:186
msgid ""
"Typing `::` into a chat window on its own generates a list of available "
"canned responses. Responses can be manually selected from the list, in "
"addition to the use of shortcuts."
msgstr ""

#: ../../content/applications/websites/livechat/responses.rst:0
msgid "View of a chat window and the list of available canned responses."
msgstr ""

#: ../../content/applications/websites/website.rst:7
msgid "Website"
msgstr "الموقع الإلكتروني"

#: ../../content/applications/websites/website.rst:9
msgid ""
"**Odoo Website** offers a user-friendly platform for creating and managing "
"your website. It includes various tools and features to help you design, "
"publish, and maintain web pages without needing advanced technical skills. "
"You can easily customize layouts, add multimedia content, and integrate with"
" other Odoo apps to expand your website's functionality."
msgstr ""

#: ../../content/applications/websites/website.rst:54
msgid ":doc:`../general/integrations/unsplash`"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:3
msgid "Address autocomplete"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:5
msgid ""
"You can use the Google Places API on your website to ensure that your users'"
" delivery addresses exist and are understood by the carrier. The Google "
"Places API allows developers to access detailed information about places "
"using HTTP requests. The autocompletion predicts a list of places when the "
"user starts typing the address."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst-1
msgid "Address autocomplete example"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:14
msgid ""
"`Google Maps Platform <https://mapsplatform.google.com/maps-products>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:15
msgid ""
"`Google Developers Documentation: Google Places API "
"<https://developers.google.com/maps/documentation/places/web-"
"service/autocomplete>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:18
msgid ""
"To do so, go to :menuselection:`Website --> Configuration --> Settings` and "
"enable :guilabel:`Address Autocomplete` in the :guilabel:`SEO` section."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst-1
msgid "Enable address autocomplete"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:24
msgid ""
"Insert your :guilabel:`Google Places API key` in the :guilabel:`API Key` "
"field. If you don't have one, create yours on the `Google Cloud Console "
"<https://console.cloud.google.com/getting-started>`_ and follow these steps."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:31
msgid "Step 1: Enable the Google Places API"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:33
msgid ""
"**Create a New Project:** To enable the **Google Places API**, you first "
"need to create a project. To do so, click :guilabel:`Select a project` in "
"the top left corner, :guilabel:`New Project`, and follow the prompts to set "
"up your project."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:38
msgid ""
"**Enable the Google Places API:** Go to the :guilabel:`Enabled APIs & "
"Services` and click :guilabel:`+ ENABLE APIS AND SERVICES.` Search for "
":guilabel:`\"Places API\"` and select it. Click on the "
":guilabel:`\"Enable\"` button."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:43
msgid ""
"Google's pricing depends on the number of requests and their complexity."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:46
msgid "Step 2: Create API Credentials"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:48
msgid ""
"Go to `APIs & Services --> Credentials "
"<https://console.cloud.google.com/apis/credentials>`_."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:50
msgid ""
"**Create credentials:** To create your credentials, go to "
":guilabel:`Credentials`, click :guilabel:`Create Credentials`, and select "
":guilabel:`API key`."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:54
msgid "Restrict the API Key (Optional)"
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:56
msgid ""
"For security purposes, you can restrict the usage of your API key. You can "
"go to the :guilabel:`API restrictions` section to specify which APIs your "
"key can access. For the Google Places API, you can restrict it to only allow"
" requests from specific websites or apps."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:61
msgid "Save Your API Key: copy your API key and securely store it."
msgstr ""

#: ../../content/applications/websites/website/configuration/address_autocomplete.rst:62
msgid "Do not share it publicly or expose it in client-side code."
msgstr ""

#: ../../content/applications/websites/website/configuration/cdn.rst:3
msgid "Set up a content delivery network (CDN)"
msgstr "قم بإعداد شبكة إيصال المحتوى (CDN) "

#: ../../content/applications/websites/website/configuration/cdn.rst:8
msgid "Deploying with KeyCDN"
msgstr "التنفيذ باستخدام KeyCDN "

#: ../../content/applications/websites/website/configuration/cdn.rst:10
msgid ""
"A :abbr:`CDN (Content Delivery Network)` or *content distribution network*, "
"is a geographically distributed network of servers that provides high speed "
"internet content. The :abbr:`CDN (Content Delivery Network)` provides quick,"
" high-quality content delivery for content-heavy websites."
msgstr ""
":abbr:`CDN (شبكة إيصال المحتوى)` أو *شبكة توزيع المحتوى*، هي شبكة موزعة "
"جغرافياً من الخوادم التي تمنحك محتوى فائق السرعة على الإنترنت. تسهل "
":abbr:`CDN (شبكة إيصال المحتوى)` من عملية توصيل المحتوى عالي الجودة بسرعة، "
"للمواقع الإلكترونية الغنية بالمحتوى. "

#: ../../content/applications/websites/website/configuration/cdn.rst:14
msgid ""
"This document will guide you through the setup of a KeyCDN_ account with an "
"Odoo powered website."
msgstr ""
"سيرشدك هذا المستند خلال مراحل إعداد حساب KeyCDN_ مع موقع إلكتروني مشغل "
"بواسطة أودو. "

#: ../../content/applications/websites/website/configuration/cdn.rst:17
msgid "Create a pull zone in the KeyCDN dashboard"
msgstr "إنشاء منطقة سحب في لوحة بيانات KeyCDN "

#: ../../content/applications/websites/website/configuration/cdn.rst:19
msgid ""
"On the KeyCDN dashboard, start by navigating to the :menuselection:`Zones` "
"menu item on the left. On the form, give a value to the :guilabel:`Zone "
"Name`, which will appear as part of the :abbr:`CDN (Content Delivery "
"Network)`'s :abbr:`URL (Uniform Resource Locator)`. Then, set the "
":guilabel:`Zone Status` to :guilabel:`active` to engage the zone. For the "
":guilabel:`Zone Type` set the value to :guilabel:`Pull`, and then, finally, "
"under the :guilabel:`Pull Settings`, enter the :guilabel:`Origin URL`— this "
"address should be the full Odoo database :abbr:`URL (Uniform Resource "
"Locator)`."
msgstr ""
"في لوحة بيانات KeyCDN، ابدأ بالانتقال إلى عنصر قائمة "
":menuselection:`المناطق` إلى اليسار. في الاستمارة، قم بإعطاء قيمة لـ "
":guilabel:`اسم المنطقة`، والذي سيظهر كجزء من :abbr:`URL (محدد موقع الموارد "
"الموحد)` لـ :abbr:`CDN (شبكة توصيل المحتوى)`. بعد ذلك، قم بتعيين "
":guilabel:`حالة المنطقة` كـ :guilabel:`نشطة` للتفاعل مع المنطقة. بالنسبة لـ "
":guilabel:`نوع المنطقة` قم بإعداد القيمة لـ :guilabel:`سحب`، ثم أسفل "
":guilabel:`إعدادات السحب`، قم بإدخال :guilabel:`عنوان URL الأصلي`— يجب أن "
"يكون هذا العنوان هو  العنوان الكامل لقاعدة بيانات أودو :abbr:`URL (محدد موقع"
" الموارد الموحد)`. "

#: ../../content/applications/websites/website/configuration/cdn.rst:28
msgid ""
"Use ``https://yourdatabase.odoo.com`` and replace the *yourdatabase* "
"subdomain prefix with the actual name of the database. A custom :abbr:`URL "
"(Uniform Resource Locator)` can be used, as well, in place of the Odoo "
"subdomain that was provided to the database."
msgstr ""
"استخدم ``https://yourdatabase.odoo.com`` وقم باستبدال بادئة النطاق الفرعي "
"*yourdatabase* باسم قاعدة بياناتك الفعلي. يمكن استخدام :abbr:`URL (محدد موقع"
" الموارد الموحد)` المخصص كذلك،  عوضاً عن نطاق أودو الفرعي الذي تم تقديمه إلى"
" قاعدة البيانات. "

#: ../../content/applications/websites/website/configuration/cdn.rst-1
msgid "KeyCDN's Zone configuration page."
msgstr "صفحة تهيئة منطقة KeyCDN. "

#: ../../content/applications/websites/website/configuration/cdn.rst:36
msgid ""
"Under the :guilabel:`General Settings` heading below the zone form, click "
"the :guilabel:`Show all settings` button to expand the zone options. This "
"should be the last option on the page. After expanding the "
":guilabel:`General Settings` ensure that the :guilabel:`CORS` option is "
":guilabel:`enabled`."
msgstr ""
"تحت ترويسة :guilabel:`الإعدادات العامة` أسفل استمارة المنطقة، اضغط على زر "
":guilabel:`إظهار كافة الإعدادات` لتوسيع خيارات المنطقة. يجب أن يكون الخيار "
"الأخير في الصفحة. بعد التوسيع، تضمن :guilabel:`الإعدادات العامة` أن خيار "
":guilabel:`CORS` قد تم :guilabel:`تمكينه`. "

#: ../../content/applications/websites/website/configuration/cdn.rst:41
msgid ""
"Next, scroll to the bottom of the zone configuration page and "
":guilabel:`Save` the changes. KeyCDN will indicate that the new zone will be"
" deployed. This can take about 10 minutes."
msgstr ""
"تالياً، قم بالتمرير إلى أسفل صفحة تهيئة المنطقة ثم قم  :guilabel:`بحفظ` "
"التغييرات. KeyCDN سيشير إلى أن المنطقة الجديدة سيتم تجهيزها للاستخدام. "
"سيستغرق الأمر حوالي 10 دقائق. "

#: ../../content/applications/websites/website/configuration/cdn.rst-1
msgid "KeyCDN deploying the new Zone."
msgstr "KeyCDN يقوم بتجهيز لمنطقة الجديدة للعمل. "

#: ../../content/applications/websites/website/configuration/cdn.rst:49
msgid ""
"A new :guilabel:`Zone URL` has been generated for your Zone, in this example"
" it is ``pulltest-xxxxx.kxcdn.com``. This value will differ for each "
"database."
msgstr ""
"لقد تم إنشاء :guilabel:`رابط URL للمنطقة` جديد لمنطقتك. في هذا المثال، يكون "
"الرابط ``pulltest-xxxxx.kxcdn.com``. ستختلف هذه القيمة بالنسبة لكل قاعدة "
"بيانات. "

#: ../../content/applications/websites/website/configuration/cdn.rst:52
msgid ""
"Copy this :guilabel:`Zone URL` to a text editor for later, as it will be "
"used in the next steps."
msgstr ""
"قم بنسخ :guilabel:`URL المنطقة` هذا لمحرر النص، حيث سيتم استخدامه لاحقاً في "
"الخطوات التالية. "

#: ../../content/applications/websites/website/configuration/cdn.rst:55
msgid "Configure the Odoo instance with the new zone"
msgstr "قم بتهيئة مثيل أودو مع المنطقة الجديدة "

#: ../../content/applications/websites/website/configuration/cdn.rst:57
msgid ""
"In the Odoo :guilabel:`Website` app, go to the :menuselection:`Settings` and"
" then activate the :guilabel:`Content Delivery Network (CDN)` setting and "
"copy/paste the :guilabel:`Zone URL` value from the earlier step into the "
":guilabel:`CDN Base URL` field. This field is only visible and configurable "
"when the :ref:`developer mode <developer-mode>` is activated."
msgstr ""

#: ../../content/applications/websites/website/configuration/cdn.rst:63
msgid ""
"Ensure that there are two *forward slashes* (`//`) before the :guilabel:`CDN"
" Base URL` and one forward slash (`/`) after the :guilabel:`CDN Base URL`."
msgstr ""
"تأكد من وجود *شرطتين مائلتين للأمام* (`//`) قبل :guilabel:`CDN Base URL` "
"وشرطة واحدة مائلة (`/`) بعد :guilabel:`CDN Base URL`. "

#: ../../content/applications/websites/website/configuration/cdn.rst:66
msgid ":guilabel:`Save` the settings when complete."
msgstr "قم :guilabel:`بحفظ` الإعدادات عند الانتهاء. "

#: ../../content/applications/websites/website/configuration/cdn.rst-1
msgid "Activate the CDN setting in Odoo."
msgstr "قم بتفعيل إعداد CDN في أودو. "

#: ../../content/applications/websites/website/configuration/cdn.rst:72
msgid ""
"Now the website is using the CDN for the resources matching the "
":guilabel:`CDN filters` regular expressions."
msgstr ""
"والآن، يستخدم الموقع الإلكتروني CDN للموارد التي تطابق التعبيرات النمطية لـ "
":guilabel:`فلاتر CDN`. "

#: ../../content/applications/websites/website/configuration/cdn.rst:75
msgid ""
"In the HTML of the Odoo website, the :abbr:`CDN (content delivery network)` "
"integration is evidenced as working properly by checking the :abbr:`URL "
"(Uniform Resource Locators)` of images. The *CDN Base URL* value can be seen"
" by using your web browser's :guilabel:`Inspect` feature on the Odoo "
"website. Look for it's record by searching within the :guilabel:`Network` "
"tab inside of devtools."
msgstr ""
"في HTML موقع أودو الإلكتروني، تم التأكد من أن تكامل :abbr:`CDN (شبكة توصيل "
"المحتوى)` يعمل بشكل صحيح عن طريق التحقق من :abbr:`URL (محدد موقع الموارد "
"الموحد)` الخاص بالصور. يمكن رؤية قيمة *CDN Base URL* عن طريق استخدام خاصية "
"متصفح الويب :guilabel:`فحص` في موقع أودو الإلكتروني. اعثر على سجله عن طريق "
"البحث في علامة تبويب :guilabel:`الشبكة` tab inside of داخل أدوات التطوير. "

#: ../../content/applications/websites/website/configuration/cdn.rst-1
msgid ""
"The CDN Base URL can be seen using the inspect function on the Odoo website."
msgstr "يمكن رؤية CDN Base URL باستخدام خاصية الفحص في موقع أودو الإلكتروني. "

#: ../../content/applications/websites/website/configuration/cdn.rst:85
msgid ""
"Prevent security issues by activating cross-origin resource sharing (CORS)"
msgstr ""
"تمكن من منع المشاكل الأمنية عن طريق تفعيل مشاركة الموارد عبر الأصول (CORS) "

#: ../../content/applications/websites/website/configuration/cdn.rst:87
msgid ""
"A security restriction in some browsers (such as Mozilla Firefox and Google "
"Chrome) prevents a remotely linked CSS file to fetch relative resources on "
"this same external server."
msgstr ""
"التقييدات الأمنية الموجودة في بعض المتصفحات (مثل Mozilla Firefox و Google "
"Chrome) تمنع ملف CSS المرتبط عن بُعد من جلب الموارد ذات الصلة في نفس الخادم "
"الخارجي. "

#: ../../content/applications/websites/website/configuration/cdn.rst:90
msgid ""
"If the :abbr:`CORS (Cross-Origin Resource Sharing)` option isn't enabled in "
"the :guilabel:`CDN Zone`, the more obvious resulting problem on a standard "
"Odoo website will be the lack of *Font Awesome* icons because the font file "
"declared in the *Font Awesome* CSS won't be loaded from the remote server."
msgstr ""
"إذا لم يكن خيار :abbr:`CORS (مشاركة الموارد عبر الأصول)` مفعلاً في "
":guilabel:`منطقة CDN`، ستكون المشكلة الناتجة الأكثر وضوحاً على موقع أودو "
"قياسي هي الافتقار إلى أيقونات *Font Awesome* لأنه لن يتم تحميل ملف الخط "
"المحدد في CSS *Font Awesome* من الخادم عن بُعد. "

#: ../../content/applications/websites/website/configuration/cdn.rst:95
msgid ""
"When these cross-origin resource issues occur, a security error message "
"similar to the output below will appear in the web browser's developer "
"console:"
msgstr ""
"عندما تحدث مشاكل مشاركة الموارد عبر الأصول، ستظهر رسالة خطأ أمني شبيهة "
"بالمخرجات أدناه على أداة مطوِّر متصفح الويب: "

#: ../../content/applications/websites/website/configuration/cdn.rst:98
msgid ""
"``Font from origin 'http://pulltest-xxxxx.kxcdn.com' has been blocked from "
"loading /shop:1 by Cross-Origin Resource Sharing policy: No 'Access-Control-"
"Allow-Origin' header is present on the requested resource. Origin "
"'http://yourdatabase.odoo.com' is therefore not allowed access.``"
msgstr ""
"``تم حجب الخط من أصل 'http://pulltest-xxxxx.kxcdn.com' من التحميل /shop:1 من"
" قِبَل سياسة مشاركة الموارد عبر الأصول: لا توجد ترويسة 'Access-Control-"
"Allow-Origin' في المورد المطلوب. وبالتالي، لن يُسمَح للأصل "
"'http://yourdatabase.odoo.com' بالوصول.`` "

#: ../../content/applications/websites/website/configuration/cdn.rst-1
msgid "Error message populated in the browser console."
msgstr "رسالة خطأ تظهر في أداة المتصفح. "

#: ../../content/applications/websites/website/configuration/cdn.rst:106
msgid ""
"Enabling the :abbr:`CORS (Cross-Origin Resource Sharing)` option in the "
":abbr:`CDN (Content Delivery Network)` settings fixes this issue."
msgstr ""
"سيؤدي تمكين خيار :abbr:`CORS (مشاركة الموارد عبر الأصول)` في إعدادات "
":abbr:`CDN (شبكة توصيل المحتوى)` إلى إصلاح هذه المشكلة. "

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:3
msgid "Cookies bar"
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:5
msgid ""
"**Cookies** are small text files sent to your device when you visit a "
"website. They are processed and stored by your browser and track user "
"information like login details, preferences, and browsing history. "
"**Essential cookies** are necessary for the website to function, while "
"**optional cookies** are used to analyze behavior or display ads."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:10
msgid ""
"Data protection laws require notifying users about data collection methods "
"and purposes. **Cookies bar** fulfill this obligation by informing users on "
"their first visit and allowing them to decide whether to store all or only "
"essential cookies on their device."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:17
msgid ""
"To enable the cookies bar on your website, go to :menuselection:`Website -->"
" Configuration --> Settings` and enable :guilabel:`Cookies Bar` in the "
":guilabel:`Privacy` section."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:21
msgid ""
"The :ref:`Cookies Policy <cookies-bar/policy>` page (/cookie-policy`) is "
"automatically created when you enable the cookies bar."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:29
msgid ""
"To adapt the display of the cookies bar, click :guilabel:`Edit`, go to the "
":guilabel:`Invisible Elements` section at the bottom of the panel, and click"
" :guilabel:`Cookies Bar`. You can modify the :guilabel:`Layout` and "
":guilabel:`Size` of the cookies bar, and enable :guilabel:`Backdrop` to gray"
" out the page in the background when the cookies bar is displayed on the "
"screen."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:35
msgid ""
"Click anywhere in the building block to further customize the appearance of "
"the cookies bar using :guilabel:`Block`, :guilabel:`Column` and/or "
":guilabel:`Inline Text` customization options."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:38
msgid ""
"To edit the contents of the cookies bar (i.e., the consent message), click "
"directly in the building block."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:44
msgid "Cookies policy"
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:46
msgid ""
"When you enable the cookies bar for your website, Odoo creates the **Cookie "
"Policy** page (`/cookie-policy`) containing a non-exhaustive list of "
"cookies, with their purpose and examples. To access it, click the "
":guilabel:`Cookie Policy` hyperlink in the cookies bar or open the page from"
" :menuselection:`Website --> Site --> Pages`."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:51
msgid ""
"To adapt the content of the page according to your needs, click the "
":guilabel:`Edit` button."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:54
msgid ""
"You could add a link to this page in your website's footer, for example."
msgstr ""

#: ../../content/applications/websites/website/configuration/cookies_bar.rst:57
msgid ":doc:`Pages <../pages>`"
msgstr ":doc:`Pages <../pages>`"

#: ../../content/applications/websites/website/configuration/domain_names.rst:3
msgid "Domain names"
msgstr "أسماء النطاقات "

#: ../../content/applications/websites/website/configuration/domain_names.rst:5
msgid ""
"Domain names are text-based addresses identifying online locations, such as "
"websites. They provide a more memorable and recognizable way for people to "
"navigate the internet than numerical IP addresses."
msgstr ""
"أسماء النطاقات هي عناوين نصية تُعرّف المواقع عبر الإنترنت، كالمواقع "
"الإلكترونية. إنها تقدم طريقة أسهل للأفراد للتنقل عبر الإنترنت غير عناوين IP "
"العددية. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:8
msgid ""
"**Odoo Online** and **Odoo.sh** databases use a **subdomain** of the "
"`odoo.com` **domain** by default (e.g., `mycompany.odoo.com`)."
msgstr ""
"قواعد بيانات **أودو أونلاين** و **Odoo.sh** تستخدم **النطاق الفرعي** لـ "
"**نطاق** `odoo.com` بشكل افتراضي (مثال: `mycompany.odoo.com`). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:11
msgid ""
"However, you can use a custom domain name instead by :ref:`registering a "
"free domain name <domain-name/register>` (only available for Odoo Online "
"databases) or by :ref:`configuring a domain name you already own <domain-"
"name/existing>`."
msgstr ""
"ولكن، يمكنك استخدام اسم نطاق مخصص عوضاً عن ذلك عن طريق :ref:`تسجيل اسم نطاق "
"مجاني <domain-name/register>` (متاح فقط لقواعد بيانات أودو أونلاين) أو عن "
"طريق :ref:`تهيئة اسم نطاق تملكه بالفعل <domain-name/existing>`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:16
msgid ""
"`Odoo Tutorials: Register a free domain name [video] "
"<https://www.odoo.com/slides/slide/register-a-free-domain-name-1663>`_"
msgstr ""
"`دروس أودو التعليمية: تسجيل اسم نطاق مجاني [فيديو] "
"<https://www.odoo.com/slides/slide/register-a-free-domain-name-1663>`_ "

#: ../../content/applications/websites/website/configuration/domain_names.rst:22
msgid "Register a free domain name with Odoo"
msgstr "تسجيل اسم نطاق مجاني مع أودو "

#: ../../content/applications/websites/website/configuration/domain_names.rst:24
msgid ""
"To register a one-year free domain name for your Odoo Online database, sign "
"in to your account and go to the `database manager "
"<https://www.odoo.com/my/databases>`_. Click the gear icon (:guilabel:`⚙️`) "
"next to the database name and select :guilabel:`Domain Names`."
msgstr ""
"لتسجيل اسم نطاق مجاني لمدة عام واحد لقاعدة بياناتك على أودو أونلاين، قم "
"بتسجيل الدخول إلى حسابك ثم اذهب إلى \"مدير قاعدة البيانات "
"<https://www.odoo.com/my/databases>`_. اضغط على أيقونة الترس "
"(:guilabel:`⚙️`) بجوار اسم قاعدة البيانات وحدد :guilabel:`أسماء النطاقات`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst-1
msgid "Accessing a database's domain names configuration"
msgstr "الوصول إلى تهيئة أسماء نطاقات قاعدة البيانات "

#: ../../content/applications/websites/website/configuration/domain_names.rst:31
msgid "Search for the desired domain name and check its availability."
msgstr "قم بالبحث عن اسم النطاق المطلوب وتحقق من توافره. "

#: ../../content/applications/websites/website/configuration/domain_names.rst-1
msgid "Searching for an available domain name"
msgstr "البحث عن اسم نطاق متوفر "

#: ../../content/applications/websites/website/configuration/domain_names.rst:37
msgid ""
"Ensure the Website app is installed if the domain name registration option "
"does not appear."
msgstr ""
"تأكد من أن تطبيق الموقع الإلكتروني مثبت في حال عدم ظهور خيار تسجيل اسم "
"النطاق. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:39
msgid ""
"Select the desired domain name, fill in the :guilabel:`Domain Owner` form, "
"and click :guilabel:`Register`. The chosen domain name is directly linked to"
" the database."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst-1
msgid "Filling in the domain owner information"
msgstr "تعبئة معلومات صاحب النطاق "

#: ../../content/applications/websites/website/configuration/domain_names.rst:45
msgid ""
"Next, you should :ref:`map your domain name to your Odoo website <domain-"
"name/website-map>`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:48
msgid ""
"A verification email from `<EMAIL>` will be sent "
"to the email address provided in the :guilabel:`Domain Owner` form. It is "
"essential to verify your email address to keep the domain active and receive"
" the renewal quote before expiration."
msgstr ""
"سيتم إرسال بريد إلكتروني للتحقق من `<EMAIL>` إلى "
"عنوان البريد الإلكتروني المقدم في استمارة :guilabel:`صاحب النطاق`. من "
"الضروري التحقق من عنوان بريدك الإلكتروني للحفاظ على النطاق نشطاً والحصول على"
" عرض السعر للتجديد قبل انتهاء الصلاحية. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:52
msgid ""
"The domain name registration is free for the first year. After this period, "
"Odoo will continue to manage the domain in partnership with **Gandi.net**, "
"the domain name registrar, and you will be charged `Gandi.net's renewal rate"
" <https://www.gandi.net/en/domain>`_. Odoo sends a renewal quotation every "
"year to the email address mentioned in the :guilabel:`Domain Owner` form "
"several weeks before the expiration date of the domain. The domain is "
"renewed automatically when the quotation is confirmed."
msgstr ""
"تسجيل اسم النطاق مجاني للسنة الأولى. بعد هذه الفترة، سيستمر أودو في إدارة "
"النطاق بالشراكة مع **Gandi.net**، مسجل اسم النطاق، وستتم محاسبتك بسعر "
"التجديد الخاص بـ Gandi.net <https://www.gandi.net/en/domain>`_. يقوم أودو "
"بإرسال عرض سعر للتجديد كل عام إلى عنوان البريد الإلكتروني المذكور في استمارة"
" :guilabel:`صاحب النطاق` قبل عدة أسابيع من تاريخ انتهاء صلاحية النطاق. يتم "
"تجديد النطاق تلقائياً عند تأكيد عرض السعر. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:60
msgid "The offer is only available for **Odoo Online** databases."
msgstr "العرض متاح فقط لقواعد بيانات **أودو أونلاين**. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:61
msgid "The offer is limited to **one** domain name per client."
msgstr "العرض يسري فقط لاسم نطاق *واحد* لكل عميل. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:62
msgid "The offer is limited to the registration of a **new** domain name."
msgstr "العرض مخصص فقط لتسجيل اسم نطاق **جديد**. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:63
msgid ""
"The offer is available to *One App Free* plans. Ensure that your website "
"contains enough original content for Odoo to verify that your request is "
"legitimate and respects `Odoo's Acceptable Use Policy "
"<https://www.odoo.com/acceptable-use>`_. Given the high number of requests, "
"it can take Odoo several days to review them."
msgstr ""
"العرض متاح لخطط *تطبيق واحد مجاني*. تأكد من أن موقعك الإلكتروني يحتوي على "
"محتوى أصلي كافٍ حتى يتمكن أودو من التحقق من أن طلبك مشروع وأنه خاضع لـ "
"``سياسة الاستخدام المقبول لدى Odoo <https://www.odoo.com/acceptable-use>`_. "
"نظراً للعدد المتزايد من الطلبات، قد يستغرق أودو عدة أيام لمراجعتها. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:71
msgid "DNS records"
msgstr "سجلات DNS "

#: ../../content/applications/websites/website/configuration/domain_names.rst:73
msgid ""
"To manage your free domain name :abbr:`DNS (domain name system)` records, "
"open the `database manager <https://www.odoo.com/my/databases>`_, click the "
"gear icon (:guilabel:`⚙️`) next to the database name, select "
":guilabel:`Domain Names`, and click :guilabel:`DNS`."
msgstr ""
"لإدارة سجلات اسم النطاق المجاني الخاص بك :abbr:`DNS (نظام اسم المجال)`، قم "
"بفتح `مدير قاعدة البيانات <https://www.odoo.com/my/databases>`_، ثم اضغط على"
" رمز الترس (:guilabel: `⚙️`) بجوار اسم قاعدة البيانات، وحدد :guilabel:`أسماء"
" النطاقات`، ثم اضغط على :guilabel:`DNS`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:77
msgid ""
":guilabel:`A`: the A record holds the IP address of the domain. It is "
"automatically created and **cannot** be edited or deleted."
msgstr ""
":guilabel:`A`: يحتفظ السجل A بعنوان IP الخاص بالنطاق. يتم إنشاؤه تلقائياً "
"و**لا** يمكن تعديله أو حذفه. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:79
msgid ""
":guilabel:`CNAME`: CNAME records forward one domain or subdomain to another "
"domain. One is automatically created to map the `www.` subdomain to the "
"database. If the database is renamed, the CNAME record **must** also be "
"renamed."
msgstr ""
":guilabel:`CNAME`: تقوم سجلات CNAME بإعادة توجيه نطاق واحد أو نطاق فرعي إلى "
"نطاق آخر. يتم إنشاء أحدهما تلقائياً لتعيين النطاق الفرعي `www.` لقاعدة "
"البيانات. إذا تمت إعادة تسمية قاعدة البيانات، فيجب أيضاً إعادة تسمية سجل "
"CNAME **. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:82
msgid ""
":guilabel:`MX`: MX records instruct servers on where to deliver emails."
msgstr ""
":guilabel:`MX`: تقوم سجلات MX بإرشاد الخوادم إلى مكان تسليم رسائل البريد "
"الإلكتروني. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:83
msgid ""
":guilabel:`TXT`: TXT records can be used for different purposes (e.g., to "
"verify domain name ownership)."
msgstr ""
":guilabel:`TXT`: يمكن استخدام سجلات TXT لأغراض مختلفة (على سبيل المثال، "
"للتحقق من ملكية اسم النطاق). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:86
msgid ""
"Any modification to the DNS records can take up to **72 hours** to propagate"
" worldwide on all servers."
msgstr ""
"يمكن أن يستغرق أي تعديل على سجلات DNS ما يصل إلى **72 ساعة** للنشر في كافة "
"أنحاء العالم على كافة الخوادم. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:90
msgid ""
"`Submit a support ticket <https://www.odoo.com/help>`_ if you need "
"assistance to manage your domain name."
msgstr ""
"`قم بإرسال تذكرة دعم <https://www.odoo.com/help>`_ إذا كنت بحاجة إلى "
"المساعدة لإدارة اسم النطاق الخاص بك. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:94
msgid "Mailbox"
msgstr "صندوق البريد "

#: ../../content/applications/websites/website/configuration/domain_names.rst:96
msgid ""
"The one-year free domain name offer does **not** include a mailbox. There "
"are two options to link your domain name with a mailbox."
msgstr ""
"عرض اسم النطاق المجاني لمدة عام واحد **لا** يتضمن صندوق بريد. هناك خياران "
"لربط اسم النطاق الخاص بك بصندوق البريد. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:100
msgid "Use a subdomain"
msgstr "استخدام نطاق فرعي "

#: ../../content/applications/websites/website/configuration/domain_names.rst:102
msgid ""
"You can create a subdomain (e.g., `subdomain.yourdomain.com`) to use as an "
"alias domain for the database. It allows users to create records in the "
"database from emails received on their `<EMAIL>` "
"alias."
msgstr ""
"يمكنك إنشاء نطاق فرعي (مثال: `subdomain.yourdomain.com`) لاستخدامه كنطاق "
"للقب لقاعدة الباينات. إنه يتيح للمستخدمين إنشاء السجلات في قاعدة البيانات من"
" رسائل البريد الإلكتروني المستلمة في لقب البريد الإلكتروني "
"`<EMAIL>`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:106
msgid ""
"To do so, open the `database manager <https://www.odoo.com/my/databases>`_, "
"click the gear icon (:guilabel:`⚙️`) next to the database name, and go to "
":menuselection:`Domain Names --> DNS --> Add DNS record --> CNAME`. Next, "
"enter the desired subdomain in the :guilabel:`Name` field (e.g., "
"`subdomain`), the original database domain with a period at the end (e.g., "
"`mycompany.odoo.com.`) in the :guilabel:`Content` field, and click "
":guilabel:`Add record`."
msgstr ""
"للقيام بذلك، قم بفتح `مدير قاعدة البيانات "
"<https://www.odoo.com/my/databases>`_، ثم اضغط على أيقونة الترس "
"(:guilabel:`⚙️`) بجانب اسم قاعدة البيانات ثم اذهب إلى :menuselection:`أسماء "
"النطاق --> DNS --> إضافة سجل DNS --> CNAME`. تالياً، قم بإدخال اسم النطاق "
"المطلوب في حقل :guilabel:`الاسم` (مثال: `النطاق الفرعي`)، نطاق قاعدة "
"البيانات الأصلي مع نقطة في النهاية، (مثال: `mycompany.odoo.com.`) في حقل "
":guilabel:`المحتوى`، ثم اضغط على زر :guilabel:`إضافة سجل`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:112
msgid ""
"Then, add the alias domain as your *own domain* by clicking :guilabel:`Use "
"my own domain`, entering the alias domain (e.g., "
"`subdomain.yourdomain.com`), clicking :guilabel:`Verify`, and then "
":guilabel:`I confirm, it's done`."
msgstr ""
"بعد ذلك، أضف نطاق اللقب *كنطاقك الخاص* عن طريق الضغط على :guilabel:`استخدام "
"نطاقي الخاص`، ثم إدخال نطاق اللقب (مثال: `subdomain.yourdomain.com`)، واضغط "
"على :guilabel:`التحقق`، ثم :guilabel:`أؤكد، لقد تم الأمر`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:116
msgid ""
"Finally, go to your database and open the :guilabel:`Settings`. Under the "
":guilabel:`Alias Domain` field, enter the alias domain (e.g., "
"`subdomain.yourdomain.com`), click :guilabel:`Create`, and then "
":guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:121
msgid "Use an external email provider"
msgstr "استخدام مزوّد بريد إلكتروني خارجي "

#: ../../content/applications/websites/website/configuration/domain_names.rst:123
msgid ""
"To use an external email provider, you should configure an MX record. To do "
"so, open the `database manager <https://www.odoo.com/my/databases>`_, click "
"the gear icon (:guilabel:`⚙️`) next to the database name, click "
":menuselection:`Domain Names --> DNS --> Add DNS record --> MX`. The values "
"you should enter for the :guilabel:`Name`, :guilabel:`Content`, and "
":guilabel:`Priority` fields depend on the external email provider."
msgstr ""
"لاستخدام مزوّد بريد إلكتروني خارجي، سيتوجب عليك تهيئة سجل MX. للقيام بذلك، "
"قم بفتح \"مدير قاعدة البيانات\" <https://www.odoo.com/my/databases>`_، واضغط"
" على أيقونة الترس (:guilabel:`⚙️`) بجوار اسم قاعدة البيانات، ثم اضغط على "
":menuselection:` أسماء النطاقات -> DNS -> إضافة سجل DNS -> MX`. تعتمد القيم "
"التي يجب عليك إدخالها لحقول :guilabel:`الاسم` و :guilabel:`المحتوى` و "
":guilabel:`الأولوية` على مزوّد البريد الإلكتروني الخارجي. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:130
msgid ""
"`Google Workspace: MX record values "
"<https://support.google.com/a/answer/174125?hl=en>`_"
msgstr ""
"`Google Workspace: قيم سجل MX "
"<https://support.google.com/a/answer/174125?hl=en>`_"

#: ../../content/applications/websites/website/configuration/domain_names.rst:131
msgid ""
"`Outlook and Exchange Online: Add an MX record for email "
"<https://learn.microsoft.com/en-us/microsoft-365/admin/get-help-with-"
"domains/create-dns-records-at-any-dns-hosting-"
"provider?view=o365-worldwide#add-an-mx-record-for-email-outlook-exchange-"
"online>`_"
msgstr ""
"`Outlook و Exchange Online: أضف سجل MX للبريد الإلكتروني "
"<https://learn.microsoft.com/en-us/microsoft-365/admin/get-help-with-"
"domains/create-dns-records-at-any-dns-hosting-"
"provider?view=o365-worldwide#add-an-mx-record-for-email-outlook-exchange-"
"online>`_ "

#: ../../content/applications/websites/website/configuration/domain_names.rst:136
msgid "Configure an existing domain name"
msgstr "تهيئة اسم نطاق موجود "

#: ../../content/applications/websites/website/configuration/domain_names.rst:138
msgid ""
"If you already have a domain name, you can use it for your Odoo website."
msgstr ""
"إذا كان لديك اسم نطاق بالفعل، يمكنك استخدامه لموقعك الإلكتروني على أودو. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:141
msgid ""
"It is strongly recommended to follow **in order** these three steps to avoid"
" any :ref:`SSL certificate validation <domain-name/ssl>` issues:"
msgstr ""
"نوصي بشدة باتباع هذه الخطوات الثلاث **بالترتيب** لتجنب أية مشاكل في "
":ref:`تصديق شهادة SSL <domain-name/ssl>`: "

#: ../../content/applications/websites/website/configuration/domain_names.rst:144
msgid ":ref:`Add a CNAME record <domain-name/cname>`"
msgstr ":ref:`أضف سجل CNAME <domain-name/cname>` "

#: ../../content/applications/websites/website/configuration/domain_names.rst:145
msgid ":ref:`Map your domain name to your Odoo database <domain-name/db-map>`"
msgstr ":ref:`قم بتعيين اسم نطاقك في قاعدة بيانات أودو <domain-name/db-map>` "

#: ../../content/applications/websites/website/configuration/domain_names.rst:146
msgid ""
":ref:`Map your domain name to your Odoo website <domain-name/website-map>`"
msgstr ""
":ref:`قم بتعيين اسم نطاقك في موقعك الإلكتروني على أودو <domain-name/website-"
"map>` "

#: ../../content/applications/websites/website/configuration/domain_names.rst:151
msgid "Add a CNAME record"
msgstr "إضافة سجل CNAME "

#: ../../content/applications/websites/website/configuration/domain_names.rst:153
msgid ""
"Adding a CNAME record to forward your domain name to the address of your "
"Odoo database is required."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:157
#: ../../content/applications/websites/website/configuration/domain_names.rst:273
msgid "Odoo Online"
msgstr "أودو أونلاين "

#: ../../content/applications/websites/website/configuration/domain_names.rst:159
msgid ""
"The CNAME record's target address should be your database's address as "
"defined at its creation (e.g., `mycompany.odoo.com`)."
msgstr ""
"يجب أن يكون العنوان المستهدف لسجل CNAME هو عنوان قاعدة بياناتك كما هو محدد "
"عند إنشائها (مثال: `mycompany.odoo.com`). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:162
#: ../../content/applications/websites/website/configuration/domain_names.rst:283
msgid "Odoo.sh"
msgstr "Odoo.sh"

#: ../../content/applications/websites/website/configuration/domain_names.rst:164
msgid ""
"The CNAME record's target address should be the project's main address, "
"which can be found on Odoo.sh by going to :menuselection:`Settings --> "
"Project Name`, or a specific branch (production, staging or development) by "
"going to :menuselection:`Branches --> select the branch --> Settings --> "
"Custom domains`, and clicking :guilabel:`How to set up my domain?`. A "
"message indicates which address your CNAME record should target."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:170
msgid "The specific instructions depend on your DNS hosting service."
msgstr "تعتمد الإرشادات المحددة على خدمة استضافة DNS الخاصة بك. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:173
msgid ""
"`GoDaddy: Add a CNAME record <https://www.godaddy.com/help/add-a-cname-"
"record-19236>`_"
msgstr ""
"`GoDaddy: أضف سجل CNAME <https://www.godaddy.com/help/add-a-cname-"
"record-19236>`_ "

#: ../../content/applications/websites/website/configuration/domain_names.rst:174
msgid ""
"`Namecheap: How to create a CNAME record for your domain "
"<https://www.namecheap.com/support/knowledgebase/article.aspx/9646/2237/how-"
"to-create-a-cname-record-for-your-domain>`_"
msgstr ""
"`Namecheap: كيفية إنشاء سجل CNAME لنطاقك "
"<https://www.namecheap.com/support/knowledgebase/article.aspx/9646/2237/how-"
"to-create-a-cname-record-for-your-domain>`_ "

#: ../../content/applications/websites/website/configuration/domain_names.rst:175
msgid ""
"`OVHcloud: Add a new DNS record "
"<https://docs.ovh.com/us/en/domains/web_hosting_how_to_edit_my_dns_zone/#add-"
"a-new-dns-record>`_"
msgstr ""
"`OVHcloud: إضافة سجل DNS جديد "
"<https://docs.ovh.com/us/en/domains/web_hosting_how_to_edit_my_dns_zone/#add-"
"a-new-dns-record>`_ "

#: ../../content/applications/websites/website/configuration/domain_names.rst:176
msgid ""
"`Cloudflare: Manage DNS records "
"<https://developers.cloudflare.com/dns/manage-dns-records/how-to/create-dns-"
"records/>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:180
msgid ""
"Odoo only supports subdomains. To use your naked domain name :dfn:`(a domain"
" name without any subdomains or prefixes)` (`yourdomain.com`), create a "
"redirection 301 to redirect visitors to `www.yourdomain.com`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:185
msgid ""
"You own the domain name `yourdomain.com`, and your Odoo Online database's "
"address is `mycompany.odoo.com`. You want to access your Odoo database "
"primarily with the domain `www.yourdomain.com` and also with the naked "
"domain `yourdomain.com`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:189
msgid ""
"To do so, create a CNAME record for the `www` subdomain, with "
"`mycompany.odoo.com` as the target. Next, create a redirect (301 permanent "
"or visible redirect) to redirect visitors from `yourdomain.com` to "
"`wwww.yourdomain.com`."
msgstr ""
"للقيام بذلك، قم بإنشاء سجل CNAME للنطاق الفرعي `www`، مع تحديد "
"`mycompany.odoo.com` كهدف. بعد ذلك، أنشئ عملية إعادة توجيه (إعادة توجيه 301 "
"دائمة أو مرئية) لإعادة توجيه الزائرين من \"yourdomain.com\" إلى "
"\"www.yourdomain.com\". "

#: ../../content/applications/websites/website/configuration/domain_names.rst:194
msgid "Using Cloudflare to secure and redirect a naked domain"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:196
msgid ""
"To redirect a naked domain with a secure HTTPS connection, we recommend "
"using Cloudflare, as most DNS hosting services do not offer an easy way to "
"do so."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:199
msgid ""
"`Sign up and log in to Cloudflare <https://dash.cloudflare.com/sign-up>`_."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:200
msgid ""
"Enter your domain name on `Cloudflare's dashboard "
"<https://dash.cloudflare.com/login>`_ and select :guilabel:`Quick scan for "
"DNS records`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:202
msgid "Choose a plan (the free plan is sufficient)."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:203
msgid ""
"Follow Cloudflare's instructions and recommendations to complete the "
"activation."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:204
msgid ""
"Add a CNAME record to redirect your naked domain (`yourdomain.com`) to the "
"`www` subdomain (e.g., `www.yourdomain.com`) by clicking :guilabel:`DNS` in "
"the navigation menu, then clicking the :guilabel:`Add record` button, and "
"using the following configuration:"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:208
#: ../../content/applications/websites/website/configuration/domain_names.rst:219
msgid ":guilabel:`Type`: CNAME"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:209
msgid ":guilabel:`Name`: `@` (or `yourdomain.com`)"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:210
msgid ":guilabel:`Target`: e.g., `www.yourdomain.com`"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:211
msgid ":guilabel:`Proxy status`: Proxied"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid ""
"Adding a CNAME DNS record to Cloudflare to redirect a naked domain to a www "
"subdomain"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:216
msgid ""
"Add another second CNAME record to redirect the `www` subdomain (e.g., "
"`www.yourdomain.com`) to your database address (e.g., `mycompany.odoo.com`) "
"using the following configuration:"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:220
msgid ":guilabel:`Name`: e.g., `www.yourdomain.com`"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:221
msgid ":guilabel:`Target`: e.g., `mycompany.odoo.com`"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:222
msgid ":guilabel:`Proxy status`: DNS only"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid ""
"Adding a CNAME DNS record to Cloudflare to redirect a www subdomain to an "
"Odoo database"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:227
msgid ""
"Define a redirect rule to permanently redirect (301) your naked domain "
"(e.g., `yourdomain.com`) to both `http://` and `https://` by going to "
":menuselection:`Rules --> Create rule --> Products`, and clicking "
":guilabel:`Create a Rule` under :guilabel:`Redirect Rules`:"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:231
msgid "Enter any :guilabel:`Rule name`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:232
msgid ""
"Under the :guilabel:`If incoming requests match...` section, select "
":guilabel:`Custom filter expression` and use the following configuration:"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:235
msgid ":guilabel:`Field`: Hostname"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:236
msgid ":guilabel:`Operator`: equals"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:237
msgid ":guilabel:`Value`: e.g., `yourdomain.com`"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:239
msgid ""
"Under the :guilabel:`Then...` section, use the following configuration:"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:241
msgid ":guilabel:`Type`: Dynamic"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:242
msgid ""
":guilabel:`Expression`: e.g., `concat(\"https://www.yourdomain.com\", "
"http.request.uri.path)`"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:243
msgid ":guilabel:`Status code`: 301"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:244
msgid ":guilabel:`Preserve query string`: enabled"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid ""
"Defining a Cloudflare redirect rule to create a permanent redirect (301)"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:249
msgid ""
"Go to :guilabel:`SSL/TLS` and set the encryption mode to :guilabel:`Full`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid "Setting the encryption mode to full on Cloudflare"
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:257
msgid "Map a domain name to an Odoo database"
msgstr "تعيين اسم النطاق لقاعدة بيانات على أودو "

#: ../../content/applications/websites/website/configuration/domain_names.rst:260
msgid ""
"Ensure you have :ref:`added a CNAME record <domain-name/cname>` to your "
"domain name's DNS **before** mapping your domain name to your Odoo database."
msgstr ""
"تأكد من أنك قد قمت بـ :ref:`إضافة سجل CNAME <domain-name/cname>` إلى DNS "
"الخاص باسم نطاقك **قبل** تعيين اسم نطاقك إلى قاعدة بياناتك على أودو. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:263
msgid ""
"Failing to do so may prevent the validation of the :ref:`SSL certificate "
"<domain-name/ssl>` and could result in a *certificate name mismatch* error. "
"Web browsers often display this as a warning, such as *\"Your connection is "
"not private\"*."
msgstr ""
"قد يؤدي الفشل في القيام بذلك إلى منع التحقق من صحة :ref:`شهادة SSL <domain-"
"name/ssl>` وقد يؤدي إلى خطأ *عدم تطابق اسم الشهادة*. غالباً ما تَعرِض "
"متصفحات الويب ذلك كتحذير، مثل *\"اتصالك ليس خاصاً\"*. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:267
msgid ""
"If you encounter this error after mapping the domain name to your database, "
"wait up to five days, as the validation may still happen. If not, you can "
"`submit a support ticket <https://www.odoo.com/help>`_, including "
"screenshots of your CNAME records."
msgstr ""
"إذا واجهت هذا الخطأ بعد تعيين اسم النطاق لقاعدة بياناتك، فانتظر لمدة تصل إلى"
" خمسة أيام، حيث قد تكون عملية التصديق لا تزال مستمرة. إذا لم يكن الأمر كذلك،"
" فيمكنك إرسال تذكرة دعم <https://www.odoo.com/help>`_، بما في ذلك لقطات شاشة"
" لسجلات CNAME الخاصة بك. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:275
msgid ""
"Open the `database manager <https://www.odoo.com/my/databases>`_, click the "
"gear icon (:guilabel:`⚙️`) next to the database name, and go to "
":menuselection:`Domain Names --> Use my own domain`. Then, enter the domain "
"name (e.g., `www.yourdomain.com`), click :guilabel:`Verify` and :guilabel:`I"
" confirm, it's done`."
msgstr ""

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid "Mapping a domain name to an Odoo Online database"
msgstr "تعيين اسم النطاق لقاعدة بيانات على أودو أونلاين "

#: ../../content/applications/websites/website/configuration/domain_names.rst:285
msgid ""
"On Odoo.sh, go to :menuselection:`Branches --> select your branch --> "
"Settings --> Custom domains`, type the domain name to add, then click "
":guilabel:`Add domain`."
msgstr ""
"في Odoo.sh، اذهب إلى :menuselection:`الفروع --> حدد فرعك --> الإعدادات --> "
"النطاقات المخصصة` واكتب اسم النطاق المراد إضافته، ثم اضغط على "
":guilabel:`إضافة النطاق`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:0
msgid "Mapping a domain name to an Odoo.sh branch"
msgstr "تعيين اسم النطاق لفرع Odoo.sh "

#: ../../content/applications/websites/website/configuration/domain_names.rst:292
msgid ""
":ref:`Odoo.sh branches: settings tab <odoosh-gettingstarted-branches-tabs-"
"settings>`"
msgstr ""
"علامة تبويب إعدادات :ref:`فروع Odoo.sh: <odoosh-gettingstarted-branches-"
"tabs-settings>` "

#: ../../content/applications/websites/website/configuration/domain_names.rst:297
msgid "SSL encryption (HTTPS protocol)"
msgstr "تشفير SSL (بروتوكول HTTPS) "

#: ../../content/applications/websites/website/configuration/domain_names.rst:299
msgid ""
"**SSL encryption** allows visitors to navigate a website through a secure "
"connection, which appears as the *https://* protocol at the beginning of a "
"web address rather than the non-secure *http://* protocol."
msgstr ""
"**تشفير SSL** يتيح للزائرين بالتنقل عبر موقع الويب من خلال اتصال آمن، والذي "
"يظهر كبروتوكول *https://* في بداية عنوان الويب عوضاً عن بروتوكول *http://* "
"غير الآمن. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:303
msgid ""
"Odoo generates a separate SSL certificate for each domain :ref:`mapped to a "
"database <domain-name/db-map>` using `Let's Encrypt's certificate authority "
"and ACME protocol <https://letsencrypt.org/how-it-works/>`_."
msgstr ""
"يقوم أودو بإنشاء شهادة SSL منفصلة لكل نطاق :ref:`تم تعيينه لقاعدة بيانات "
"<domain-name/db-map>` باستخدام هيئة شهادة \"Let's Encrypt\" وبروتوكول ACME  "
"<https://letsencrypt.org/how-it-works/>`_."

#: ../../content/applications/websites/website/configuration/domain_names.rst:308
msgid "Certificate generation may take up to 24 hours."
msgstr "قد تستغرق عملية إنشاء الشهادة حتى 24 ساعة. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:309
msgid ""
"Several attempts to validate your certificate are made for five days after "
"you map your domain name to your database."
msgstr ""
"تم القيام بعدة محاولات لتصديق شهادتك لمدة خمس أيام من تعيينك لاسم نطاقك في "
"الإعدادات. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:311
msgid "If you use another service, you can keep using it or change to Odoo's."
msgstr ""
"إذا كنت تستخدم خدمة أخرى، فيمكنك الاستمرار في استخدامها أو التغيير إلى خدمة "
"أودو. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:314
msgid ""
"No SSL certificate is generated for naked domains :dfn:`(domain names "
"without any subdomains or prefixes)`."
msgstr ""
"لم يتم إنشاء شهادة SSL للنطاقات المجردة :dfn:`(أسماء النطاقات دون أي نطاقات "
"فرعية أو بادئات)`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:320
msgid "Web base URL of a database"
msgstr "رابط URL الأساسي على الويب لقاعدة البيانات "

#: ../../content/applications/websites/website/configuration/domain_names.rst:323
msgid ""
"If the Website app is installed on your database, skip this section and "
"continue from the :ref:`Map a domain name to a website <domain-name/website-"
"map>` section."
msgstr ""
"إذا كان تطبيق الموقع الإلكتروني مثبتاً على قاعدة بياناتك، فتخط هذا القسم "
"وتابع من قسم :ref:`تعيين اسم نطاق لموقع إلكتروني <domain-name/website-map>`."
" "

#: ../../content/applications/websites/website/configuration/domain_names.rst:326
msgid ""
"The *web base URL* or root URL of a database affects your main website "
"address and all the links sent to your customers (e.g., quotations, portal "
"links, etc.)."
msgstr ""
"يؤثر *رابط URL الأساسي على الويب* لقاعدة البيانات، أو root URL على عنوان "
"موقعك الإلكتروني الرئيسي وكافة الروابط المرسلة إلى عملائك (مثال: عروض "
"الأسعار، روابط البوابة، إلخ..). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:329
msgid ""
"To make your custom domain name the *web base URL* of your database, access "
"your database using your custom domain name and log in as an administrator "
":dfn:`(a user part of the Settings access right group under "
"Administration)`."
msgstr ""
"لجعل اسم نطاقك المخصص هو *عنوان URL الأساسي للويب* لقاعدة بياناتك، قم "
"بالوصول إلى قاعدة بياناتك باستخدام اسم النطاق المخصص الخاص بك وقم بتسجيل "
"الدخول كمسؤول :dfn:`(مستخدم يكون جزءاً من مجموعة صلاحية الوصول إلى الإعدادات"
" ضمن الإدارة)`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:334
msgid ""
"If you access your database with the original Odoo address (e.g., "
"`mycompany.odoo.com`), the *web base URL* of your database will be updated "
"accordingly. To prevent the automatic update of the *web base URL* when an "
"administrator logs in to the database, activate the :ref:`developer mode "
"<developer-mode>`, go to :menuselection:`Settings --> Technical --> System "
"Parameters --> New`, and enter `web.base.url.freeze` as the :guilabel:`Key` "
"and `True` as the :guilabel:`Value`."
msgstr ""
"إذا قمت بالوصول إلى قاعدة بياناتك باستخدام عنوان أودو الأصلي (مثال: "
"`mycompany.odoo.com`)، فسيتم تحديث *عنوان URL الأساسي للويب* لقاعدة بياناتك "
"وفقاً لذلك. لمنع التحديث التلقائي لـ *عنوان URL لقاعدة الويب* عندما يقوم "
"المسؤول بتسجيل الدخول إلى قاعدة البيانات، قم بتفعيل :ref:`وضع المطوِّر "
"<developer-mode>` ثم اذهب إلى :menuselection:`الإعدادات --> تقني --> معايير "
"النظام --> جديد، وقم بإدخال `web.base.url.freeze` كـ :guilabel:`المفتاح` "
"و`صحيح` كـ :guilabel:`القيمة`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:341
msgid ""
"You can also set the web base URL manually. To do so, activate the "
":ref:`developer mode <developer-mode>`, go to :menuselection:`Settings --> "
"Technical --> System Parameters`, and search for the `web.base.url` key "
"(create it if necessary) and enter the full address of your website as the "
"value (e.g., `https://www.yourdomain.com`). The URL must include the "
"protocol `https://` (or `http://`) and *not* end with a slash (`/`)."
msgstr ""
"يمكنك أيضاً تعيين عنوان URL الأساسي للويب يدوياً. للقيام بذلك، قم يتفعيل "
":ref:`وضع المطوِّر <developer-mode>` ثم اذهب إلى :menuselection:`الإعدادات "
"--> تقني --> معايير النظام`، وابحث عن مفتاح `web.base.url` (قم بإنشائه إذا "
"لزم الأمر) وأدخل العنوان الكامل لموقعك الإلكتروني كقيمة (مثال: "
"`https://www.yourdomain.com`). يجب أن يتضمن عنوان URL البروتوكول `https://` "
"(أو `http://`) و*لا* ينتهي بشرطة مائلة (`/`). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:350
msgid "Map a domain name to an Odoo website"
msgstr "تعيين اسم النطاق لموقع إلكتروني على أودو "

#: ../../content/applications/websites/website/configuration/domain_names.rst:352
msgid ""
"Mapping your domain name to your website is different than mapping it to "
"your database:"
msgstr ""
"تعيين اسم نطاقك في موقعك الإلكتروني يختلف عن تعيينه في قاعدة بياناتك: "

#: ../../content/applications/websites/website/configuration/domain_names.rst:354
msgid ""
"It defines your domain name as the main one for your website, helping search"
" engines to index your website correctly."
msgstr ""
"يحدد اسم نطاقك كاسم النطاق الرئيسي لموقعك الإلكتروني، مما يساعد محركات البحث"
" على فهرسة موقعك الإلكتروني بشكل صحيح. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:356
msgid ""
"It defines your domain name as the base URL for your database, including "
"portal links sent by email to your customers."
msgstr ""
"يقوم بتحديد اسم نطاقك كرابط URL الأساسي لقاعدة بياناتك، شامل روابط البوابة "
"المرسلة عبر البريد الإلكتروني إلى عملائك. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:358
msgid ""
"If you have multiple websites, it maps your domain name to the appropriate "
"website."
msgstr ""
"إذا كانت لديك مواقع إلكترونية متعددة، يقوم بتعيين اسم نطاقك للموقع "
"الإلكتروني المناسب. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:360
msgid ""
"Go to :menuselection:`Website --> Configuration --> Settings`. If you have "
"multiple websites, select the one you want to configure. In the "
":guilabel:`Domain` field, enter the address of your website (e.g., "
"`https://www.yourdomain.com`) and :guilabel:`Save`."
msgstr ""
"قم بالذهاب إلى :menuselection:`الموقع الإلكتروني --> التهيئة --> الإعدادات`."
" إذا كانت لديك مواقع إلكترونية متعددة، فقم بتحديد الموقع الإلكتروني الذي "
"ترغب في تهيئته. في حقل :guilabel:`النطاق`، أدخل عنوان موقعك الإلكتروني "
"(مثال: `https://www.yourdomain.com`) ثم اضغط على :guilabel:`حفظ`. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:365
msgid ""
"Mapping your domain name to your Odoo website prevents Google Search from "
"indexing your original database address (e.g., `mycompany.odoo.com`)."
msgstr ""
"يؤدي تعيين اسم النطاق الخاص بك لموقعك الإلكتروني على أودو إلى منع بحث Google"
" من فهرسة عنوان قاعدة البيانات الأصلية (مثال: `mycompany.odoo.com`). "

#: ../../content/applications/websites/website/configuration/domain_names.rst:368
msgid ""
"If both addresses are already indexed, it may take some time before the "
"indexation of the second address is removed from Google Search. You can use "
"the `Google Search Console <https://search.google.com/search-"
"console/welcome>`_ to fix the issue."
msgstr ""
"إذا تمت فهرسة كلا العنوانين بالفعل، فقد يستغرق الأمر بعض الوقت قبل إزالة "
"فهرسة العنوان الثاني من بحث Google. يمكنك استخدام `أداة بحث Google "
"<https://search.google.com/search-console/welcome>`_ لإصلاح المشكلة. "

#: ../../content/applications/websites/website/configuration/domain_names.rst:373
msgid ""
"If you have multiple websites and companies on your database, make sure to "
"select the right :guilabel:`Company` under :menuselection:`Website --> "
"Configuration --> Settings`. Doing so indicates Odoo which URL to use as the"
" :ref:`base URL <domain-name/web-base-url>` according to the company in use."
msgstr ""
"إذا كان لديك العديد من المواقع الإلكترونية والشركات في قاعدة بياناتك، فتأكد "
"من تحديد :guilabel:`الشركة` الصحيحة ضمن :menuselection:`الموقع الإلكتروني "
"--> التهيئة --> الإعدادات`. يؤدي القيام بذلك إلى تحديد عنوان URL الذي "
"سيتخدمه أودو كـ :ref:`عنوان URL الأساسي <domain-name/web-base-url>` وفقاً "
"للشركة المستخدمة. "

#: ../../content/applications/websites/website/configuration/google_search_console.rst:3
msgid "Google Search Console"
msgstr "أداة بحث Google "

#: ../../content/applications/websites/website/configuration/google_search_console.rst:5
msgid ""
"Google Search Console is a free web service provided by Google that allows "
"website owners to monitor, maintain, and troubleshoot their site's presence "
"in Google Search results. It offers valuable insights into how Google views "
"and interacts with your site, helping you optimize its performance."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:10
msgid ""
"To enable Google Search Console for your website, go to `Google Search "
"Console <https://search.google.com/search-console/welcome>`_. Then, select "
"the property type :ref:`GSC-Domain` or :ref:`GSC-URL prefix`."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst-1
msgid "Google Search Console domain or URL prefix"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:20
msgid "Domain property"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:22
msgid ""
"A domain property in Search Console tracks all versions of your website, "
"including subdomains and protocols (http/https). This comprehensive view "
"allows you to analyze your overall website's search performance and make "
"informed decisions to optimize its visibility. Enter the domain, e.g., "
"`example.com` and click :guilabel:`Continue`."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:28
msgid ""
"The domain property type can only be verified via `DNS record "
"<https://support.google.com/webmasters/answer/9008080?hl=en#domain_name_verification&zippy=%2Chtml-"
"tag>`_."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:30
msgid ""
"Google suggests creating at least one domain property to represent your "
"site, as it is the most complete view of your website information."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:36
msgid "URL prefix property"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:38
msgid ""
"This type of verification is usually simpler as you have multiple "
"verification methods, such as using your existing Google Analytics or Tag "
"Manager account. It also makes sense to view a section of your website "
"separately. For example, if you work with a consultant on a specific part of"
" your website, you might want to verify this part separately to limit access"
" to your data. Enter the URL, e.g., `https://www.example.odoo.com` and click"
" :guilabel:`Continue`."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:46
msgid "Site ownership verification"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:48
msgid ""
"Before using Google Search Console for your website, you must verify your "
"site ownership. This verification process is a security measure that "
"protects both you and Google. It ensures that only authorized users have "
"access to sensitive data and that you have control over how your website is "
"treated in Google Search."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:53
msgid "Five methods are available to do this:"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:57
msgid ":ref:`GSC-HTML-file-upload`"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:58
msgid ""
"`DNS record "
"<https://support.google.com/webmasters/answer/9008080?hl=en#domain_name_verification&zippy=%2Chtml-"
"tag>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:59
msgid ""
"`HTML tag "
"<https://support.google.com/webmasters/answer/9008080?hl=en#meta_tag_verification&zippy=%2Chtml-"
"tag>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:60
msgid ""
"`Google Analytics tracking code "
"<https://support.google.com/webmasters/answer/9008080?hl=en#google_analytics_verification>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:61
msgid ""
"`Google Tag Manager container snippet "
"<https://support.google.com/webmasters/answer/9008080?hl=en#google_tag_manager_verification>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:64
msgid ""
"The best method for you depends on your comfort level and technical "
"expertise. For beginners, using a file upload or HTML tag might be easiest. "
"Those options are convenient if you already use Google Analytics or Tag "
"Manager. You need to access your domain registrar's settings for domain "
"verification."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:72
msgid "HTML file upload"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:74
msgid ""
"This method involves uploading an HTML file provided by Google containing "
"the verification code you have to put in your Odoo's Website Settings. "
"Google verifies ownership by checking for this code."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:77
msgid ""
"Once you added your website URL under the URL prefix option and clicked "
":guilabel:`continue`, expand the HTML file section where you find a download"
" :icon:`fa-download` button."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:0
msgid "HTML file download"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:83
msgid ""
"Download your HTML verification file and copy the verification code (e.g., "
"`google123abc.html`)."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:0
msgid "Open and copy html file"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:88
msgid ""
"In your Odoo database, go to :menuselection:`Website --> Configuration --> "
"Settings`, and enable :guilabel:`Google Search Console` in the "
":guilabel:`SEO` section. Paste the verification code (e.g., "
"`google123abc.html`) in the dedicated field."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:0
msgid "Paste html code in Odoo"
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:95
msgid ""
"In Google Search Console, click :guilabel:`Verify`. If you perform the steps"
" above correctly, verification should be done immediately."
msgstr ""

#: ../../content/applications/websites/website/configuration/google_search_console.rst:99
msgid ":doc:`domain_names`"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:3
msgid "Multiple websites"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:5
msgid ""
"Odoo allows you to create multiple websites from the same database. This can"
" be useful, for example, if you have multiple brands operating under your "
"organization, or to create separate websites for different "
"products/services, or different audiences. In these cases, having different "
"websites can help avoid confusion and make it easier to tailor your digital "
"outreach strategies and reach your target audience."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:11
msgid ""
"Each website can be designed and configured independently with its own "
":doc:`domain name <domain_names>`, :doc:`theme <../web_design/themes>`, "
":doc:`pages <../pages>`, :doc:`menus <../pages/menus>`, :doc:`languages "
"<translate>`, :doc:`products <../../ecommerce/products>`, assigned sales "
"team, etc. They can also :ref:`share content and pages <multi-"
"website/website_content>`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:18
msgid ""
"Duplicate content (i.e., pages and content shared between multiple websites)"
" can have a negative impact on :doc:`../pages/seo`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:22
msgid "Website creation"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:24
msgid "To create a new website, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:27
msgid "Click :guilabel:`+ New Website`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:0
msgid "New website button"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:32
msgid ""
"Specify the :guilabel:`Website Name` and :guilabel:`Website domain`. Each "
"website must be published under its own :doc:`domain <domain_names>`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:34
msgid ""
"Adapt the :guilabel:`Company name`, :guilabel:`Languages` and "
":guilabel:`Default language` if needed."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:36
msgid "Click the :guilabel:`Create` button."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:38
msgid "You can then start building your new website."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:41
msgid ""
"By default, all website-related apps that you have installed (e.g. "
"**eCommerce**, **Forum**, **Blog**, etc.) and their related website pages "
"are also available on the new website. You can remove them by amending the "
"website's menu."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:46
msgid "Switching websites"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:48
msgid ""
"To switch from one website to another, click the menu next to the "
":guilabel:`+New` button in the top right corner and select the website you "
"want to switch to."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst-1
msgid "Website selector"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:55
msgid ""
"When you switch websites, you are redirected to the homepage of the other "
"website."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:58
msgid "Website-specific configuration"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:60
msgid ""
"Most website settings are website-specific, which means they can be "
"enabled/disabled per website. To adapt the settings for a website, go to "
":menuselection:`Website --> Configuration --> Settings`. Select the desired "
"website in the field :guilabel:`Settings of Website` at the top of the "
":guilabel:`Settings` page, in the **yellow** banner. Then, adapt the options"
" for that specific website."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:67
msgid ""
"Websites are created with the default settings; the settings are not copied "
"from one website to the other."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:69
msgid ""
"In a :doc:`multi-company environment </applications/general/companies>`, "
"each website can be linked to a specific company in your database so that "
"only company-related data (e.g., products, jobs, events, etc.) is displayed "
"on the website. To display company-specific data, set the desired company in"
" the :guilabel:`Company` field."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:77
msgid "Content availability"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:79
msgid ""
"By default, pages, products, events, etc. created from the frontend (using "
"the :guilabel:`+New` button) are only available on the website from which it"
" was created. Records created from the backend, however, are made available "
"on all websites by default. The content's availability can be changed in the"
" backend, in the :guilabel:`Website` field. For example, for products, go to"
" :menuselection:`eCommerce --> Products`, then select the product and go to "
"the :guilabel:`Sales` tab. For forums, go to :menuselection:`Configuration "
"--> Forums`, then select the forum."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst-1
msgid "Website field in Forum form"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:92
msgid "Records and features can be made available:"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:94
msgid "On all websites: leave the :guilabel:`Website` field empty;"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:95
msgid "Only on one website: set the :guilabel:`Website` field accordingly;"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:96
msgid ""
"On some websites: in this case, you should duplicate the item and set the "
":guilabel:`Website` field."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:100
msgid "Website pages"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:102
msgid ""
"To modify the website on which a page is to be published, proceed as "
"follows:"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:104
msgid "Go to :menuselection:`Website --> Site --> Pages`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:105
msgid ""
"Open the search panel and select the website on which the page is currently "
"published."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:0
msgid "Display pages per website"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:110
msgid "Tick the check box next to the page(s) you want to change."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:111
msgid ""
"Click the :guilabel:`Website` field and select the website, or empty it to "
"publish the page on all websites."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:115
msgid ""
"Each website must have its own homepage; you may not use the same homepage "
"for several websites."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:118
msgid "eCommerce features"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:120
msgid ""
"eCommerce features such as products, eCommerce categories, pricelists, "
"discounts, payment providers, etc. can be restricted to :ref:`a specific "
"website <website_field>`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:126
msgid ""
"You can :doc:`allow your customers to use the same account "
"<../../ecommerce/customer_accounts>` on all of your websites by enabling the"
" :guilabel:`Shared Customer Accounts` check box in the website settings."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:131
msgid "Pricing"
msgstr "الأسعار "

#: ../../content/applications/websites/website/configuration/multi_website.rst:133
msgid ""
"Products can be priced differently based on the website using "
":ref:`pricelists <ecommerce/pricelists>`. The following configuration is "
"required:"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:137
msgid ""
"Scroll down to the :guilabel:`Shop - Products` section and select the "
":guilabel:`Pricelists` option :guilabel:`Multiple prices per product`."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:139
msgid ""
"Click :guilabel:`Pricelists` to define new pricelists or edit existing ones."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:140
msgid ""
"Select the pricelist or click :guilabel:`New` to create a new one, then "
"select the :guilabel:`Configuration` tab and set the :guilabel:`Website` "
"field."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:144
#: ../../content/applications/websites/website/reporting.rst:5
msgid "Reporting"
msgstr "إعداد التقارير "

#: ../../content/applications/websites/website/configuration/multi_website.rst:149
msgid ""
"Each website has its own :ref:`analytics <analytics/plausible>`. To switch "
"between websites, click the buttons in the upper right corner."
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst-1
msgid "Switch websites in analytics"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:156
msgid "Other reporting data"
msgstr ""

#: ../../content/applications/websites/website/configuration/multi_website.rst:158
msgid ""
"Other reporting data such as eCommerce dashboard data, online sales analyses"
" and visitors can be grouped by website if necessary. Open the search panel "
"and select :guilabel:`Group by --> Website`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:3
msgid "Forms spam protection"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:5
msgid ""
":ref:`Cloudflare Turnstile <cloudflare-turnstile>` and :ref:`Google "
"reCAPTCHA v3 <google-recaptcha>` protect website forms against spam and "
"abuse. They attempt to distinguish between human and bot submissions using "
"non-interactive challenges based on telemetry and visitor behavior."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:10
msgid ""
"We recommend using **Cloudflare Turnstile** as reCAPTCHA v3 may not be "
"compliant with local data protection regulations."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:14
msgid ""
"All pages using the :guilabel:`Form`, :guilabel:`Newsletter Block`, "
":guilabel:`Newsletter Popup` snippets, and the eCommerce :guilabel:`Extra "
"Step During Checkout` form are protected by both tools."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:19
msgid ""
"`Cloudflare Turnstile's documentation "
"<https://developers.cloudflare.com/turnstile/>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:20
msgid ""
"`Google's reCAPTCHA v3 guide "
"<https://developers.google.com/recaptcha/docs/v3>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:25
msgid "Cloudflare Turnstile configuration"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:28
msgid "On Cloudflare"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:30
msgid ""
"`Create <https://dash.cloudflare.com/sign-up>`_ a Cloudflare account or use "
"an existing one and `log in <https://dash.cloudflare.com/login>`_."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:32
msgid "On the dashboard navigation sidebar, click :guilabel:`Turnstile`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:33
msgid "On the :guilabel:`Turnstile Sites` page, click :guilabel:`Add Site`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:34
msgid "Add a :guilabel:`Site name` to identify it easily."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:35
msgid ""
"Enter or select the website's :guilabel:`Domain` (e.g., *example.com* or "
"*subdomain.example.com*)."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:36
msgid "Select a :guilabel:`Widget Mode`:"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:38
msgid ""
"The :guilabel:`Managed` mode is **recommended**, as visitors can be prompted"
" to check a box confirming they are human if deemed necessary by Turnstile."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:0
msgid "Cloudflare Turnstile human verification widget"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:44
msgid ""
"For the :guilabel:`Non-interactive` and :guilabel:`Invisible` modes, "
"visitors are never prompted to interact. In :guilabel:`Non-interactive` "
"mode, a loading widget can be displayed to warn visitors that Turnstile "
"protects the form; however, the widget is not supported by Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:49
msgid ""
"If the Turnstile check fails, visitors are not able to submit the form, and "
"the following error message is displayed:"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:0
msgid "Cloudflare Turnstile verification error message"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:55
#: ../../content/applications/websites/website/pages.rst:28
msgid "Click :guilabel:`Create`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst-1
msgid "Adding a website to Cloudflare Turnstile"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:60
msgid ""
"The generated keys are then displayed. Leave the page open for convenience, "
"as copying the keys in Odoo is required next."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:64
#: ../../content/applications/websites/website/configuration/spam_protection.rst:111
msgid "On Odoo"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:66
msgid ""
"From the database dashboard, click :guilabel:`Settings`. Under "
":guilabel:`Integrations`, enable :guilabel:`Cloudflare Turnstile` and click "
":guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:68
msgid ""
"Open the Cloudflare Turnstile page, copy the :guilabel:`Site Key`, and paste"
" it into the :guilabel:`CF Site Key` field in Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:70
msgid ""
"Open the Cloudflare Turnstile page, copy the :guilabel:`Secret Key`, and "
"paste it into the :guilabel:`CF Secret Key` field in Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:72
#: ../../content/applications/websites/website/configuration/spam_protection.rst:128
#: ../../content/applications/websites/website/configuration/translate.rst:61
msgid "Click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:75
msgid ""
"Navigate to Turnstile on your Cloudflare account to view the solve rates and"
" access more settings."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:81
msgid "reCAPTCHA v3 configuration"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:84
msgid ""
"reCAPTCHA v3 may not be compliant with local data protection regulations."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:87
msgid "On Google"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:89
msgid ""
"Open `the reCAPTCHA website registration page "
"<https://www.google.com/recaptcha/admin/create>`_. Log in or create a Google"
" account if necessary."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:92
msgid "On the website registration page:"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:94
msgid "Give the website a :guilabel:`Label`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:95
msgid "Leave the :guilabel:`reCAPTCHA type` on :guilabel:`Score based (v3)`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:96
msgid ""
"Enter one or more :guilabel:`Domains` (e.g., *example.com* or "
"*subdomain.example.com*)."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:97
msgid ""
"Under :guilabel:`Google Cloud Platform`, a project is automatically selected"
" if one was already created with the logged-in Google account. If not, one "
"is automatically created. Click :guilabel:`Google Cloud Platform` to select "
"a project yourself or rename the automatically created project."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:101
msgid "Agree to the terms of service."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:102
msgid "Click :guilabel:`Submit`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst-1
msgid "reCAPTCHA website registration example"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:107
msgid ""
"A new page with the generated keys is then displayed. Leave it open for "
"convenience, as copying the keys to Odoo is required next."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:113
msgid ""
"From the database dashboard, click :guilabel:`Settings`. Under "
":guilabel:`Integrations`, enable :guilabel:`reCAPTCHA` if needed."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:117
msgid ""
"Do not disable the :guilabel:`reCAPTCHA` feature or uninstall the "
":guilabel:`Google reCAPTCHA integration` module, as many other modules would"
" also be removed."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:120
msgid ""
"Open the Google reCAPTCHA page, copy the :guilabel:`Site key`, and paste it "
"into the :guilabel:`Site Key` field in Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:122
msgid ""
"Open the Google reCAPTCHA page, copy the :guilabel:`Secret key`, and paste "
"it into the :guilabel:`Secret Key` field in Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:124
msgid ""
"Change the default :guilabel:`Minimum score` (`0.70`) if necessary, using a "
"value between `1.00` and `0.00`. The higher the threshold is, the more "
"difficult it is to pass the reCAPTCHA, and vice versa.  Out of the 11 "
"levels, only the following four score levels are available by default: "
"`0.1`, `0.3`, `0.7` and `0.9`."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:131
msgid ""
"`Interpret reCAPTCHA scores - Google documentation "
"<https://cloud.google.com/recaptcha/docs/interpret-assessment-"
"website#interpret_scores>`_"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:133
msgid ""
"You can notify visitors that reCAPTCHA protects a form. To do so, open the "
"website editor and navigate to the form. Then, click somewhere on the form, "
"and on the right sidebar's :guilabel:`Customize` tab, toggle :guilabel:`Show"
" reCAPTCHA Policy` found under the :guilabel:`Form` section."
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst-1
msgid "reCAPTCHA policy message displayed on a form"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:142
msgid ""
"If the reCAPTCHA check fails, the following error message is displayed:"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:0
msgid "Google reCAPTCHA verification error message"
msgstr ""

#: ../../content/applications/websites/website/configuration/spam_protection.rst:148
msgid ""
"Analytics and additional settings are available on `Google's reCAPTCHA "
"administration page <https://www.google.com/recaptcha/admin/>`_. For "
"example, you can receive email alerts if Google detects suspicious traffic "
"on your website or view the percentage of suspicious requests, which could "
"help you determine the right minimum score."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:3
msgid "Translations"
msgstr "الترجمات"

#: ../../content/applications/websites/website/configuration/translate.rst:5
msgid ""
"Your website is displayed in the language that matches your visitor’s "
"browser. If the browser’s language has not been installed and added to your "
"website, the content is shown in the :ref:`default language "
"<translate/default-language>`. When additional languages are installed, "
"users can choose their preferred language using the :ref:`language selector "
"<translate/language-selector>`."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:10
msgid ""
"The :ref:`Translate <translate/translate>` feature on your website allows "
"automatic translation of standard terms and provides a tool for manual "
"content translation."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:14
msgid "Install languages"
msgstr "تثبيت اللغات "

#: ../../content/applications/websites/website/configuration/translate.rst:16
msgid ""
"To allow translation of your website, you must first :doc:`install "
"<../../../general/users/language>` the required languages and add them to "
"your website. To do so, go to :menuselection:`Website --> Configuration --> "
"Settings` and click :icon:`fa-arrow-right` :guilabel:`Install languages` in "
"the :guilabel:`Website Info` section. In the dialog box that opens, select "
"the :guilabel:`Languages` you want from the dropdown menu, tick the required"
" :guilabel:`Websites to translate`, and click :guilabel:`Add`."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:23
msgid ""
"To edit your website's languages, go to :menuselection:`Website -–> "
"Configuration -–> Settings` and add/remove the required languages in/from "
"the :guilabel:`Languages` field in the :guilabel:`Website info` section."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:28
msgid ""
"Alternatively, once the languages have been installed, you can add them from"
" the :ref:`language selector <translate/language-selector>`. You might then "
"need to refresh your page to see the new language."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:35
msgid "Default language"
msgstr "اللغة الافتراضية"

#: ../../content/applications/websites/website/configuration/translate.rst:37
msgid ""
"When multiple languages are available on your website, you can set a default"
" language to be used if the visitor’s browser language is not available. To "
"do so, go to :menuselection:`Website –-> Configuration -–> Settings`, and "
"select a language in the :guilabel:`Default` field."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:42
msgid ""
"This field is only visible if multiple languages have been installed and "
"added to your website."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:47
msgid "Language selector"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:49
msgid ""
"Your website’s visitors can switch languages using the language selector, "
"available by default in the :guilabel:`Copyright` section at the bottom of "
"the page. To edit the language selector menu:"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:52
msgid "Go to your website and click :guilabel:`Edit`;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:53
msgid ""
"Click the language selector available in the :guilabel:`Copyright` block and"
" go to the :guilabel:`Copyright` section of the website builder;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:55
msgid ""
"Set the :guilabel:`Language Selector` field to either :guilabel:`Dropdown` "
"or :guilabel:`Inline`. Click :guilabel:`None` if you do not want to display "
"the  :guilabel:`Language selector`;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:0
msgid "Add a language selector menu."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:64
msgid ""
"You can also add the :guilabel:`Language Selector` to the :guilabel:`Header`"
" of your page. To do so, click the :guilabel:`Header` block and go to the "
":guilabel:`Navbar` section to edit the :guilabel:`Language Selector`."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:71
msgid "Translate your website"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:73
msgid ""
"Select your desired language from the language selector to see your content "
"in another language. Then, click the :guilabel:`Translate` button in the "
"top-right corner to manually activate the translation mode so that you can "
"translate what has not been translated automatically by Odoo."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:77
msgid ""
"Translated text strings are highlighted in green; text strings that were not"
" translated automatically are highlighted in yellow."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst-1
msgid "Entering the translation mode"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:83
msgid ""
"In this mode, you can only translate text. To change the page's structure, "
"you must edit the master page, i.e., the page in the original language of "
"the database. Any changes made to the master page are automatically applied "
"to all translated versions."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:87
msgid ""
"To replace the original text with the translation, click the block, edit its"
" contents, and :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:91
msgid ""
"When a website supports multiple languages, the core URL structure remains "
"consistent across languages, while specific elements like product names or "
"categories are translated. For example, "
"`https://www.mywebsite.com/shop/product/my-product-1` is the English version"
" of a product page, while `https://www.mywebsite.com/fr/shop/product/mon-"
"produit-1` is the French version of the same page. The structure "
"(/shop/product/) stays unchanged, but the translated elements (e.g., product"
" name) adapt to the selected language."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:99
msgid ""
"Once the desired language is installed, you can translate some items from "
"the backend (e.g., the product's name in the product form). To do so, click "
"the language code (e.g., :guilabel:`EN`) next to the text you want to "
"translate and add the translation."
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:104
msgid "Content visibility by language"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:106
msgid ""
"You can hide content (such as images or videos, for example) depending on "
"the language. To do so:"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:108
msgid "Click :guilabel:`Edit` and select an element of your website;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:109
msgid "Go to the :guilabel:`Text - Image` section and :guilabel:`Visibility`;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:110
msgid ""
"Click :guilabel:`No condition` and select :guilabel:`Conditionally` instead;"
msgstr ""

#: ../../content/applications/websites/website/configuration/translate.rst:111
msgid ""
"Go to :guilabel:`Languages` to configure the condition(s) to apply by "
"selecting :guilabel:`Visible for` or :guilabel:`Hidden for`, and click "
":guilabel:`Choose a record` to decide which languages are impacted."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:3
msgid "Mail groups"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:5
msgid ""
"The **mail groups** feature allows website visitors to have a public "
"discussion by email. They can join a group to receive emails from other "
"group members (i.e., website users who have subscribed to the group) and "
"send new ones to all group members."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:9
msgid ""
"To activate the feature, :ref:`install <general/install>` the "
":guilabel:`Website Mail Group` (`website_mail_group`) module."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:13
msgid ""
"The **mail groups** feature is not to be confused with the "
":doc:`../../marketing/email_marketing/mailing_lists` in the Email Marketing "
"app."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:19
msgid "Configuring mail groups"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:21
msgid "To configure mail groups, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:23
msgid ""
"Configure a custom email alias domain by accessing the **General settings**,"
" scrolling down to the :guilabel:`Discuss` section, enabling the "
":guilabel:`Custom Email Server` feature, and entering the :guilabel:`Alias "
"domain` (e.g., `@mycompany.com`)."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:26
msgid ""
"Go to :menuselection:`Website --> Configuration --> Mailing Lists`, then "
"click :guilabel:`New`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:27
msgid ""
"Specify a :guilabel:`Group Name`, the :guilabel:`Email Alias`, and a "
":guilabel:`Description`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:28
msgid ""
"Enable :guilabel:`Moderate this group` and specify the "
":guilabel:`Moderators` if you wish to :ref:`moderate messages "
"<website/mailing_lists/moderate>` from this group. Alternatively, if the "
"group is not moderated, you can define :guilabel:`Responsible Users` who can"
" manage the messages in the group."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:32
msgid ""
"In the :guilabel:`Privacy` tab, define who can subscribe to the mail group:"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:34
msgid ""
":guilabel:`Everyone`: to make the mail group public so anyone can subscribe "
"to it;"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:35
msgid ""
":guilabel:`Members only`: to only allow users defined as members to "
"subscribe to the mail group;"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:36
msgid ""
":guilabel:`Selected group of users`: to only allow users from the "
":guilabel:`Authorized group` to subscribe to the mail group."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:39
msgid ""
"If the mail group is moderated, you can automatically notify authors when "
"their message is pending moderation by enabling :guilabel:`Automatic "
"notification` in the :guilabel:`Notify Members` tab and writing the "
":guilabel:`Notification message`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:42
msgid ""
"If you wish to send out guidelines to new subscribers, enable "
":guilabel:`Send guidelines to new subscribers` and write them in the "
":guilabel:`Guidelines` tab. This is particularly useful when the mail group "
"is moderated."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:47
msgid "Using mail groups"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:50
msgid "Subscribing/unsubscribing"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:52
msgid ""
"Based on the :ref:`configuration of the mail group "
"<website/mailing_lists/configure_groups>`, users can subscribe to and "
"unsubscribe from mail groups from the website page (`/groups` by default)."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst-1
msgid "Mail group web page."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:58
msgid ""
"Internal users can also do this from :menuselection:`Website --> "
"Configuration --> Mailing Lists`, using the :guilabel:`Join` and "
":guilabel:`Leave` buttons."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:62
msgid "Sending messages"
msgstr "جاري إرسال الرسائل "

#: ../../content/applications/websites/website/mail_groups.rst:64
msgid ""
"To send messages to a mail group, website users can email the :ref:`mail "
"group's email address <website/mailing_lists/configure_groups>`. Internal "
"users can also create messages directly from Odoo. To do so, go to "
":menuselection:`Website --> Configuration --> Mailing Lists`, select the "
"mail group, click the :guilabel:`Emails` smart button, and click "
":guilabel:`New`. Then, fill in the fields and click :guilabel:`Send`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:71
msgid ""
"The list of messages can also be accessed by selecting the group from the "
"`/groups` website page."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:73
msgid ""
"Group members can also unsubscribe from the group, access the mail group "
"page, and send emails to the group using the URLs in the footer of any group"
" email they have received."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:0
msgid "URLs in the footer of a group email."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:82
msgid "Moderating mail group messages"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:84
msgid ""
"If the :guilabel:`Moderate this group` feature has been enabled for the "
":ref:`mail group <website/mailing_lists/configure_groups>`, one of the "
":guilabel:`Moderators` must approve the group's messages before they are "
"dispatched to the other members."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:88
msgid ""
"To moderate messages, go to :menuselection:`Website --> Configuration --> "
"Mailing Lists`, select the mail group, and click the :guilabel:`To review` "
"smart button. You can moderate messages using the buttons at the end of the "
"message line or select a message to view its content and moderate it "
"accordingly."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:0
msgid "Moderation buttons in the message line."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:96
msgid "The following actions are available:"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:98
msgid ""
":guilabel:`Accept`: to accept the email and send it to the mail group "
"members."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:99
msgid ""
":guilabel:`Reject`: to reject the email. In the pop-up window that opens, "
"click :guilabel:`Reject Silently` to reject the email without notifying the "
"author, or specify an explanation for rejecting the message, then click "
":guilabel:`Send & Reject` to reject the message and send the explanation to "
"the author."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:103
msgid ""
":guilabel:`Whitelist`: to whitelist the author, i.e. automatically accept "
"all of their emails. As a result, a :ref:`moderation rule "
"<website/mailing_lists/moderate>` is created for the author's email address "
"with the status :guilabel:`Always allow`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:106
msgid ""
":guilabel:`Ban`: to blacklist the author, i.e. automatically discard all "
"their emails. In the pop-up window that opens, click :guilabel:`Ban` to ban "
"the author without notifying them, or specify an explanation, then click "
":guilabel:`Send & Ban` to ban the author and send them the explanation. As a"
" result, a :ref:`moderation rule <website/mailing_lists/moderate>` is "
"created for the author's email address with the status :guilabel:`Permanent "
"ban`."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:113
msgid ""
"Messages can also be moderated from the group's list of messages. Go to "
":menuselection:`Website --> Groups --> Mailing List Groups`, select the mail"
" group and click the :guilabel:`Emails` smart button."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:120
msgid "Whitelisting/Blacklisting authors"
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:122
msgid ""
"You can whitelist or blacklist an author either directly :ref:`from a mail "
"group message <website/mailing_lists/moderate>`, or by creating a moderation"
" rule. To do so, go to :menuselection:`Website --> Configuration --> "
"Moderation Rules` and click :guilabel:`New`. Then, select the "
":guilabel:`Group`, specify the author's :guilabel:`Email` and set the "
":guilabel:`Status` field."
msgstr ""

#: ../../content/applications/websites/website/mail_groups.rst:129
msgid ""
"You can also access the mail group's moderation rules by going to "
":menuselection:`Website --> Configuration --> Mailing Lists`, selecting the "
"group, then clicking the :guilabel:`Moderations` smart button."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:5
msgid "Pages"
msgstr "الصفحات "

#: ../../content/applications/websites/website/pages.rst:7
msgid ""
"Odoo allows you to create pages for your website and customize their content"
" and appearance to your needs."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:12
msgid ""
"**Static** pages have stable content, such as the homepage. You can manually"
" create new ones, define their URLs, adapt their :ref:`properties "
"<website/page_properties>`, etc. **Dynamic** pages, on the other hand, are "
"generated dynamically. All pages generated automatically by Odoo, for "
"example, when you install an app or module (e.g., `/shop` or `/blog`) or "
"publish a new product or blog post, are dynamic pages and are therefore "
"managed differently."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:19
msgid "Page creation"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:21
msgid ""
"Website pages can be created from the **frontend** and the **backend**. To "
"create a new website page, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:24
msgid ""
"Either open the **Website** app, click :guilabel:`+ New` in the top-right "
"corner, then select :guilabel:`Page`;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:26
msgid ""
"Or go to :menuselection:`Website --> Site --> Pages` and click "
":guilabel:`New`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:27
msgid ""
"Enter a :guilabel:`Page Title`; this title is used in the menu and the "
"page's URL."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:29
msgid ""
"Customize the page's content and appearance using the website builder, then "
"click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:31
msgid ":ref:`Publish <website/un-publish-page>` the page."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:34
msgid ""
"Disable :guilabel:`Add to menu` if the page should not appear in the menu."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:37
msgid "Page management"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:42
msgid "Publishing/unpublishing pages"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:44
msgid ""
"Pages need to be published to make them accessible to website visitors. To "
"publish or unpublish a page, access it and toggle the switch in the upper-"
"right corner from :guilabel:`Unpublished` to :guilabel:`Published`, or vice "
"versa."
msgstr ""

#: ../../content/applications/websites/website/pages.rst-1
msgid "Unpublished/Published toggle"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:52
msgid "It is also possible to:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:54
msgid ""
"publish/unpublish a page from the :ref:`page properties "
"<website/page_properties>`, where you can define a publishing date and/or "
"restrict the page's visibility if needed;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:56
msgid ""
"publish/unpublish several pages at once: go to :menuselection:`Website --> "
"Site --> Pages`, select the pages, then click :guilabel:`Action` and select "
":guilabel:`Publish` or :guilabel:`Unpublish`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:61
msgid "Homepage"
msgstr "الصفحة الرئيسية"

#: ../../content/applications/websites/website/pages.rst:63
msgid ""
"When you create a website, Odoo creates a dedicated :guilabel:`Home` page by"
" default, but you can define any website page as your homepage. To do so, go"
" to :menuselection:`Website --> Configuration --> Settings`, then, in the "
":guilabel:`Website info` section, define the URL of the desired page in the "
"field :guilabel:`Homepage URL` (e.g., `/shop`)."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:68
msgid ""
"Alternatively, you can define any :ref:`static page <website/page_type>` as "
"your homepage by going to :menuselection:`Website --> Site --> Properties`. "
"Select the :guilabel:`Publish` tab and enable :guilabel:`Use as Homepage`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:75
msgid "Page properties"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:77
msgid ""
"To modify a :ref:`static page's <website/page_type>` properties, access the "
"page you wish to modify, then go to :menuselection:`Site --> Properties`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:80
msgid "The :guilabel:`Name` tab allows you to:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:82
msgid "rename the page using the :guilabel:`Page Name` field;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:83
msgid ""
"modify the :guilabel:`Page URL`. In this case, you can redirect the old URL "
"to the new one if needed. To do so, enable :guilabel:`Redirect Old URL`, "
"then select the :guilabel:`Type` of :ref:`redirection <website/URL-"
"redirection>`:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:87
msgid ":guilabel:`301 Moved permanently`: to redirect the page permanently;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:88
msgid ":guilabel:`302 Moved temporarily`: to redirect the page temporarily."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:0
msgid "Redirect old URL"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:93
msgid ""
"You can further adapt the page's properties in the :guilabel:`Publish` tab:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:95
msgid ""
":guilabel:`Show in Top Menu`: Disable if you don't want the page to appear "
"in the menu;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:96
msgid ""
":guilabel:`Use as Homepage`: Enable if you want the page to be the homepage "
"of your website;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:97
msgid ""
":guilabel:`Indexed`: Disable if you don't want the page to be shown in "
"search engine results;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:98
msgid ":guilabel:`Published`: Enable to publish the page;"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:99
msgid ""
":guilabel:`Publishing Date`: To publish the page at a specific moment, "
"select the date, click the clock icon to set the time, then click the green "
"check mark to validate your selection."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:101
msgid ":guilabel:`Visibility`: Select who can access the page:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:103
msgid ":guilabel:`All`"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:104
msgid ":guilabel:`Signed In`"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:105
msgid ""
":guilabel:`Restricted Group`: Select the :doc:`user access group(s) "
"</applications/general/users/access_rights>` in the :guilabel:`Authorized "
"group` field."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:107
msgid ""
":guilabel:`With Password`: Enter the password in the :guilabel:`Password` "
"field."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:110
msgid ""
"*Some* of these properties can also be modified from :menuselection:`Website"
" --> Site --> Pages`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:113
msgid "Duplicating pages"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:115
msgid ""
"To duplicate a page, access the page, then go to :menuselection:`Site --> "
"Properties` and click :guilabel:`Duplicate Page`. Enter a :guilabel:`Page "
"Name`, then click :guilabel:`OK`. By default, the new page is added after "
"the duplicated page in the menu, but you can remove it from the menu or "
"change its position using the :doc:`menu editor <pages/menus>`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:123
msgid "Deleting pages"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:125
msgid "To delete a page, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:127
msgid ""
"Access the page, then go to :menuselection:`Site --> Properties` and click "
":guilabel:`Delete Page`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:129
msgid ""
"A pop-up window appears on the screen with all links referring to the page "
"you want to delete, organized by category. To ensure website visitors don't "
"land on a 404 error page, you must update all the links on your website "
"referring to the page. To do so, expand a category, then click on a link to "
"open it in a new window. Alternatively, you can set up a :ref:`redirection "
"<website/URL-redirection>` for the deleted page."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:134
msgid ""
"Once you have updated the links (or set up a :ref:`redirection <website/URL-"
"redirection>`), select the :guilabel:`I am sure about this` check box, then "
"click :guilabel:`OK`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:140
msgid "URL redirect mapping"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:142
msgid ""
"URL redirect mapping consists in sending visitors and search engines to a "
"URL different from the one they initially requested. This technique is used,"
" for example, to prevent broken links when you :ref:`delete a page "
"<website/delete-page>`, :ref:`modify its URL <website/page_properties>`, or "
"migrate your site from another platform to an Odoo :doc:`domain "
"<configuration/domain_names>`. It can also be used to improve "
":doc:`pages/seo`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:148
msgid ""
"To access existing URL redirections and create new ones, :doc:`activate the "
"developer mode </applications/general/developer_mode>` and go to "
":menuselection:`Website --> Configuration --> Redirects`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:153
msgid ""
"A redirect record is added automatically every time you :ref:`modify a "
"page's URL <website/page_properties>` and enable :guilabel:`Redirect Old "
"URL`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:155
msgid ""
"You can set up redirections for :ref:`static and dynamic pages "
"<website/page_type>`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:157
msgid ""
"To create a new redirection, click the :guilabel:`New` button, then fill in "
"the fields:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:159
msgid ":guilabel:`Name`: Enter a name to identify the redirect."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:160
msgid ":guilabel:`Action`: Select the type of redirection:"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:162
msgid ""
":guilabel:`404 Not found`: visitors are redirected to a 404 error page when "
"they try to access an unpublished or deleted page."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:164
msgid ""
":guilabel:`301 Moved Permanently`: for permanent redirections of unpublished"
" or deleted :ref:`static pages <website/page_type>`. The new URL is shown in"
" search engine results, and the redirect is cached by browsers."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:167
msgid ""
":guilabel:`302 Moved Temporarily`: for short-term redirections, for example,"
" if you are redesigning or updating a page. The new URL is neither cached by"
" browsers nor shown in search engine results."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:170
msgid ""
":guilabel:`308 Redirect/Rewrite`: for permanent redirections of existing "
":ref:`dynamic pages <website/page_type>`. The URL is renamed; the new name "
"is shown in search engine results and is cached by browsers. Use this "
"redirect type to rename a dynamic page, for example, if you wish to rename "
"`/shop` into `/market`."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:175
msgid ""
":guilabel:`URL from`: Enter the URL to be redirected (e.g., `/about-the-"
"company`) or search for the desired :ref:`dynamic page <website/page_type>` "
"and select it from the list."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:177
msgid ""
":guilabel:`URL to`: For 301, 302, and 308 redirects, enter the URL to be "
"redirected to. If you want to redirect to an external URL, include the "
"protocol (e.g., `https://`)."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:179
msgid ":guilabel:`Website`: Select a specific website."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:180
msgid ""
":guilabel:`Sequence`: To define the order in which redirections are "
"performed, e.g., in the case of redirect chains (i.e., a series of redirects"
" where one URL is redirected to another one, which is itself further "
"redirected to another URL)."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:184
msgid "Toggle the :guilabel:`Activate` switch to deactivate the redirection."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:187
msgid ""
"404, 301, and 302 redirections are meant to migrate traffic from "
":ref:`unpublished <website/un-publish-page>` or :ref:`deleted "
"<website/delete-page>` pages to *new* pages, while the 308 redirect is used "
"for *permanent* redirections of *existing* pages."
msgstr ""

#: ../../content/applications/websites/website/pages.rst:192
msgid ""
"`Google documentation on redirects and search "
"<https://developers.google.com/search/docs/crawling-"
"indexing/301-redirects>`_"
msgstr ""

#: ../../content/applications/websites/website/pages.rst:193
msgid ":doc:`pages/seo`"
msgstr ":doc:`pages/seo`"

#: ../../content/applications/websites/website/pages/menus.rst:3
msgid "Menus"
msgstr "القوائم"

#: ../../content/applications/websites/website/pages/menus.rst:5
msgid ""
"Menus are used to organize your website’s content and help visitors navigate"
" through your web pages effectively. User-friendly and well-structured "
"website menus also play a crucial role in improving :doc:`search engine "
"rankings <seo>`."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:9
msgid ""
"Odoo allows you to customize the content and appearance of your website's "
"menu to your needs."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:12
msgid "Menu editor"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:14
msgid ""
"The menu editor allows you to edit your website's menu and add :ref:`regular"
" menu items <website/regular-menus>` and :ref:`mega menus <website/mega-"
"menus>`."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:17
msgid ""
"To edit your website's menu, go to :menuselection:`Website --> Site --> Menu"
" Editor`. From there, you can:"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:20
msgid ""
"**rename** a menu item or change its URL using the :guilabel:`Edit Menu "
"Item` icon;"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:21
msgid "**delete** a menu item using the :guilabel:`Delete Menu Item` icon;"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:22
msgid ""
"**move** a menu item by dragging and dropping it to the desired place in the"
" menu;"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:23
msgid ""
"**create a regular drop-down menu** by dragging and dropping the sub-menu "
"items to the right, underneath their parent menu."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst-1
msgid "Menu editor with sub-menus"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:31
msgid ""
"You can also access the menu editor by clicking :guilabel:`Edit`, selecting "
"any menu item and clicking the :guilabel:`Edit Menu` icon."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:0
msgid "Access the Menu editor while in Edit mode."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:40
msgid "Adding regular menu items"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:42
msgid ""
"By default, pages are added to the menu as regular menu items when "
":doc:`they are created <../pages>`. You can also add regular menu items from"
" the menu editor by clicking :guilabel:`Add Menu Item`. Enter the "
":guilabel:`Name` and URL of the related page in the pop-up window that "
"appears on the screen and click :guilabel:`OK`."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:48
msgid ""
"In the :guilabel:`URL or Email` field, you can type `/` to search for a page"
" on your website or `#` to search for an existing custom anchor."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:54
msgid "Adding mega menus"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:56
msgid ""
"Mega menus are similar to drop-down menus, but instead of a simple list of "
"sub-menus, they display a panel divided into groups of navigation options. "
"This makes them suitable for websites with large amounts of content, as they"
" can help include all of your web pages in the menu while still making all "
"menu items visible at once. Mega menus can also be structured more visually "
"than regular drop-down menus, for example, through layout, typography, and "
"icons."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst-1
msgid "Mega menu in the navigation bar."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:65
msgid ""
"To create a mega menu, go to :menuselection:`Website --> Site --> Menu "
"Editor` and click :guilabel:`Add Mega Menu Item`. Enter the :guilabel:`Name`"
" of the mega menu in the pop-up, click :guilabel:`OK`, then "
":guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:69
msgid ""
"To adapt the options and layout of the mega menu, click it in the navigation"
" bar, then click :guilabel:`Edit`. Mega menus are composed of building "
"blocks, which means you can customize each component individually using "
"inline formatting, as well as the options available in the "
":guilabel:`Customize` tab in the website builder. For example, you can:"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:74
msgid "edit the text directly in the building block;"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:75
msgid ""
"edit a menu item's URL by selecting the menu item, then clicking the "
":guilabel:`Edit link` button in the small preview pop-up. Type `/` to search"
" for a page on your website, or `#` to search for an existing custom anchor."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:0
msgid "Edit a mega menu option."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:82
msgid ""
"move a menu item by dragging and dropping the related block to the desired "
"position in the mega menu;"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:84
msgid "delete a menu item by deleting the related block."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:87
msgid ""
"You can adapt the general layout of the mega menu by selecting the desired "
":guilabel:`Template` and :guilabel:`Size` in the :guilabel:`Mega menu` "
"section in the :guilabel:`Customize` tab in the website builder."
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:92
msgid "Header and navigation bar appearance"
msgstr ""

#: ../../content/applications/websites/website/pages/menus.rst:94
msgid ""
"To customize the appearance of your website's menu, click :guilabel:`Edit`, "
"then select the navigation bar or any menu item. You can then adapt the "
"fields in the :guilabel:`Header` and :guilabel:`Navbar` sections in the "
":guilabel:`Customize` tab in the website builder."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:3
msgid "Search Engine Optimization (SEO)"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:5
msgid ""
"Search Engine Optimization, often abbreviated as SEO, is a digital marketing"
" strategy to improve a website's visibility and ranking in search engine "
"results (e.g., in Google). It involves optimizing various elements on your "
"website, including its content, social sharing, URLs, images, and page "
"speed."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:11
msgid ""
"Odoo provides several modules to help you build your website content, such "
"as :doc:`eCommerce <../../ecommerce>`, :doc:`Blog <../../blog>`, "
":doc:`eLearning <../../elearning>`, and :doc:`Forum <../../forum>`."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:14
msgid ""
"All Odoo :doc:`themes <../web_design/themes>` rely on the CSS Framework "
"`Bootstrap <https://getbootstrap.com/>`_ to render efficiently according to "
"the device: desktop, tablet, or mobile, which positively impacts ranking in "
"search engines."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:19
msgid "Content optimization"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:21
msgid ""
"To optimize a webpage's SEO, access the page, then go to "
":menuselection:`Website --> Site --> Optimize SEO`."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst-1
msgid "Optimize SEO"
msgstr "تحسين محركات البحث"

#: ../../content/applications/websites/website/pages/seo.rst:28
msgid "Meta tags"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:30
msgid ""
"Meta tags are HTML elements that provide information about a webpage to "
"search engines and website visitors. They play a crucial role in SEO by "
"helping search engines understand the content and context of a webpage and "
"attract visitors with appealing content. There are two types of meta tags in"
" Odoo:"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:35
msgid ""
":guilabel:`Title` tags specify a webpage's title and are displayed as a "
"clickable link in search engine results. They should be concise, "
"descriptive, and relevant to the page's content. You can update the title "
"tag of your webpage or keep it empty to use the default value based on the "
"page’s content."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:40
msgid ""
":guilabel:`Description` tags summarize the webpage's content, often "
"displayed in search engine results below the title. They are used to "
"encourage the user to visit the page. You can update the description tag of "
"your webpage or keep it empty to use the default value based on the page’s "
"content."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:46
msgid ""
"The :guilabel:`Preview` card displays how the title and description tags "
"should appear in search results. It also includes the URL of your page."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:50
msgid "Keywords"
msgstr "كلمات مفتاحية "

#: ../../content/applications/websites/website/pages/seo.rst:52
msgid ""
"Keywords are one of the main elements of SEO. A website that is well "
"optimized for search engines speaks the same language as potential visitors,"
" with keywords for SEO helping them to connect to your site."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:56
msgid ""
"You can enter the keywords you consider essential in the :guilabel:`Keyword`"
" field and click :guilabel:`ADD` to see how they are used at different "
"levels in your content (H1, H2, page title, page description, page content) "
"and the related searches in Google. The tool also suggests relevant keywords"
" to drive your web traffic. The more keywords are present on your webpage, "
"the better."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:62
msgid "It is strongly recommended to only use one H1 title per page for SEO."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:65
msgid "Image for social share"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:67
msgid ""
"When you share your page on social media, your logo image is selected, but "
"you can upload any other image by clicking the upward arrow."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:71
msgid ""
"The :guilabel:`Social Preview` card displays how the page's information "
"would appear when shared."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:73
msgid ""
"If you change the title of a blog post or the name of a product, the changes"
" apply automatically everywhere on your website. The old link still "
"functions when external websites use a :ref:`301 redirect <website/URL-"
"redirection>`, maintaining the SEO link juice."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:78
#: ../../content/applications/websites/website/web_design/elements.rst:64
msgid "Images"
msgstr "الصور "

#: ../../content/applications/websites/website/pages/seo.rst:80
msgid ""
"The size of images has a significant impact on page speed, which is an "
"essential criterion for search engines to optimize SEO ranking."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:84
msgid ""
"Compare how your website ranks using `Google Page Speed "
"<https://pagespeed.web.dev/?utm_source=psi&utm_medium=redirect>`_ or "
"`Pingdom Website Speed Test <https://tools.pingdom.com/>`_."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:87
msgid ""
"Odoo automatically compresses uploaded images and converts them to `Webp`. "
"With this file format, photos are smaller, which increases the page loading "
"speed and, therefore, gives a better ranking in SEO. All images used in Odoo"
" official :doc:`themes <../web_design/themes>` are also compressed by "
"default. If you are using a third-party theme, it may provide images that "
"are not compressed efficiently."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:93
msgid ""
"**To modify an image** from your website, select the image, click "
":guilabel:`Edit`, then go to the :guilabel:`Customize` tab, and adapt the "
":guilabel:`Format` in the :guilabel:`Image` section."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst-1
msgid "automated image compression"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:100
msgid ""
"Alt tags are used to provide context to what an image is displaying, "
"informing search engine crawlers and allowing them to index an image "
"correctly. Adding alt tags keywords in the :guilabel:`Description` field is "
"essential from an SEO perspective. This description is added to the HTML "
"code of your image, and it is shown when the image cannot be displayed."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:106
msgid "Advanced features"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:109
msgid "Structured data markup"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:111
msgid ""
"Structured data markup is used to generate rich snippets in search engine "
"results. It is a way for websites to send structured data to search engine "
"robots, helping them understand your content and create well-presented "
"search results."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:115
msgid ""
"By default, Google supports many `rich snippets "
"<https://developers.google.com/search/blog/2009/05/introducing-rich-"
"snippets>`_ for content types, including Reviews, People, Products, "
"Businesses, Events, and Organizations."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:118
msgid ""
"Microdata is a set of tags, introduced with HTML5, that help search engines "
"better understand your content and display it in a relevant way. Odoo "
"implements microdata as defined in the schema.org `specification "
"<https://schema.org/docs/gs.html>`_ for events, eCommerce products, forum "
"posts, and contact addresses. This allows your product pages to be displayed"
" in Google using extra information like the price and rating of a product:"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst-1
msgid "snippets in search engine results"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:128
msgid "robots.txt"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:130
msgid ""
"A robots.txt file tells search engine crawlers which URLs the crawler can "
"access on your site, to index its content. This is used mainly to avoid "
"overloading your site with requests."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:133
msgid ""
"When indexing your website, search engines take a first look at the "
"robots.txt file. Odoo automatically creates one robot.txt file available on "
"`mydatabase.odoo.com/robots.txt`."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:136
msgid ""
"By editing a robots.txt file, you can control which site pages are "
"accessible to search engine crawlers. To add custom instructions to the "
"file, go to :menuselection:`Website --> Configuration --> Settings`, scroll "
"down to the :guilabel:`SEO` section, and click :guilabel:`Edit robots.txt`."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:141
msgid ""
"If you do not want the robots to crawl the `/about-us` page of your site, "
"you can edit the robots.txt file to add `Disallow: /about-us`."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:145
msgid "Sitemap"
msgstr "خريطة الموقع "

#: ../../content/applications/websites/website/pages/seo.rst:147
msgid ""
"The sitemap points out website pages and their relation to each other to "
"search engine robots. Odoo generates a `/sitemap.xml` file, including all "
"URLs. For performance reasons, this file is cached and updated every 12 "
"hours."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:152
msgid ""
"If your website has a lot of pages, Odoo automatically creates a Sitemap "
"Index file, respecting the `sitemaps.org protocol "
"<http://www.sitemaps.org/protocol.html>`_, grouping sitemap URLs in 45000 "
"chunks per file."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:156
msgid ""
"Every sitemap entry has three attributes that are computed automatically:"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:158
msgid "`<loc>`: the URL of a page."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:159
msgid ""
"`<lastmod>`: last modification date of the resource, computed automatically "
"based on the related object. For a page related to a product, this could be "
"the last modification date of the product or the page."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:162
msgid ""
"`<priority>`: modules may implement their priority algorithm based on their "
"content (for example, a forum might assign a priority based on the number of"
" votes on a specific post). The priority of a static page is defined by its "
"priority field, which is normalized (16 is the default)."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:167
msgid ""
"To prevent pages from appearing in a sitemap, go to :menuselection:`Site -->"
" Properties`, click the :guilabel:`Publish` tab, and turn off the "
":guilabel:`Indexed` feature."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:0
msgid "disabling the “Indexed” checkbox"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:174
msgid "Hreflang HTML tags"
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:176
msgid ""
"Odoo automatically includes `hreflang` and `x-default` tags in the code of "
"your website's multilingual pages. These HTML attributes are crucial in "
"informing search engines about a specific page's language and geographical "
"targeting."
msgstr ""

#: ../../content/applications/websites/website/pages/seo.rst:181
msgid ":doc:`../configuration/translate`"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:3
msgid "Website analytics"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:5
msgid ""
"Website analytics helps website owners monitor how people use their site. It"
" provides data on visitor demographics, behavior, and interactions, helping "
"improve websites and marketing strategies."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:8
msgid ""
"You can track your Odoo website's traffic using :ref:`analytics/plausible` "
"or :ref:`analytics/google-analytics`. We recommend using Plausible.io as it "
"is privacy-friendly, lightweight, and easy to use."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:12
msgid ""
"The Plausible Analytics dashboard is also integrated into Odoo and can be "
"accessed via :menuselection:`Website --> Reporting --> Analytics`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:18
msgid "Plausible.io"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:20
msgid ""
"Odoo hosts its own Plausible.io server and provides a free and ready-to-use "
"Plausible.io solution for **Odoo Online** databases that use the odoo.com "
"domain. Odoo automatically creates and sets up your account. Start using it "
"by going to :menuselection:`Website --> Reporting --> Analytics`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:25
msgid ""
"If you use a custom :doc:`domain name <../configuration/domain_names>` "
"(e.g., `example.com`), you need to create your own Plausible.io account and "
"subscription."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:27
msgid ""
"**If you already have a Plausible.io account** and you want to connect it to"
" your Odoo Online database, you must create two `ir.config.parameters` to "
"use Plausible.io's servers. To do so, enable the :ref:`developer mode "
"<developer-mode>` and go to :menuselection:`General Settings --> Technical "
"--> System Parameters`. Click :guilabel:`New` and fill in the following "
":guilabel:`Key` and :guilabel:`Value` fields:"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:36
msgid "Key"
msgstr "المفتاح"

#: ../../content/applications/websites/website/reporting/analytics.rst:37
msgid "Value"
msgstr "القيمة"

#: ../../content/applications/websites/website/reporting/analytics.rst:38
msgid "`website.plausible_script`"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:39
msgid "`https://plausible.io/js/plausible.js`"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:40
msgid "`website.plausible_server`"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:41
msgid "`https://plausible.io`"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:43
msgid ""
"Then, follow the steps below to connect your existing account with "
"Plausible.io servers."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:45
msgid ""
"If your database is hosted on **Odoo.sh** or **On-premise**, or if you wish "
"to use your own Plausible.io account, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:48
msgid ""
"Create or sign in to a Plausible.io account using the following link: "
"`<https://plausible.io/register>`_."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:49
msgid ""
"If you are creating a new account, go through the registration and "
"activation steps. On the :guilabel:`Add website info` page, add the "
":guilabel:`Domain` of your website without including `www` (e.g., "
"`example.odoo.com`) and change the :guilabel:`Reporting Timezone`, if "
"necessary. Click :guilabel:`Install Plausible` to proceed to the next step. "
"Ignore the :guilabel:`Manual installation` instructions and click "
":guilabel:`Start collecting data`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:54
msgid ""
"Once done, click the Plausible.io logo in the upper-left part of the page to"
" access your `list of websites <https://plausible.io/sites>`_, then click "
"the :icon:`fa-ellipsis-v` (:guilabel:`ellipsis`) icon next to the website "
"and select :icon:`fa-cog` :guilabel:`Settings` from the drop-down menu."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:0
msgid "Click the gear icon in the list of websites."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:61
msgid ""
"In the sidebar, select :guilabel:`Visibility`, then click :guilabel:`Add "
"Shared link`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:62
msgid ""
"Enter a :guilabel:`Name`, leave the :guilabel:`Password (optional)` field "
"empty, as the Plausible analytics dashboard integration in Odoo does not "
"support it, then click :guilabel:`Create shared link`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:65
msgid "Copy the shared link."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:0
msgid "Copy the shared link URL from Plausible.io"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:70
#: ../../content/applications/websites/website/reporting/analytics.rst:114
msgid ""
"In Odoo, go to :menuselection:`Website --> Configuration --> Settings`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:71
msgid ""
"In the :guilabel:`SEO` section, enable :guilabel:`Plausible Analytics`, then"
" paste the :guilabel:`Shared Link Auth` and click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:75
msgid ""
"If you have :doc:`multiple websites <../configuration/multi_website>`, add "
"your websites to your Plausible.io account by going to "
"`<https://plausible.io/sites>`_ and clicking :guilabel:`+ Add Website`. In "
"Odoo, in the **Website settings**, make sure to select the website in the "
":guilabel:`Settings of Website` field at the top of the page before pasting "
"the :guilabel:`Shared link`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:81
msgid ""
"Odoo automatically pushes two custom goals: `Lead Generation` and `Shop`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:84
msgid "`Plausible Analytics documentation <https://plausible.io/docs>`_"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:89
msgid "Google Analytics"
msgstr "تحليلات Google "

#: ../../content/applications/websites/website/reporting/analytics.rst:91
msgid "To follow your Odoo website's traffic with Google Analytics:"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:93
msgid ""
"Create or sign in to a Google account using the following link: "
"`<https://analytics.google.com>`_."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:94
msgid ""
"If you are setting up Google Analytics for the first time, click "
":guilabel:`Start measuring` and go through the account creation step."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:96
msgid ""
"If you already have a Google Analytics account, sign in and click the "
":icon:`fa-cog` icon in the bottom-left corner of the page to access the "
"**Admin** page. Then, click :guilabel:`+ Create` and select "
":guilabel:`Property` from the drop-down menu."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:100
msgid ""
"Complete the next steps: `property creation "
"<https://support.google.com/analytics/answer/9304153?hl=en/&visit_id=638278591144564289-**********&rd=2#property>`_,"
" business details and business objectives."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:102
msgid ""
"When you reach the **Data collection** step, choose the :guilabel:`Web` "
"platform."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:0
msgid "Choose a platform for your Google Analytics property."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:107
msgid ""
"Set up your data stream: Specify your :guilabel:`Website URL` and a "
":guilabel:`Stream name`, then click :guilabel:`Create & continue`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:109
msgid "Copy the :guilabel:`Measurement ID`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:0
msgid "Measurement ID in Google Analytics."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:115
msgid ""
"In the :guilabel:`SEO` section, enable :guilabel:`Google Analytics`, then "
"paste the :guilabel:`Measurement ID` and click :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:119
msgid ""
"If you have :doc:`multiple websites <../configuration/multi_website>` with "
"separate domains, it is recommended to create `one property "
"<https://support.google.com/analytics/answer/9304153?hl=en/&visit_id=638278591144564289-**********&rd=2#property>`_"
" per domain. In Odoo, in the **Website settings**, make sure to select the "
"website in the :guilabel:`Settings of Website` field at the top of the page "
"before pasting the :guilabel:`Measurement ID`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:125
msgid ""
"`Google documentation on setting up Analytics for a website "
"<https://support.google.com/analytics/answer/1008015?hl=en/>`_"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:131
msgid "Google Tag Manager"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:133
msgid ""
"Google Tag Manager is a tag management system that allows you to easily "
"update measurement codes and related code fragments, collectively known as "
"tags on your website or mobile app, directly through the code injector."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:138
msgid ""
":abbr:`GTM (Google Tag Manager)` is not an analytics tool and does not offer"
" reporting features; it is used to collect data and works alongside Google "
"Analytics to provide more detailed insights. In order to use GTM properly, "
"it is recommended to configure Google Analytics as well."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:142
msgid ""
"For more information refer to the `documentation on linking Google Analytics"
" and Google Tag Manager "
"<https://support.google.com/tagmanager/answer/9442095?hl=en>`_."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:146
msgid ""
"Some GTM tags use data layers (e.g., advanced eCommerce tracking data "
"layers) to retrieve variables and send them to Google Analytics. Data layers"
" are currently not managed in Odoo."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:148
msgid ""
"Google Tag Manager may not be compliant with local data protection "
"regulations."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:150
msgid "To configure GTM, proceed as follows:"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:152
msgid ""
"Create or sign in to a Google account by going to "
"https://tagmanager.google.com/."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:154
msgid "In the :guilabel:`Accounts` tab, click :guilabel:`Create Account`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:156
msgid ""
"Enter an :guilabel:`Account Name` and select the account's "
":guilabel:`Country`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:158
msgid ""
"Enter your website's URL in the :guilabel:`Container name` field and select "
"the :guilabel:`Target platform`."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:161
msgid "Click :guilabel:`Create` and agree to the Terms of Service."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:163
msgid ""
"Copy the `<head>` and `<body>` codes from the popup window. Then, go to your"
" website, click :guilabel:`Edit`, go to the :guilabel:`Theme` tab, scroll "
"down to the :guilabel:`Advanced` section, then click :guilabel:`<head>` and "
":guilabel:`</body>` next to :guilabel:`Code Injection` to paste the codes."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:0
msgid "Install Google Tag Manager"
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:172
msgid ""
"The data is collected in the marketing tools used to monitor the website "
"(e.g., Google Analytics, Plausible, Facebook Pixel), not in Odoo."
msgstr ""

#: ../../content/applications/websites/website/reporting/analytics.rst:176
msgid ""
"`Setting up click triggers on Google "
"<https://support.google.com/tagmanager/answer/7679320?hl=en&ref_topic=7679108&sjid=17684856364781654579-EU>`_"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:3
msgid "Link tracker"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:5
msgid ""
"The link tracker allow you to create tracked links to measure your marketing"
" campaigns' effectiveness. They let you determine which channels bring you "
"the most visitors and make informed decisions."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:12
msgid ""
"The :guilabel:`Link Tracker` module is not installed by default. You need to"
" enable the :guilabel:`Email Marketing` option by going to "
":menuselection:`Website --> Configuration --> Settings.` Alternatively, you "
"can :doc:`install <../../../general/apps_modules>` the :guilabel:`Link "
"Tracker` module itself or one of the marketing apps."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:18
msgid "Create a traceable URL"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:20
msgid ""
"To create and manage tracked links, navigate to :menuselection:`Website --> "
"Site --> Link Tracker`. Fill in the following information and click "
":guilabel:`Get tracked link` to generate a tracking URL."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:23
msgid ""
":guilabel:`URL`: The URL which is the target of the campaign. It is "
"automatically populated with the URL from where you access the menu."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:26
msgid ""
":guilabel:`Campaign`: The specific campaign the link should be associated "
"with. This parameter is used to distinguish the different campaigns."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:29
msgid ""
":guilabel:`Medium`: The medium describes the category or method through "
"which the visitor arrives at your site, such as organic search, paid search,"
" social media ad, email, etc."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:32
msgid ""
":guilabel:`Source`: The source identifies the precise platform or website "
"that referred the visitor, such as a search engine, a newsletter, or a "
"website."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst-1
msgid "Create a link tracker URL"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:38
msgid ""
"The :guilabel:`Campaign`, :guilabel:`Medium`, and :guilabel:`Source` are "
"called :abbr:`UTM (Urchin Tracking Module)` parameters. They are "
"incorporated in the tracked URL."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:42
msgid "Website visibility"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:44
msgid ""
"You can use the :abbr:`UTM (Urchin Tracking Module)` parameters to hide or "
"show building blocks for specific audiences. To achieve this, click the "
":guilabel:`Edit` button on your website, select a building block, go to the "
":guilabel:`Customize` tab, scroll down to :guilabel:`Visibility`, and click "
":guilabel:`Conditionally`."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst-1
msgid ""
"Use the conditional visibility to display site elements to specific "
"audiences."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:52
msgid ""
"For each parameter available in the :ref:`Visibility "
"<building_blocks/visibility>` section, you can choose :guilabel:`Visible "
"for` or :guilabel:`Hidden for` and select the record you want from the "
"dropdown list."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:57
msgid "Tracked links overview"
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:59
msgid ""
"To get an overview of your tracked links, go to :menuselection:`Website --> "
"Site --> Link Tracker` and scroll down to :guilabel:`Your tracked links` "
"section."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst-1
msgid "Get an overview of all the links you track."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:66
msgid "Statistics"
msgstr "الإحصائيات"

#: ../../content/applications/websites/website/reporting/link_tracker.rst:68
msgid ""
"To measure the performance of tracked links, click the :guilabel:`Stats` "
"button."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst-1
msgid "View the statistics related to a specific tracked link."
msgstr ""

#: ../../content/applications/websites/website/reporting/link_tracker.rst:73
msgid ""
"Scroll down to the :guilabel:`Statistics` section to get an overview of the "
"number of clicks of your tracked links. You can display information for a "
"specific period by clicking the :guilabel:`All Time`, :guilabel:`Last "
"Month`, or :guilabel:`Last Week` options."
msgstr ""

#: ../../content/applications/websites/website/web_design.rst:5
msgid "Web design"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:5
msgid "Building blocks"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:7
msgid ""
"Building blocks let you design your website quickly by dragging and dropping"
" them onto your web pages. Four types of building blocks are available "
"depending on their use: :doc:`Structure <building_blocks/structure>`, "
":doc:`Features <building_blocks/features>`, :doc:`Dynamic Content "
"<building_blocks/dynamic_content>`, and :doc:`Inner Content "
"<building_blocks/inner_content>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:14
msgid ""
"`Odoo Tutorial: Design your first webpage "
"<https://www.odoo.com/slides/slide/design-your-website-images-and-"
"motion-6931?fullscreen=1>`_"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:19
msgid "Adding a building block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:21
msgid ""
"To add a building block to your website page, click :guilabel:`Edit`, select"
" the desired building block, and drag and drop it to your page. You can add "
"as many blocks as needed."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:24
msgid ""
"To edit the content of a building block, click on it and go to the "
":guilabel:`Customize` tab, where available features depend on the block you "
"selected."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:28
msgid "Color preset and background"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:30
msgid ""
"You can customize and apply color presets to building blocks. To proceed, "
"select a building block, go to the :guilabel:`Customize` tab, click the "
":guilabel:`Background` button, and select a :guilabel:`Preset`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:34
msgid ""
"When you modify a color preset, all elements using it are automatically "
"updated to match the new configuration."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:38
msgid ":doc:`Website themes <themes>`"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:41
msgid "Layout: grid and columns"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:43
msgid ""
"You can choose between two layout styles for most building blocks: "
":ref:`grid <building_blocks/grid>` or :ref:`columns (cols) "
"<building_blocks/cols>`. To change the default layout, go to the "
":guilabel:`Customize` tab. Under the :guilabel:`Banner` section, select "
":guilabel:`Grid` or :guilabel:`Cols` as the :guilabel:`Layout`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:51
msgid "Grid"
msgstr "الشبكة"

#: ../../content/applications/websites/website/web_design/building_blocks.rst:53
msgid ""
"The :guilabel:`Grid` layout allows you to reposition and resize elements, "
"such as images or text, by dragging and dropping them."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid ""
"When the grid layout is selected, choose an image and drag and drop it where"
" needed."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:60
msgid "Position images behind the text by using the above/below icons."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:0
msgid "Positioning an image behind text"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:68
msgid "Cols"
msgstr "الأعمدة "

#: ../../content/applications/websites/website/web_design/building_blocks.rst:70
msgid ""
"Choosing the :guilabel:`Cols` layout allows you to determine the number of "
"elements per line within the block. To do so, select the block to modify, "
"click the :guilabel:`Cols` :guilabel:`Layout`, and adjust the number."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:74
msgid ""
"By default, **on mobile devices**, one element is visible per line to ensure"
" that content remains easily readable and accessible on smaller screens. To "
"adjust the value, click the :icon:`fa-mobile` (:guilabel:`mobile icon`) at "
"the top of the website editor and adapt the number of columns."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid "Adjust the number of images per line on mobile view."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:82
#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid "Duplicating a building block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:84
msgid ""
"You can duplicate a building block by clicking on the duplicate icon. Once "
"duplicated, the new block appears on your website beneath the original one."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:91
msgid "Reordering a building block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:93
msgid ""
"To reorder a building block, select it and click the up arrow to move it "
"before the previous block or click the down arrow to move it after."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:96
msgid "You can also use the drag-and-drop icon to move a block manually."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid "Reordering building blocks"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:102
msgid "Saving a custom building block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:104
msgid ""
"You can save a customized building block and reuse it elsewhere. To do so, "
"select it, navigate to the :guilabel:`Customize` tab, and click the "
":icon:`fa-floppy-o` (:guilabel:`floppy disk`) icon to save it."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid "Saving a building block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:111
msgid ""
"Saved building blocks are available in the :guilabel:`Custom` section of the"
" :guilabel:`Blocks` tab. Click the :icon:`fa-pencil` (:guilabel:`pen`) icon "
"to edit their name."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid "Custom section with saved building blocks"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:120
msgid "Visibility"
msgstr "الظهور"

#: ../../content/applications/websites/website/web_design/building_blocks.rst:123
msgid "Visibility on desktop/mobile"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:125
msgid ""
"You can hide specific elements depending on the visitor's device. To do so, "
"select the element to hide, and in the :guilabel:`Customize` tab, scroll "
"down to :guilabel:`Visibility`, and click the :guilabel:`Show/Hide on "
"Mobile` or the :guilabel:`Show/Hide on Desktop` icon."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst-1
msgid ""
"Click the \"show/hide on mobile\" icons to show or hide some elements on "
"mobile."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:133
msgid ""
"Click the :icon:`fa-mobile` (:guilabel:`mobile`) icon at the top of the "
"configurator to preview how your website would look on a mobile device."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:0
msgid "Mobile phone preview icon"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:140
msgid "Conditional visibility"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:142
msgid ""
"You can also hide or show building blocks using other conditions. To do so, "
"select an element, go to :guilabel:`Visibility`, click :guilabel:`No "
"condition`, and select :guilabel:`Conditionally` instead. Then, configure "
"the condition(s) to apply by selecting :guilabel:`Visible for` or "
":guilabel:`Hidden for` and which :guilabel:`Records` will be impacted."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:148
msgid ":doc:`Link Tracker <../reporting/link_tracker>`"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:151
msgid "Invisible elements"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:153
msgid ""
"Depending on the visibility settings, some elements can become hidden from "
"your current view. To make a building block visible again, go to the "
":guilabel:`Invisible Elements` section at the bottom of the configurator and"
" select a building block."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:158
msgid "Mobile view customization"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:160
msgid ""
"You can customize building block elements for the mobile view without "
"impacting the desktop view. To do so, open the website editor, click the "
":icon:`fa-mobile` (:guilabel:`mobile`) icon at the top, and select the "
"building block element. Then, you can:"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:164
msgid ""
"reorder the elements by clicking the :icon:`fa-angle-left` :icon:`fa-angle-"
"right` (:guilabel:`left/right arrow`) icons;"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks.rst:166
msgid ""
"edit the :ref:`Cols <building_blocks/cols>` and :ref:`Visibility "
"<building_blocks/visibility>` features in the :guilabel:`Customize` tab of "
"the website editor."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:3
msgid "Dynamic content"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:5
msgid ""
"The :guilabel:`Dynamic Content` :doc:`building blocks <../building_blocks>`,"
" such as :ref:`Form <website/dynamic_content/form>`, :ref:`Products "
"<website/dynamic_content/products>`, :ref:`Embed Code "
"<website/dynamic_content/embed_code>`, or :doc:`Blog Posts <../../../blog>`,"
" help you create interactive and visually appealing layouts for your web "
":doc:`pages <../../pages>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:11
#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:12
#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:10
#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:12
msgid ""
"To add a building block, click :guilabel:`Edit`, select the desired building"
" block under the :guilabel:`Blocks` tab, and drag and drop it onto the page."
" To access its settings, click it and go to the :guilabel:`Customize` tab, "
"where the available options depend on the type of block selected."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:19
msgid "Form"
msgstr "الاستمارة "

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:21
msgid ""
"The :guilabel:`Form` block is used to collect information from website "
"visitors and create records in your database."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst-1
msgid "Example of a form block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:28
msgid "Action"
msgstr "إجراء"

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:30
msgid ""
"By default, submitting the form **sends you an email** containing what the "
"visitor entered. Depending on the apps installed on your database, new "
"actions that can automatically create records become available:"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:34
msgid ":guilabel:`Apply for a Job` (Recruitment)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:35
msgid ":guilabel:`Create a Customer` (eCommerce)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:36
msgid ":guilabel:`Create a Ticket` (Helpdesk)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:37
msgid ":guilabel:`Create an Opportunity` (CRM)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:38
msgid ":guilabel:`Subscribe to Newsletter` (Email Marketing)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:39
msgid ":guilabel:`Create a Task` (Project)"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:41
msgid ""
"Select another action with the :guilabel:`Action` field found under the "
":guilabel:`Customize` tab's :guilabel:`Form` section."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst-1
msgid "Editing a form to change its action"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:47
msgid ""
"By default, actions redirect visitors to a *thank you* page after submitting"
" the form. Use the :guilabel:`URL` field to change where they are "
"redirected. It is also possible to let visitors stay on the form's page by "
"selecting :guilabel:`Nothing` or :guilabel:`Show Message` under the "
":guilabel:`On Success` field."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:53
msgid "Fields"
msgstr "الحقول"

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:55
msgid ""
"To add a new field to the form, click the :guilabel:`+ Field` button found "
"next to the Customize tab's :guilabel:`Form` or :guilabel:`Field` section. "
"By default, new fields are *text* fields. To change the type, use the "
":guilabel:`Type` field and select an option under the :guilabel:`Custom "
"Field` heading."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:0
msgid "All types of form fields"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:65
msgid ""
"Some fields are visually similar, but the data entered must follow a "
"specific format."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:67
msgid ""
"It is also possible to select an :guilabel:`Existing Field` from a database "
"and use the data it contains. The fields available depend on the selected "
"action."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:71
msgid "Property fields added to the database can also be used."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:78
msgid ""
"The :guilabel:`Products` block is available after installing the eCommerce "
"app. It is used to display a selection of products sold on your website."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst-1
msgid "Example of a products block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:84
msgid ""
"By default, the block displays the :guilabel:`Newest Products`. To change "
"which products are shown, go to the :guilabel:`Customize` tab's "
":guilabel:`Products` section and select as :guilabel:`Filter` the "
":guilabel:`Recently Sold Products` or :guilabel:`Recently Viewed Products` "
"option."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:88
msgid ""
"In addition, it is possible to display products from a single category only "
"by selecting one with the :guilabel:`Category` field."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:94
msgid "Embed code"
msgstr "تضمين الكود "

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:96
msgid ""
"Embedding code allows you to integrate content from third-party services "
"into a page, such as videos from YouTube, maps from Google Maps, social "
"media posts from Instagram, etc."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst-1
msgid "Add the link to the embedded code you want to point to"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/dynamic_content.rst:102
msgid ""
"After adding the block to a page, click the :guilabel:`Edit` button found "
"under the :guilabel:`Customize` tab's :guilabel:`Embed Code` section and "
"enter the code, replacing the code used to show the block's instructions."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:3
msgid "Features"
msgstr "الخصائص"

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:5
msgid ""
"The :guilabel:`Features` :doc:`building blocks <../building_blocks>` allow "
"you to list multiple items next to each other."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:8
msgid ""
"The :ref:`Table of Content <features/table_of_content>` and the :ref:`Call "
"to Action <features/call_to_action>` blocks are presented below."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:20
msgid "Table of content"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:22
msgid ""
"The :guilabel:`Table of Content` block is used to list many different items "
"grouped under several headings. A clickable index is available to navigate "
"quickly between the different categories."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst-1
msgid "The default Table of Content block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:31
#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:29
msgid "Call to action"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:33
msgid ""
"The :guilabel:`Call to Action` block is used to prompt visitors to take a "
"specific action, such as signing up for a newsletter or contacting you."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst-1
msgid "The default Call to Action block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/features.rst:39
msgid ""
"To change the button's link, select it, go to the :guilabel:`Customize` "
"tab's :guilabel:`Inline Text` section and replace `/contactus` with another "
"URL."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:3
msgid "Inner content"
msgstr "المحتوى الداخلي "

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:5
msgid ""
"The :guilabel:`Inner content` :doc:`building blocks <../building_blocks>` "
"allow you to add elements such as videos, images, and :ref:`social media "
"buttons <inner_content/social_media>`, into pre-existing blocks."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:18
msgid "Social media"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:20
msgid ""
"The :guilabel:`Social Media` block inserts clickable buttons leading to your"
" social network's URL. By default, the buttons display the icons of seven "
"major social networks. You can click :guilabel:`Add New Social Network` to "
"create a new button and switch the buttons next to a URL to turn them on or "
"off."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst-1
msgid "The social media building block and its settings"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/inner_content.rst:30
msgid ""
"You cannot edit the default icons but can edit the ones you added by "
"clicking :guilabel:`Add New Social Network`. To do so, select the icon, then"
" click the :guilabel:`Replace` button found under the :guilabel:`Customize` "
"tab's :guilabel:`Icon` section, and either select one of the available icons"
" or click the :guilabel:`Images` tab and upload an image or add its URL."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:3
msgid "Structure"
msgstr "الهيكل"

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:5
msgid ""
"The website configurator provides a range of :guilabel:`Structure` "
":doc:`building blocks <../building_blocks>` to design your website's layout,"
" including headings, images, and text."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:8
msgid ""
"Below are presented two types of structure blocks: :ref:`Banner "
"<structure/banner>` and :ref:`Masonry <structure/masonry>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:20
msgid "Banner"
msgstr "العارضة "

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:22
msgid ""
"The :guilabel:`Banner` block combines a title, text, images, and a call to "
"action button, making it suitable for placement at the top of a website."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst-1
msgid "The default banner block"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:31
msgid ""
"The call to action button encourages visitors to take a specific action, for"
" example, consulting your shop, downloading a file, or making an "
"appointment."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst-1
msgid "Selecting the call to action button"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:37
msgid ""
"To change the button's link, select it and click the :guilabel:`Edit Link` "
"icon. Additional customization options are available in the "
":guilabel:`Inline Text` section."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst-1
msgid "Configuring the call to action button"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:46
msgid "Masonry"
msgstr "البناء "

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:48
msgid ""
"The :guilabel:`Masonry` block offers a range of templates that associate "
"image and text bricks. To change the default template, go to the "
":guilabel:`Customize` tab, click :guilabel:`Template` and select one."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst-1
msgid "Selecting a masonry building block template"
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:56
msgid ""
"The :guilabel:`Masonry` block allows you to add text on top of images. To do"
" so, go to the :guilabel:`Customize` tab, scroll to :guilabel:`Add "
"Elements`, and click :guilabel:`Text`."
msgstr ""

#: ../../content/applications/websites/website/web_design/building_blocks/structure.rst:0
msgid "Adding text on top of an image"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:3
msgid "Elements"
msgstr "العناصر "

#: ../../content/applications/websites/website/web_design/elements.rst:5
msgid ""
"Elements help structure and present content effectively. They range from "
"text-based components like :ref:`titles <website/elements/titles>`, "
":ref:`lists <website/elements/lists>` and :ref:`text highlights "
"<website/elements/text_highlights>` to interactive ones such as "
":ref:`buttons <website/elements/buttons>` and :ref:`links "
"<website/elements/links>`. Visual elements like :ref:`images "
"<website/elements/images>`, :ref:`icons <website/elements/icons>`, "
":ref:`videos <website/elements/videos>`, and :ref:`animations "
"<website/elements/animations>` can also be added to improve content "
"presentation and organization."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:13
msgid "To add or modify a website element:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:15
msgid "Navigate to the relevant website page and click on :guilabel:`Edit`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:16
msgid ""
"Click the section on the page where you want to add or modify an element."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:17
msgid "Make the necessary changes."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:18
msgid "Click on :guilabel:`Save`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:21
msgid ""
"The default styles for headings, buttons, links, and paragraph text, for "
"example, are defined in the :doc:`Theme tab <themes>` of the website editor."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst-1
msgid "Type / to add website elements."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:30
msgid "Titles"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:32
msgid ""
"Titles define headings and organize website content into different levels "
"for clarity and structure. To insert a title, type `/title`, choose the "
"heading style (:guilabel:`Heading 1`, :guilabel:`Heading 2`, or "
":guilabel:`Heading 3`), and type the text."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:37
msgid ""
"Alternatively, type the text, select it, and choose the appropriate style "
"from the :guilabel:`Inline Text` section in the :guilabel:`Customize` tab of"
" the website editor. Additional formatting options, such as fonts and "
"colors, are also available in this section."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:44
msgid "Buttons"
msgstr "الأزرار "

#: ../../content/applications/websites/website/web_design/elements.rst:46
msgid ""
"Buttons are interactive elements that allow to link to another page or to a "
"page anchor. To insert a button:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:49
msgid "Type `/button`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:50
msgid "Enter the button's label in the :guilabel:`Link Label` field."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:51
msgid ""
"Add the :guilabel:`URL or Email`. Type `/` to search for a page and `#` to "
"link to an anchor."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:52
msgid ""
"Set the :guilabel:`Style`, :guilabel:`Size`, and :guilabel:`Layout` to "
"define the button's appearance."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:54
msgid ""
"If needed, toggle the switch to open the linked page or anchor in a new tab."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:55
msgid "Click :guilabel:`Apply` to save changes."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:58
msgid ""
"To modify an existing button, click the button and edit the options in the "
":guilabel:`Inline text` section of the website editor."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:66
msgid "To insert an image:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:68
msgid "Type `/image`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:69
msgid ""
":doc:`Search the Unsplash database "
"</applications/general/integrations/unsplash>` or click :guilabel:`Upload an"
" image` to choose a file from your local images."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:71
msgid "Click :guilabel:`Add`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:72
msgid ""
"To customize the image, click on the image and edit the options in the "
":guilabel:`Image` section of the website editor. For example:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:75
msgid ":guilabel:`Replace` the image."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:76
msgid ""
"Define an `alt tag "
"<https://help.siteimprove.com/support/solutions/articles/80000448480-where-"
"are-alt-tags- used-and-why-are-they-important>`_ in the "
":guilabel:`Description` field."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:78
msgid ""
"Enter a title tag in the :guilabel:`Tooltip` field. This text will appear "
"when visitors hover their mouse over the image."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:80
msgid ""
"Add a :guilabel:`Shape`; some shapes also allow for color customization."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:81
msgid ""
"Adjust the image's :guilabel:`Width`, e.g., to improve performance. A "
"smaller size may be suggested if it is sufficient for display."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:83
msgid "Resize the image using the :guilabel:`Transform` tool."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:84
msgid ""
"Adjust the :guilabel:`Padding` to add space (in pixels) around the image."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:85
msgid "Etc."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:90
msgid "Videos"
msgstr "مقاطع الفيديو"

#: ../../content/applications/websites/website/web_design/elements.rst:92
msgid ""
"To add a video, type `/video`, insert the URL, and turn on the desired "
"options:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:94
msgid ""
":guilabel:`Autoplay`: to automatically play the video when the page is "
"accessed. The video is automatically muted by default."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:96
msgid ":guilabel:`Loop`: to play the video on a loop."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:97
msgid ":guilabel:`Hide player controls`"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:98
msgid ":guilabel:`Hide fullscreen button`"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:103
msgid "Icons"
msgstr "الأيقونات "

#: ../../content/applications/websites/website/web_design/elements.rst:105
msgid ""
"To insert an icon, type `/image`, go to the :guilabel:`Icons` tab, select an"
" icon, and click :guilabel:`Add`. To modify an icon, click on it and use the"
" :guilabel:`Icon` section of the website editor to customize options, such "
"as :guilabel:`Color`, :guilabel:`Size`, :ref:`Animations "
"<website/elements/animations>`, :guilabel:`Shape`, etc."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:113
msgid "Links"
msgstr "الروابط"

#: ../../content/applications/websites/website/web_design/elements.rst:115
msgid ""
"Links are used to connect different pages and resources, guiding visitors "
"and improving navigation. To add a link, type `/link`, then, in the pop-up "
"that opens, enter the link's :guilabel:`Label` and add the :guilabel:`URL or"
" Email`. Type `/` to search for a page and `#` to link to an anchor."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:120
msgid ""
"By default, the :guilabel:`Style` field is set to :guilabel:`Link`. Select a"
" different style to transform the link into a :ref:`button "
"<website/elements/buttons>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:126
msgid "Lists"
msgstr "القوائم"

#: ../../content/applications/websites/website/web_design/elements.rst:128
msgid ""
"Lists help organize content clearly, making information easier to read and "
"improving web pages' structures. Type `/list` and choose from three "
"different types of lists: :guilabel:`Bulleted lists`, :guilabel:`Numbered "
"lists`, or :guilabel:`Checklists`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:135
msgid "Text highlights"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:137
msgid ""
"Highlights can be added to titles and text using in the :guilabel:`Inline "
"Text` section of the website editor. To add a highlight:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:140
msgid "Select the text or title you want to highlight."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:141
msgid "In the website editor, click on :guilabel:`Highlight`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:142
msgid "Select the highlight style."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:143
msgid "Modify its :guilabel:`Color`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:144
msgid "Choose its :guilabel:`Thickness`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst-1
msgid "Highlight texts and titles"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:152
msgid "Animations"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:154
msgid ""
"Animations are used to add movement to :doc:`building blocks "
"<building_blocks>` and website elements such as images and text. Three types"
" of animation are available: :guilabel:`On Scroll`, :guilabel:`On "
"Appearance`, and :guilabel:`On Hover` (for images only)."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:158
msgid "To add an animation to a website element:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:160
msgid "Click on the element."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:161
msgid ""
"In the website editor, go to the relevant section for the element (e.g., "
":guilabel:`Button`, :guilabel:`Column`, :guilabel:`Inline Text`, etc.)."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:163
msgid "In the :guilabel:`Animation` field, select the desired animation type."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:164
msgid ""
"Customize the animation settings as needed. Available options vary based on "
"the selected animation type."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:168
msgid "Animations on scroll"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:170
msgid "For animations on scroll, it is possible to:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:172
msgid ""
"Choose :guilabel:`In` to add the animation when the element enters the "
"screen and :guilabel:`Out` to add it when it leaves the screen."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:174
msgid "Select an :guilabel:`Effect`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:175
#: ../../content/applications/websites/website/web_design/elements.rst:186
msgid "Choose the :guilabel:`Direction` of the effect."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:176
#: ../../content/applications/websites/website/web_design/elements.rst:189
msgid "Adapt the :guilabel:`Intensity` of the effect."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:177
msgid ""
"Define the :guilabel:`Scroll Zone`, where the first value represents the "
"percentage of the screen shown when the effect starts, and the second value "
"represents its percentage at the end."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:181
msgid "Animations on appearance"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:183
msgid "For animations on appearance, it is possible to:"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:185
msgid "Choose among different effects."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:187
msgid ""
"Pick a :guilabel:`Trigger` option to define when the animation occurs: "
"either the :guilabel:`First Time only` or :guilabel:`Every Time`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:190
msgid ""
"If you want the animation to be triggered after a number of seconds, define "
"this number in the :guilabel:`Start After` field."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:192
msgid "Choose a :guilabel:`Duration` for the animation."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:195
msgid "Animations on hover (for images only)"
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:197
msgid ""
"Animations :guilabel:`On hover` can be added to :ref:`images "
"<website/elements/images>`. You can choose the :guilabel:`Effect` of the "
"animation, as well as the :guilabel:`Color` and the :guilabel:`Stroke "
"Width`."
msgstr ""

#: ../../content/applications/websites/website/web_design/elements.rst:202
msgid ":doc:`Odoo HTML editor </applications/essentials/html_editor>`"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:3
msgid "General theme"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:5
msgid ""
"Odoo offers various options to shape your website’s theme, including its "
":ref:`colors <website/themes/colors>`, :ref:`fonts <website/themes/fonts>`, "
"and :ref:`layout <website/themes/page-layouts>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:9
msgid ""
"When setting up your website for the first time, you are prompted to select "
"a theme. Hover your mouse over the themes to see an extended preview of each"
" one. Click on a theme to select it."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:13
msgid ""
"If you leave without selecting a theme, your website is created using the "
"default one."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:14
msgid "You can :ref:`switch themes later <website/themes/switch>` if needed."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:16
msgid ""
"In the website builder, the :guilabel:`Theme` tab offers various options to "
"customize your website's general theme. To access it, click :guilabel:`Edit`"
" and go to the :guilabel:`Theme` tab."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:19
msgid ""
"Once you have made the desired changes, click on :guilabel:`Save` to confirm"
" and apply them to your website."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:25
msgid "Theme"
msgstr "السمة "

#: ../../content/applications/websites/website/web_design/themes.rst:27
msgid ""
"In the :guilabel:`Website` section, click on :guilabel:`Switch Theme` to "
"open the theme selector. Hover your mouse over the themes to see an extended"
" preview of each one. Click on a theme to apply it to your website."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:34
msgid "Colors"
msgstr "الألوان"

#: ../../content/applications/websites/website/web_design/themes.rst:36
msgid ""
"Odoo's website editor features two main types of colors: :ref:`theme colors "
"<website/themes/theme-colors>` and :ref:`status colors "
"<website/themes/status-colors>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:42
msgid "Theme colors"
msgstr "ألوان السمة"

#: ../../content/applications/websites/website/web_design/themes.rst:44
msgid ""
"Theme colors refer to the set of colors displayed across all pages of your "
"website. These are made of five colors: three main colors and two light and "
"dark colors."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:47
msgid ""
"To edit your website's colors, go to the :guilabel:`Colors` section in the "
"website editor, then:"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:49
msgid ""
"Click on the color dot you want to change, then select a :guilabel:`Solid` "
"color or click on :guilabel:`Custom` to pick a specific color tone manually "
"(or add its #HEX or RGBA code)."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:51
msgid ""
"Click on the paint palette icon and choose a color palette. As a result, all"
" color customizations are reset; click a color dot to change a specific "
"color."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:54
msgid ""
"Odoo automatically creates :guilabel:`Color Presets` for your chosen "
"palette. These are predefined color combinations applied to different "
"elements of your website to provide a structured and visually appealing "
"design. When you select a color palette, its presets define how those colors"
" are distributed across different elements from a building block, such as "
"buttons, backgrounds, and text. If you want to modify them, click on "
":guilabel:`Color Presets` and click on a preset to customize it further. "
"Each color preset contains colors for your building block’s background, "
"text, headings, links, primary buttons, and secondary buttons."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst-1
msgid "Color presets"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:66
msgid ""
"To apply a color preset to a building block on your site, select the "
"building block, go to the :guilabel:`Customize` tab, click the color dot "
"located next to :guilabel:`Background`, and choose a :guilabel:`Theme`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:71
msgid ""
"Changing a color preset automatically updates the colors of both the default"
" preset and the building blocks where the preset is used."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:77
msgid "Status colors"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:79
msgid ""
"Status colors are used to indicate the status of certain actions (e.g., "
":guilabel:`Success`, :guilabel:`Warning`, etc.). They're used in pop-up "
"messages that appear to provide feedback to users and website visitors. To "
"customize your website's :guilabel:`Status Colors`, scroll down to the "
":guilabel:`Advanced` section and click on the dots to change their color."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:0
msgid "Status color selection"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:0
msgid "Success pop-up"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:94
msgid "Page layout"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:96
msgid ""
"The :guilabel:`Page layout` option in the :guilabel:`Website` section allows"
" you to change the overall display and spacing of building blocks and "
"website elements on pages. Click the dropdown menu and select the desired "
"layout. Under :guilabel:`Page Layout`, customize your :guilabel:`Background`"
" by choosing an :guilabel:`Image`, using a selected image in a "
":guilabel:`Pattern`, or leaving it blank."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:105
msgid "Fonts"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:107
msgid ""
"Odoo allows you to customize the font family and size for specific elements "
"on your website, including paragraphs, headings, buttons, and input fields."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:110
msgid ""
":guilabel:`Font Family`: In the :guilabel:`Paragraph`, :guilabel:`Headings`,"
" and :guilabel:`Button` sections, select a font from the dropdown menu."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:113
msgid ""
":guilabel:`Font Size`: In the :guilabel:`Paragraph`, :guilabel:`Headings`, "
":guilabel:`Button`, and :guilabel:`Input Fields` sections, use the "
":guilabel:`Font Size` field to set a default size. Click the :icon:`fa-"
"caret-right` (arrow) icon to expand the section and define custom sizes "
"(e.g., based on the heading level, button size, etc.)."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:118
msgid ""
"Additionally, each element-specific section offers extra styling options, "
"such as :guilabel:`Line Height` and :guilabel:`Margins`, for further "
"customization."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:122
msgid "Custom fonts"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:124
msgid ""
"It is possible to use fonts on your website that are not offered by default "
"in Odoo. To add a custom font, click the dropdown menu related to the "
":guilabel:`Font Family` field and select :guilabel:`Add a Custom Font` at "
"the bottom of the dropdown menu. In the pop-up window:"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:128
msgid ""
"To add a Google font, click on :guilabel:`Select a Google Font` and click on"
" the desired font in the list. Toggle off the :guilabel:`Serve font from "
"Google servers` if your website is operated from a location where "
"regulations require compliance with laws such as, but not limited to, the "
"European Union's GDPR. This will ensure that the Google Font is stored on "
"your website's server instead of Google's."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:133
msgid ""
"To upload a custom font from your computer, click on :guilabel:`Choose "
"File`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:135
msgid "Once done, click on :guilabel:`Save and Reload`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:138
msgid "Button styles"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:140
msgid ""
"To customize the style of your website's primary and secondary buttons, "
"navigate to the :guilabel:`Button` section in the website editor and edit "
"the relevant options:"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:143
msgid ""
"Click the arrow next to the :guilabel:`Primary Style` or "
":guilabel:`Secondary Style` fields and select one of the available styles "
"for each type of button: :guilabel:`Fill`, :guilabel:`Outline`, or "
":guilabel:`Flat`. When selecting :guilabel:`Outline`, the :guilabel:`Border "
"Width` option"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:146
msgid ":ref:`Modify the fonts <website/themes/fonts>`."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:147
msgid ""
"Adjust the :guilabel:`Padding` to change the size of the spacing (in pixels)"
" around the buttons' labels."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:149
msgid ""
"Customize the buttons' border radius using the :guilabel:`Round Corners` "
"option."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:150
msgid ""
"Add an animation when a button is clicked in the :guilabel:`On Click Effect`"
" dropdown menu."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst-1
msgid "Primary and secondary buttons"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:156
msgid ""
"You can define custom :guilabel:`Small` and :guilabel:`Large` sizes for the "
"buttons' :guilabel:`Padding`, :guilabel:`Font Size`, and :guilabel:`Round "
"Corners`: Click on the :icon:`fa-caret-right` (arrow) icon and use the "
"related fields."
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:0
msgid "Button padding, font size and round corners settings"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:165
msgid "Link style"
msgstr ""

#: ../../content/applications/websites/website/web_design/themes.rst:167
msgid ""
"In the :guilabel:`Link` section, click on :guilabel:`Link Style` to choose "
"the appearance of links on your website. Select :guilabel:`No Underline`, "
":guilabel:`Underline On Hover`, or :guilabel:`Always Underline` in the "
"dropdown menu."
msgstr ""
