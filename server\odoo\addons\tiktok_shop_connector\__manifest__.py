# -*- coding: utf-8 -*-
{
    'name': 'TikTok Shop Connector',
    'version': '1.0.0',
    'category': 'Sales/Sales',
    'summary': 'Kết nối và đồng bộ đơn hàng với TikTok Shop',
    'description': """
TikTok Shop Connector
=====================

Module này cho phép:
* Kết nối với TikTok Shop API
* Đồng bộ đơn hàng từ TikTok Shop về Odoo
* Cập nhật trạng thái đơn hàng lên TikTok Shop
* Quản lý sản phẩm và inventory
* Tự động đồng bộ theo định kỳ

Tính năng chính:
- Cấu hình kết nối API TikTok Shop
- Import đơn hàng tự động
- Mapping sản phẩm giữa TikTok Shop và Odoo
- Cập nhật trạng thái đơn hàng
- <PERSON><PERSON>o cáo và thống kê
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'sale',
        'stock',
        'product',
        'account',
        'contacts',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/tiktok_shop_menus.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'external_dependencies': {
        'python': ['requests'],
    },
}
