# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* muk_web_enterprise_theme
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 13:40+0000\n"
"PO-Revision-Date: 2024-11-06 13:40+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Apps Active"
msgstr "Aktive Apps"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_company__background_image_dark
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_background_image_dark
msgid "Apps Menu Background Dark Image"
msgstr "dunkles Hintergrundbild Appsmenü"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_company__background_image_light
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_background_image_light
msgid "Apps Menu Background Light Image"
msgstr "helles Hintergrundbild Appsmenü"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Apps Text"
msgstr "Appstext"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "AppsBar"
msgstr "Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_active_dark
msgid "AppsBar Active Dark Color"
msgstr "aktive dunkle Farbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_active_light
msgid "AppsBar Active Light Color"
msgstr "aktive helle Farbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_background_dark
msgid "AppsBar Background Dark Color"
msgstr "dunkle Hintergrundfarbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_background_light
msgid "AppsBar Background Light Color"
msgstr "helle Hintergrundfarbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_text_dark
msgid "AppsBar Text Dark Color"
msgstr "dunkle Textfarbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_color_appbar_text_light
msgid "AppsBar Text Light Color"
msgstr "helle Textfarbe Appsleiste"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Backend Theme"
msgstr "Backend-Theme"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Background"
msgstr "Hintergrund"

#. module: muk_web_enterprise_theme
#: model:ir.model,name:muk_web_enterprise_theme.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: muk_web_enterprise_theme
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_company__favicon
#: model:ir.model.fields,field_description:muk_web_enterprise_theme.field_res_config_settings__theme_favicon
msgid "Company Favicon"
msgstr "Unternehmens Favicon"

#. module: muk_web_enterprise_theme
#: model:ir.model,name:muk_web_enterprise_theme.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Dark Mode Background Image"
msgstr "Hintergrundbild dunkler Modus"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Favicon"
msgstr "Favicon"

#. module: muk_web_enterprise_theme
#: model:ir.model,name:muk_web_enterprise_theme.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Light Mode Background Image"
msgstr "Hintergrundbild heller Modus"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Set the background image for the apps menu"
msgstr "Legen Sie das Hintergrundbild für das Appsmenü fest"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Set your own Logo for the appsbar"
msgstr "Legen Sie ihr eigenes Logo für die Appsleiste fest"

#. module: muk_web_enterprise_theme
#: model_terms:ir.ui.view,arch_db:muk_web_enterprise_theme.view_res_config_settings_colors_form
msgid "Set your own favicon"
msgstr "Legen Sie ihr eigenes Favicon fest"