==============
Enable Banking
==============

**Enable Banking** is a third-party provider aggregating banking information from bank accounts all
in one place. It offers non-intrusive connectivity to ASPSPs' official APIs across Europe without
storing data.

.. image:: enablebanking/enablebanking.png
   :alt:   Enable Banking logo

**Odoo** synchronizes directly with banks to get access to all bank transactions and automatically
import them into your database.

.. seealso::
   - :doc:`../bank_synchronization`
   - `Enable Banking website <https://enablebanking.com/>`_

Configuration
=============

Link bank accounts with Odoo
----------------------------

#. Start synchronization by clicking on :menuselection:`Accounting --> Configuration -->
   Add a Bank Account`;
#. Select your bank;
#. Make sure you give your consent to share your account information with Odoo by clicking
   :guilabel:`Continue authentication`;

   .. image:: enablebanking/enablebankingauth.png
      :alt: Enable Banking authentication page

#. Finally, you are redirected to your bank's login page.
