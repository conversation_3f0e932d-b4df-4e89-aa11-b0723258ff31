# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* muk_web_appsbar
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 14:43+0000\n"
"PO-Revision-Date: 2024-11-06 14:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: muk_web_appsbar
#: model:ir.model.fields,field_description:muk_web_appsbar.field_res_company__appbar_image
#: model:ir.model.fields,field_description:muk_web_appsbar.field_res_config_settings__appbar_image
msgid "Apps Menu Footer Image"
msgstr "Apps Menü Fusszeilen Bild"

#. module: muk_web_appsbar
#: model_terms:ir.ui.view,arch_db:muk_web_appsbar.view_res_config_settings_form
msgid "AppsBar"
msgstr "AppsLeiste"

#. module: muk_web_appsbar
#: model:ir.model,name:muk_web_appsbar.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: muk_web_appsbar
#: model:ir.model,name:muk_web_appsbar.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: muk_web_appsbar
#: model:ir.model,name:muk_web_appsbar.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: muk_web_appsbar
#: model:ir.model.fields.selection,name:muk_web_appsbar.selection__res_users__sidebar_type__invisible
msgid "Invisible"
msgstr "Unsichtbar"

#. module: muk_web_appsbar
#: model:ir.model.fields.selection,name:muk_web_appsbar.selection__res_users__sidebar_type__large
msgid "Large"
msgstr "Groß"

#. module: muk_web_appsbar
#. odoo-javascript
#: code:addons/muk_web_appsbar/static/src/webclient/appsbar/appsbar.xml:0
msgid "Logo"
msgstr "Logo"

#. module: muk_web_appsbar
#: model_terms:ir.ui.view,arch_db:muk_web_appsbar.view_res_config_settings_form
msgid "Set your own Logo for the appsbar"
msgstr "Legen Sie ihr eigenes Logo für die Appsleiste fest"

#. module: muk_web_appsbar
#: model:ir.model.fields,field_description:muk_web_appsbar.field_res_users__sidebar_type
msgid "Sidebar Type"
msgstr "Seitenleiste Typ"

#. module: muk_web_appsbar
#: model:ir.model.fields.selection,name:muk_web_appsbar.selection__res_users__sidebar_type__small
msgid "Small"
msgstr "Klein"

#. module: muk_web_appsbar
#: model:ir.model,name:muk_web_appsbar.model_res_users
msgid "User"
msgstr "Benutzer"