================
Change languages
================

You select the language of your database upon its creation. However, you can
:ref:`add <language/add>` and :ref:`install <language/install>` additional languages to allow users
to manage the database in another language or to
:doc:`translate <../../websites/website/configuration/translate>` your website.

.. _language/add:

Add languages
=============

To download additional languages:

- either click the profile icon in the upper-right corner, select :menuselection:`My profile`, and
  click the :icon:`fa-globe` (:guilabel:`globe`) icon next to the :guilabel:`Language` field;
- or go to the **Settings** app, and click :guilabel:`Add Languages` in the :guilabel:`Languages`
  section.

You can then select the languages you want from the dropdown menu and click :guilabel:`Add`.

.. seealso::
   :doc:`Translations <../../websites/website/configuration/translate>`

.. _language/install:

Change languages
================

To select their preferred language, users can click the profile icon in the upper-right corner, go
to :menuselection:`My profile`, and select a :guilabel:`Language` in the dropdown list.

Change another user's language
------------------------------

To change the database language for a user:

#. Go to the :guilabel:`Settings` app and click :guilabel:`Manage Users` in the :guilabel:`Users`
   section.

#. Click on the user whose language you want to change.

#. Go to the :guilabel:`Preferences` tab and select a previously
   :ref:`installed language <language/add>` from the :guilabel:`Language` dropdown menu.

.. note::
   Emails and documents will be sent to the user in the selected language.
