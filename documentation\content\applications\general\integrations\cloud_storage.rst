=============
Cloud Storage
=============

The cloud storage integration allows to store :doc:`chatter <../../productivity/discuss/chatter>`
and email attachments on the :ref:`Google Cloud <cloud-storage/google>` or :ref:`Microsoft Azure
<cloud-storage/microsoft>` platform instead of the database's server.

The module can be used to prevent large files from being uploaded to and downloaded from a
database's server or when a database requires more data storage.

.. note::
   - Files generated by Odoo (e.g., sales orders) and Documents/Sign app files are always stored on
     the database's server.
   - A database's storage limit depends on its hosting solution:

     - Odoo Online: 100 GB
     - Odoo.sh:

       - Shared hosting: 512 GB
       - Dedicated hosting: 4 TB

     - On-premise: limited by the infrastructure in place.

.. _cloud-storage/google:

Google Cloud
============

First, register and sign in to `Google Cloud <https://cloud.google.com>`_.

.. _cloud-storage/google/service:

Service account
---------------

#. Open the navigation sidebar on the Google Cloud console, then go to :menuselection:`IAM & Admin
   --> Service Accounts --> Create service account`.
#. Define a :guilabel:`Service account name`, click :guilabel:`Create and continue`, then
   :guilabel:`Done`.

   .. image:: cloud_storage/service-account.png
      :alt: Creating a Google Cloud service account

#. Note down the service account's :guilabel:`Email` as it will be used during the :ref:`cloud
   storage bucket configuration <cloud-storage/google/bucket>`.
#. Click the :icon:`fa-ellipsis-v` (:guilabel:`Actions`) button, then select :guilabel:`Manage
   keys`.

   .. image:: cloud_storage/manage-keys.png
      :alt: Accessing the "Manage keys" action

#. Go to :menuselection:`Add key --> Create new key`, select :guilabel:`JSON` as the :guilabel:`Key
   type`, and click :guilabel:`Create`. Store the downloaded JSON file containing the key securely.
   It will be used when :ref:`configuring Odoo <cloud-storage/google/odoo>`.

   .. image:: cloud_storage/create-key.png
      :alt: Creating a Google Cloud service account's JSON key

.. _cloud-storage/google/bucket:

Cloud storage bucket
--------------------

#. Open the navigation sidebar on the Google Cloud console, then go to :menuselection:`Cloud
   Storage --> Buckets --> Create`.
#. Enter a bucket name following the `bucket naming guidelines <https://cloud.google.com/storage/docs/buckets?_gl=1*h4hwrv*_ga******************************_ga_WH2QY8WWF5********************************************.#naming>`_
   and note it down as it will be used when :ref:`configuring Odoo <cloud-storage/google/odoo>`.
#. Configure the bucket as desired and click :guilabel:`Create` when done.

   .. image:: cloud_storage/create-bucket.png
      :alt: Creating a bucket

#. Click the :icon:`fa-ellipsis-v` (:guilabel:`More actions`) button, then select :guilabel:`Edit
   access`.

   .. image:: cloud_storage/bucket-actions.png
      :alt: Accessing the "Edit access" action of a Google Cloud storage bucket

#. Click :guilabel:`Add principal` and paste the service account's email in the :guilabel:`New
   principals` field.
#. Select :guilabel:`Storage Admin` as :guilabel:`Role` under the :guilabel:`Cloud Storage` section
   and click :guilabel:`Save`.

   .. image:: cloud_storage/bucket-access.png
      :alt: Adding a principal to a Google Cloud storage bucket

.. _cloud-storage/google/odoo:

Odoo configuration
------------------

#. :ref:`Install <general/install>` the :guilabel:`Cloud Storage Google` module.
#. Open the :guilabel:`Settings` app and select :guilabel:`Cloud Storage` in the navigation sidebar.
#. Select :guilabel:`Google Cloud Storage` as the :guilabel:`Cloud Storage Provider for new
   attachments`.
#. Enter the :guilabel:`Google Bucket Name` as :ref:`previously set <cloud-storage/google/bucket>`.
#. Click :guilabel:`Upload your file` next to :guilabel:`Google Service Account Key` and select the
   :ref:`downloaded JSON file <cloud-storage/google/service>`.
#. Set a :guilabel:`Minimum File Size (bytes)` for attachments to be stored on Google Cloud.

.. _cloud-storage/microsoft:

Microsoft Azure
===============

First, register and sign it to `Microsoft Azure <https://azure.microsoft.com>`_.

.. _cloud-storage/microsoft/app:

App registration
----------------

#. On the Microsoft Azure portal, search for the :guilabel:`App registrations` service and open it.
#. Click :guilabel:`New registration`, enter an application :guilabel:`Name`, select
   :guilabel:`Accounts in any organizational directory (Any Microsoft Entra ID tenant -
   Multitenant)` under :guilabel:`Supported account types`, and click :guilabel:`Register`.

   .. image:: cloud_storage/app-registration.png
      :alt: Registering a Microsoft Azure app

#. Note down the :guilabel:`Application (client) ID` and :guilabel:`Directory (tenant) ID` as they
   will be used when :ref:`configuring Odoo <cloud-storage/microsoft/odoo>`.
#. Click :guilabel:`Add a certificate or secret` next to :guilabel:`Client credentials`, click
   :guilabel:`New client secret`, then :guilabel:`Add`.

   .. important::
      For security reasons, leave the :guilabel:`Expires` field on `180 days (6 months)` or choose a
      shorter expiration interval. Before the secret expires, adding a new client secret and
      updating :ref:`Odoo’s configuration <cloud-storage/microsoft/odoo>` with the new value is
      necessary.

#. Copy the client secret's :guilabel:`Value` and store it securely. It will be used when
   :ref:`configuring Odoo <cloud-storage/microsoft/odoo>`.

   .. image:: cloud_storage/app-client-secret.png
      :alt: Adding a secret to a Microsoft Azure app

.. _cloud-storage/microsoft/storage:

Storage account
---------------

#. Search for the :guilabel:`Storage accounts` service, open it, and click :guilabel:`Create`.
#. Click :guilabel:`Create new` below the :guilabel:`Resource group` field, enter a
   :guilabel:`Name`, and click :guilabel:`OK`.
#. Enter a unique :guilabel:`Storage account name` and note it down as it will be used when
   :ref:`configuring Odoo <cloud-storage/microsoft/odoo>`.
#. Configure the storage account as desired, and, when done, click :guilabel:`Review + create`, then
   :guilabel:`Create`.

   .. image:: cloud_storage/storage-account.png
      :alt: Creating a Microsoft Azure storage account

.. _cloud-storage/microsoft/container:

Container
~~~~~~~~~

#. Open your storage account resource, for example by searching for its name, and select
   :guilabel:`Containers` under :guilabel:`Data storage` in the navigation sidebar.
#. Enter a :guilabel:`Name`, note it down as it will be used when :ref:`configuring Odoo
   <cloud-storage/microsoft/odoo>`, and click :guilabel:`Create`.

   .. image:: cloud_storage/storage-account-container.png
      :alt: Creating a Microsoft Azure storage container

.. _cloud-storage/microsoft/resource:

Resource sharing
~~~~~~~~~~~~~~~~

#. Select :guilabel:`Resource sharing (CORS)` under :guilabel:`Settings` on the storage account's
   navigation sidebar.
#. Create a first CORS blob service rule:

   - :guilabel:`Allowed origins`: `*`
   - :guilabel:`Allowed methods`: `GET`
   - :guilabel:`Allowed headers`: `Content-Type`
   - :guilabel:`Exposed headers`: `Content-Type`
   - :guilabel:`Max age`: `0`

#. Create a second CORS blob service rule and click :guilabel:`Save`:

   - :guilabel:`Allowed origins`: `*`
   - :guilabel:`Allowed methods`: `PUT`
   - :guilabel:`Allowed headers`: `content-type,x-ms-blob-type`
   - :guilabel:`Exposed headers`: `content-type,x-ms-blob-type`
   - :guilabel:`Max age`: `0`

   .. image:: cloud_storage/resource-sharing.png
      :alt: Creating Microsoft Azure storage account CORS rules

.. _cloud-storage/microsoft/role:

Role assignment
~~~~~~~~~~~~~~~

#. Select :guilabel:`Access control (IAM)` on the storage account's navigation sidebar, then click
   :guilabel:`Add` and select :guilabel:`Add role assignment`.
#. Search for :guilabel:`Storage Blobs Data Contributor` and click :guilabel:`Next`.

   .. note::
      To remove the unnecessary `delete` permission, create a :ref:`custom role
      <cloud-storage/microsoft/custom>` and search for the custom role's name instead.

#. Click :guilabel:`Select members`, enter the name of the :ref:`previously registered application
   <cloud-storage/microsoft/app>`, select it, and click :guilabel:`Select`.
#. Click :guilabel:`Review + assign` twice.

   .. image:: cloud_storage/storage-account-role.png
      :alt: Adding a member to a container

.. _cloud-storage/microsoft/custom:

Custom role
***********

.. note::
   This step is **optional**. However, removing the `delete` permission would prevent anyone
   managing to access the cloud storage's credentials from deleting files.

#. Open your subscription resource, for example by searching for its name, select :guilabel:`Access
   control (IAM)` in the navigation sidebar, click :guilabel:`Add` and select :guilabel:`Add custom
   role`.
#. Select the :guilabel:`JSON` tab and click :guilabel:`Edit`. Copy the code below, add your
   `subscription-id` under `assignableScopes` and change the `roleName` (`Custom role`) if desired,
   paste it, and click :guilabel:`Save`.

.. code-block:: json

   {
       "properties": {
           "roleName": "Custom role",
           "description": "",
           "assignableScopes": [
               "/subscriptions/subscription-id"
           ],
           "permissions": [
               {
                   "actions": ["Microsoft.Storage/storageAccounts/blobServices/generateUserDelegationKey/action"],
                   "notActions": [],
                   "dataActions": ["Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read", "Microsoft.Storage/storageAccounts/blobServices/containers/blobs/add/action"],
                   "notDataActions": []
               }
           ]
       }
   }

.. _cloud-storage/microsoft/odoo:

Odoo configuration
------------------

#. :ref:`Install <general/install>` the :guilabel:`Cloud Storage Azure` module.
#. Open the :guilabel:`Settings` app and select :guilabel:`Cloud Storage` in the navigation sidebar.
#. Select :guilabel:`Azure Cloud Azure` as the :guilabel:`Cloud Storage Provider for new
   attachments`.
#. Enter:

   - the :ref:`storage account name <cloud-storage/microsoft/storage>` in the :guilabel:`Azure
     Account Name` field;
   - the :ref:`container name <cloud-storage/microsoft/container>` in the :guilabel:`Azure Container
     Name` field;
   - the :ref:`directory (tenant) ID <cloud-storage/microsoft/app>` in the :guilabel:`Azure Tenant
     ID` field;
   - the :ref:`application (client) ID <cloud-storage/microsoft/app>` in the :guilabel:`Azure Client
     ID` field; and
   - the :ref:`client secret's value <cloud-storage/microsoft/app>` in the :guilabel:`Azure Client
     Secret` field.

#. Set a :guilabel:`Minimum File Size (bytes)` for attachments to be stored on Microsoft Azure.
