<?xml version="1.0" encoding="UTF-8"?>

<odoo>

	<record id="view_res_config_settings_appsbar_form" model="ir.ui.view">
	    <field name="name">res.config.settings.form</field>
	    <field name="model">res.config.settings</field>
	    <field name="inherit_id" ref="muk_web_appsbar.view_res_config_settings_form"/>
	    <field name="arch" type="xml">
	    	<xpath expr="//field[@name='appbar_image']/../.." position="attributes">
	    		<attribute name="invisible">1</attribute>
	    	</xpath>
	    </field>
	</record>

	<record id="view_res_config_settings_colors_form" model="ir.ui.view">
	    <field name="name">res.config.settings.form</field>
	    <field name="model">res.config.settings</field>
	    <field name="inherit_id" ref="muk_web_colors.view_res_config_settings_form"/>
	    <field name="arch" type="xml">
	    	<xpath expr="//block[@id='branding_settings']" position="attributes">
	    		<attribute name="string">Backend Theme</attribute>
	    	</xpath>
	    	<xpath expr="//field[@name='color_primary_light']/.." position="after">
             	<div class="w-50 row">
                    <label for="theme_color_appbar_text_light" string="Apps Text" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_text_light" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
             	<div class="w-50 row">
                    <label for="theme_color_appbar_active_light" string="Apps Active" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_active_light" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
             	<div class="w-50 row">
                    <label for="theme_color_appbar_background_light" string="Background" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_background_light" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
	    	</xpath>
	    	<xpath expr="//field[@name='color_primary_dark']/.." position="after">
             	<div class="w-50 row">
                    <label for="theme_color_appbar_text_dark" string="Apps Text" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_text_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
             	<div class="w-50 row">
                    <label for="theme_color_appbar_active_dark" string="Apps Active" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_active_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
             	<div class="w-50 row">
                    <label for="theme_color_appbar_background_dark" string="Background" class="d-block w-75 py-2"/>
                    <field name="theme_color_appbar_background_dark" class="d-block w-25 p-0 m-0" widget="color"/>
                </div>
	    	</xpath>
	    	<xpath expr="//block[@id='branding_settings']" position="inside">
    			<setting 
    				string="Light Mode Background Image" 
    				company_dependent="1" 
    				help="Set the background image for the apps menu"
    			>
    				<field name="theme_background_image_light" widget="image" class="ml-4 w-75 bg-transparent"/>
                </setting>
    			<setting 
    				string="Dark Mode Background Image" 
    				company_dependent="1" 
    				help="Set the background image for the apps menu"
    			>
    				<field name="theme_background_image_dark" widget="image" class="ml-4 w-75 bg-transparent"/>
                </setting>
	    		<setting string="AppsBar" company_dependent="1" help="Set your own Logo for the appsbar">
	    			<div class="w-50 row">
                    	<field name="appbar_image" widget="image" class="oe_avatar bg-transparent"/>
                    </div>
                </setting>
                <setting string="Favicon" company_dependent="1" help="Set your own favicon">
	    			<div class="w-50 row">
                    	<field name="theme_favicon" widget="image" class="oe_avatar bg-transparent"/>
                    </div>
                </setting>
	    	</xpath>
	    </field>
	</record>
	
</odoo>
