:show-content:
:hide-page-toc:

=============
Replenishment
=============

.. |MTO| replace:: :abbr:`MTO (Make to Oder)`
.. |PO| replace:: :abbr:`PO (Purchase Order)`
.. |MO| replace:: :abbr:`MO (Manufacturing Order)`
.. |POs| replace:: :abbr:`POs (Purchase Orders)`
.. |MOs| replace:: :abbr:`MOs (Manufacturing Orders)`
.. |SO| replace:: :abbr:`SO (Sales Order)`

In Odoo, stock can be replenished one of three ways: *reordering rules*, the *make to order* (MTO)
route, or using the *master production schedule* (MPS).

Each replenishment mechanism triggers the creation or suggestion of a purchase order (PO) or
manufacturing order (MO), with the best choice depending on the business process.

.. cards::

   .. card:: Reordering rules
      :target: replenishment/reordering_rules
      :tag: Recommended
      :large:

      Automatically suggest or generate POs or MOs when stock falls below a minimum level.

   .. card:: MTO
      :target: replenishment/mto
      :tag: Beginner-friendly

      Automatically generate POs or MOs when sales orders are confirmed.

   .. card:: MPS
      :target: ../../manufacturing/workflows/use_mps

      Manage long-term replenishment based on inputted sales forecasts, via a dashboard.

Replenishment strategies
========================

Replenishment report and reordering rules
-----------------------------------------

Reordering rules are rules that can be set up to maintain a minimum stock level. They are often
configured to support manufacturing or sales requirements. When a product's stock falls at or below
the minimum level, Odoo generates (or suggests) a purchase or manufacturing order to replenish stock
to the maximum level.

When using automatic reordering rules, Odoo generates a new order. When using manual, Odoo suggests
orders on the replenishment report. For detailed guidance, refer to the :doc:`replenishment report
<replenishment/report>` and :doc:`reordering rules <replenishment/reordering_rules>`.

Key points include:

- :ref:`Automatic reordering rules <inventory/warehouses_storage/auto-rr>`: Automatically create
  |POs| or |MOs| when stock falls below the minimum level. While this is convenient, it is less
  flexible.
- :ref:`Manual reordering rules <inventory/warehouses_storage/manual-rr>`: Generate suggestions in
  the replenishment report for user review, allowing adjustments and batch orders while meeting
  deadlines.
- :ref:`Just-in-time logic <inventory/warehouses_storage/just-in-time>`: A strategy to replenish
  only what is needed to prevent overstocking.

.. seealso::
   - :doc:`replenishment/reordering_rules`
   - :doc:`replenishment/report`

.. _inventory/management/products/strategies:

Make to order
-------------

An |MTO| strategy means that procurement or production is triggered only after a sales order has
been confirmed. This strategy is recommended when products are customizable, demand is
unpredictable, there is limited storage capacity, and when products are high in value and low in
demand. In such cases, it does not make sense to keep on-hand inventory.

Unlike products replenished using reordering rules, Odoo automatically links the sales order to the
|PO| or |MO| generated by the |MTO| route.

Another difference between reordering rules and |MTO| is, with |MTO|, Odoo generates a draft |PO| or
|MO| immediately after the |SO| is confirmed. With reordering rules, Odoo generates a draft |PO| or
|MO| when the product's forecasted stock falls below the set minimum quantity.

In addition, Odoo automatically adds quantities to the |PO| or |MO| as the forecast changes, so long
as the |PO| or |MO| is not confirmed.

The |MTO| route is the best replenishment strategy for products that are customized, and/or for
products that have no stock kept on-hand.

.. seealso::
   :doc:`replenishment/mto`

Master production schedule
--------------------------

The :abbr:`MPS (Master Production Schedule)` is a dashboard where products and their forecasted
quantities are entered. Based on confirmed manufacturing and purchase orders, the dashboard
recommends amounts to order or produce.

This a useful **manual** tool for keeping track of quantities. The :abbr:`MPS (Master Production
Schedule)` **should absolutely not** be used alongside reordering rules, as the automated workflow
disrupts its manual replenishment method.

.. seealso::
   :doc:`../../manufacturing/workflows/use_mps`

.. toctree::
   :titlesonly:

   replenishment/mto
   replenishment/reordering_rules
   replenishment/report
   replenishment/lead_times
   replenishment/resupply_warehouses
