<?xml version='1.0' encoding='UTF-8'?>
<Order xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Order-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <cbc:CustomizationID data-oe-model="ir.ui.view" data-oe-id="4828" data-oe-field="arch" data-oe-xpath="/t[1]/{urn:oasis:names:specification:ubl:schema:xsd:Order-2}Order[1]/{urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2}CustomizationID[1]">urn:fdc:peppol.eu:poacc:trns:order:3</cbc:CustomizationID>
  <cbc:ProfileID data-oe-model="ir.ui.view" data-oe-id="4828" data-oe-field="arch" data-oe-xpath="/t[1]/{urn:oasis:names:specification:ubl:schema:xsd:Order-2}Order[1]/{urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2}ProfileID[1]">urn:fdc:peppol.eu:poacc:bis:ordering:3</cbc:ProfileID>
  <cbc:ID>P00017</cbc:ID>
  <cbc:IssueDate>2025-04-03</cbc:IssueDate>
  <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
  <cac:BuyerCustomerParty>
    <cac:Party>
      <cac:PartyName>
        <cbc:Name>JEBAĆ TUSKA</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>1034 Wildwood Street</cbc:StreetName>
        <cbc:CityName>Millersburg</cbc:CityName>
        <cbc:PostalZone>44654</cbc:PostalZone>
        <cbc:CountrySubentity>Ohio</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode>US</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:Contact>
        <cbc:Name>JEBAĆ TUSKA</cbc:Name>
        <cbc:Telephone>******-555-5556</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:BuyerCustomerParty>
  <cac:SellerSupplierParty>
    <cac:Party>
      <cac:PartyName>
        <cbc:Name>YourCompany, Joel Willis</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>858 Lynn Street</cbc:StreetName>
        <cbc:CityName>Bayonne</cbc:CityName>
        <cbc:PostalZone>07002</cbc:PostalZone>
        <cbc:CountrySubentity>New Jersey</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode>US</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:Contact>
        <cbc:Name>Joel Willis</cbc:Name>
        <cbc:Telephone>(*************</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:SellerSupplierParty>
  <cac:Delivery>
    <cac:DeliveryParty>
      <cac:PartyName>
        <cbc:Name>JEBAĆ TUSKA</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>1034 Wildwood Street</cbc:StreetName>
        <cbc:CityName>Millersburg</cbc:CityName>
        <cbc:PostalZone>44654</cbc:PostalZone>
        <cbc:CountrySubentity>Ohio</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode>US</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:Contact>
        <cbc:Name>JEBAĆ TUSKA</cbc:Name>
        <cbc:Telephone>******-555-5556</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:DeliveryParty>
  </cac:Delivery>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="USD">7.5</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:AnticipatedMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="USD">50.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="USD">50.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="USD">57.50</cbc:TaxInclusiveAmount>
    <cbc:PayableAmount currencyID="USD">57.50</cbc:PayableAmount>
  </cac:AnticipatedMonetaryTotal>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>1</cbc:ID>
      <cbc:Quantity unitCode="C62">5.0</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="USD">50.0</cbc:LineExtensionAmount>
      <cac:Price>
        <cbc:PriceAmount currencyID="USD">10.0</cbc:PriceAmount>
        <cbc:BaseQuantity unitCode="C62">1</cbc:BaseQuantity>
      </cac:Price>
      <cac:Item>
        <cbc:Name>Office Lamp</cbc:Name>
        <cbc:Description>Office Lamp</cbc:Description>
        <cac:ClassifiedTaxCategory>
          <cbc:ID>S</cbc:ID>
          <cbc:Percent>15.0</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
      </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
</Order>
